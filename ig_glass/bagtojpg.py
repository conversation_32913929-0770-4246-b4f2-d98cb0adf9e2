#!/usr/bin/env python

import rosbag
import cv2
import os
import numpy as np
from cv_bridge import Cv<PERSON>ridge

def extract_images_from_bag(bag_file, output_dir, frame_rate):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    bag = rosbag.Bag(bag_file, 'r')
    bridge = CvBridge()
    frame_count = 0

    for topic, msg, t in bag.read_messages(topics=['/camera/image_color/compressed']):
        if frame_count % int(30 / frame_rate) == 0: 
            try:
                np_arr = np.frombuffer(msg.data, np.uint8)
                cv_image = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
                image_path = os.path.join(output_dir, f"frame_{frame_count:06}.jpg")
                cv2.imwrite(image_path, cv_image)
            except Exception as e:
                print(f"Failed to convert image: {e}")
        frame_count += 1

    bag.close()
    print(f"Extracted images to {output_dir}")

if __name__ == "__main__":
    bag_file = r'/home/<USER>/orb_slam3_ws/src/SLAM/hku.bag' 
    output_dir = r'/home/<USER>/orb_slam3_ws/src/SLAM/Task/HKU'
    frame_rate = 10  

    extract_images_from_bag(bag_file, output_dir, frame_rate)
