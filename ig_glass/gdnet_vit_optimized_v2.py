"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_vit_optimized_v2.py
 @Function: 基于Proteus-pytorch的优化ViT玻璃检测网络，目标突破90% IoU

关键优化：
1. 使用正确的Proteus DINOv2 ViT-B框架
2. patch_size=8，提供更精细的边缘分辨率 (420÷8=52.5→53)
3. 多层次特征提取，保留边缘细节
4. ViT-CNN协同设计，避免特征冲突
5. 专门的玻璃边缘感知机制
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os
from functools import partial

# 添加Proteus路径
proteus_path = '/home/<USER>/ws/IG_SLAM/Proteus-pytorch/pretrain'
if proteus_path not in sys.path:
    sys.path.append(proteus_path)

# 添加项目路径
project_path = '/home/<USER>/ws/IG_SLAM/ig_glass'
if project_path not in sys.path:
    sys.path.append(project_path)

try:
    # 使用正确的Proteus DINOv2框架
    from models_dinov2 import DinoVisionTransformer, Attention, Block
    PROTEUS_AVAILABLE = True
    print("✅ Proteus DINOv2框架加载成功")
except ImportError:
    print("⚠️ Proteus框架未找到，使用fallback实现")
    PROTEUS_AVAILABLE = False

from diff_crf import SimplifiedDiffCRF
from attention_scsa import SCSA


class ProteusViTBBackbone(nn.Module):
    """
    基于Proteus DINOv2的ViT-B骨干网络
    专门针对玻璃边缘检测优化
    """
    def __init__(self, pretrained_path=None, img_size=420, patch_size=8):
        super(ProteusViTBBackbone, self).__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size  # 使用更小的patch_size获得更精细的特征
        self.num_patches = (img_size // patch_size) ** 2  # 53x53 = 2809
        
        if PROTEUS_AVAILABLE:
            # 使用Proteus DINOv2 ViT-B，但调整patch_size
            self.vit_model = DinoVisionTransformer(
                patch_size=patch_size,  # 8x8 patches for fine-grained glass edges
                embed_dim=768,          # ViT-B embed dim
                depth=12,               # ViT-B depth
                num_heads=12,           # ViT-B heads
                mlp_ratio=4,
                block_fn=partial(Block, attn_class=Attention),  # 使用标准Attention
                num_register_tokens=0
            )
            
            self.embed_dim = 768
            
            # 加载Proteus预训练权重（如果可用）
            self._load_proteus_weights(pretrained_path)
        else:
            # Fallback实现
            self.vit_model = self._create_fallback_vit()
            self.embed_dim = 768
            
        # 多层特征提取 - 提取不同深度的特征
        self.feature_layers = [3, 6, 9, 12]  # 1/4, 1/2, 3/4, full depth
        
        # 特征适配器 - 将不同层的特征统一到256维
        self.layer_adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(self.embed_dim, 256, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            ) for _ in self.feature_layers
        ])
        
        print(f"✅ Proteus ViT-B创建成功:")
        print(f"   图像尺寸: {img_size}x{img_size}")
        print(f"   Patch尺寸: {patch_size}x{patch_size}")
        print(f"   特征图尺寸: {img_size//patch_size}x{img_size//patch_size}")
        print(f"   嵌入维度: {self.embed_dim}")
        
    def _create_fallback_vit(self):
        """创建fallback ViT实现"""
        class FallbackViT(nn.Module):
            def __init__(self, patch_size, embed_dim, img_size):
                super().__init__()
                self.patch_embed = nn.Conv2d(3, embed_dim, kernel_size=patch_size, stride=patch_size)
                num_patches = (img_size // patch_size) ** 2
                self.pos_embed = nn.Parameter(torch.randn(1, num_patches, embed_dim) * 0.02)
                self.blocks = nn.ModuleList([
                    nn.TransformerEncoderLayer(embed_dim, 12, embed_dim*4, dropout=0.1, batch_first=True)
                    for _ in range(12)
                ])
                self.norm = nn.LayerNorm(embed_dim)
                
            def forward_features(self, x):
                B, C, H, W = x.shape
                x = self.patch_embed(x)  # [B, 768, 53, 53]
                x = x.flatten(2).transpose(1, 2)  # [B, 2809, 768]
                x = x + self.pos_embed
                
                intermediate_features = []
                for i, block in enumerate(self.blocks):
                    x = block(x)
                    if i + 1 in [3, 6, 9, 12]:
                        intermediate_features.append(x)
                
                return {
                    "intermediate_features": intermediate_features,
                    "x_norm_patchtokens": intermediate_features[-1]
                }
        
        return FallbackViT(self.patch_size, self.embed_dim, self.img_size)
        
    def _load_proteus_weights(self, pretrained_path):
        """加载Proteus预训练权重"""
        if pretrained_path and os.path.exists(pretrained_path):
            print(f"📦 加载Proteus ViT-B权重: {pretrained_path}")
            try:
                checkpoint = torch.load(pretrained_path, map_location='cpu')
                if 'model' in checkpoint:
                    model_weights = checkpoint['model']
                    student_weights = {}
                    for key, value in model_weights.items():
                        if key.startswith('student.backbone.'):
                            new_key = key.replace('student.backbone.', '')
                            student_weights[new_key] = value
                    
                    # 尝试加载权重，忽略patch_embed和pos_embed的尺寸不匹配
                    missing_keys, unexpected_keys = self.vit_model.load_state_dict(student_weights, strict=False)
                    print(f"   权重加载完成: {len(student_weights)}个参数")
                    if missing_keys:
                        print(f"   缺失键: {len(missing_keys)}个（预期，因为patch_size不同）")
                else:
                    self.vit_model.load_state_dict(checkpoint, strict=False)
                    
                print(f"✅ Proteus权重加载成功")
            except Exception as e:
                print(f"⚠️ 权重加载失败: {e}，使用随机初始化")
        else:
            print(f"⚠️ 未找到预训练权重，使用随机初始化")
            
    def forward(self, x):
        """
        提取多层次ViT特征
        Args:
            x: [B, 3, 420, 420]
        Returns:
            multi_scale_features: list of [B, 256, 53, 53]
        """
        B, C, H, W = x.shape
        
        # 确保输入尺寸正确
        if H != self.img_size or W != self.img_size:
            x = F.interpolate(x, size=(self.img_size, self.img_size), mode='bilinear', align_corners=True)
        
        # 禁用xformers（如果使用Proteus）
        if PROTEUS_AVAILABLE:
            import models_dinov2
            models_dinov2.XFORMERS_AVAILABLE = False
        
        # ViT前向传播
        vit_output = self.vit_model.forward_features(x)
        
        # 获取中间特征
        if "intermediate_features" in vit_output:
            intermediate_features = vit_output["intermediate_features"]
        else:
            # 如果没有中间特征，使用最终特征重复
            final_features = vit_output["x_norm_patchtokens"]
            intermediate_features = [final_features] * len(self.feature_layers)
        
        # 转换为特征图格式并适配
        multi_scale_features = []
        patch_h = patch_w = self.img_size // self.patch_size  # 53
        
        for i, features in enumerate(intermediate_features):
            # [B, N, 768] -> [B, 768, 53, 53]
            feature_map = features.transpose(1, 2).reshape(B, self.embed_dim, patch_h, patch_w)
            
            # 特征适配到256维
            adapted_features = self.layer_adapters[i](feature_map)  # [B, 256, 53, 53]
            multi_scale_features.append(adapted_features)
        
        return multi_scale_features


class GlassEdgeAwareAttention(nn.Module):
    """
    玻璃边缘感知注意力模块
    专门针对玻璃的细微边缘特征设计
    """
    def __init__(self, in_channels=256):
        super(GlassEdgeAwareAttention, self).__init__()
        
        # 玻璃边缘检测核
        self.register_buffer('glass_edge_kernel_h', torch.tensor([
            [-1, -1, -1],
            [0, 0, 0],
            [1, 1, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('glass_edge_kernel_v', torch.tensor([
            [-1, 0, 1],
            [-1, 0, 1],
            [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        # 玻璃反射检测（对角线边缘）
        self.register_buffer('glass_reflection_kernel', torch.tensor([
            [1, 0, -1],
            [0, 0, 0],
            [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        # 边缘融合网络
        self.edge_fusion = nn.Sequential(
            nn.Conv2d(3, 32, 3, 1, 1),  # 3种边缘检测结果
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 1),
            nn.Sigmoid()
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//8, 1),
            nn.BatchNorm2d(in_channels//8),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//8, 1, 1),
            nn.Sigmoid()
        )
        
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, in_channels//16, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//16, in_channels, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        """
        Args:
            x: [B, 256, 53, 53]
        Returns:
            enhanced_x: [B, 256, 53, 53]
        """
        # 玻璃边缘检测
        gray = torch.mean(x, dim=1, keepdim=True)
        
        # 应用不同的边缘检测核
        edge_h = F.conv2d(gray, self.glass_edge_kernel_h, padding=1)
        edge_v = F.conv2d(gray, self.glass_edge_kernel_v, padding=1)
        edge_r = F.conv2d(gray, self.glass_reflection_kernel, padding=1)
        
        # 融合边缘信息
        edge_stack = torch.cat([edge_h, edge_v, edge_r], dim=1)
        edge_map = self.edge_fusion(edge_stack)  # [B, 1, 53, 53]
        
        # 空间注意力（增强边缘区域）
        spatial_att = self.spatial_attention(x)  # [B, 1, 53, 53]
        spatial_att = spatial_att * (1 + edge_map)  # 边缘区域获得更多关注
        
        # 通道注意力
        channel_att = self.channel_attention(x)  # [B, 256, 1, 1]
        
        # 应用注意力
        enhanced_x = x * spatial_att * channel_att
        
        return enhanced_x


class LightweightCNNBranch(nn.Module):
    """
    轻量级CNN分支 - 提供局部细节信息
    """
    def __init__(self, target_size=(53, 53)):
        super(LightweightCNNBranch, self).__init__()
        self.target_size = target_size
        
        # 轻量级CNN层
        self.layers = nn.ModuleList([
            # Layer 1: 420 -> 210
            nn.Sequential(
                nn.Conv2d(3, 32, 3, 2, 1),
                nn.BatchNorm2d(32),
                nn.ReLU(inplace=True)
            ),
            # Layer 2: 210 -> 105  
            nn.Sequential(
                nn.Conv2d(32, 64, 3, 2, 1),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True)
            ),
            # Layer 3: 105 -> 53
            nn.Sequential(
                nn.Conv2d(64, 128, 3, 2, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True)
            ),
            # Layer 4: 53 -> 53
            nn.Sequential(
                nn.Conv2d(128, 256, 3, 1, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            )
        ])
        
    def forward(self, x):
        """
        Args:
            x: [B, 3, 420, 420]
        Returns:
            features: [B, 256, 53, 53]
        """
        for layer in self.layers:
            x = layer(x)
        
        # 确保输出尺寸正确
        if x.shape[2:] != self.target_size:
            x = F.interpolate(x, size=self.target_size, mode='bilinear', align_corners=True)
            
        return x


class ViTCNNHybridFusion(nn.Module):
    """
    ViT-CNN混合融合模块
    """
    def __init__(self, vit_dim=256, cnn_dim=256):
        super(ViTCNNHybridFusion, self).__init__()
        
        # 特征对齐
        self.vit_proj = nn.Conv2d(vit_dim, 256, 1)
        self.cnn_proj = nn.Conv2d(cnn_dim, 256, 1)
        
        # 交叉注意力
        self.cross_attention = nn.Sequential(
            nn.Conv2d(512, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 1),
            nn.Sigmoid()
        )
        
        # SCSA融合
        self.scsa_fusion = SCSA(
            dim=512,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        # 输出投影
        self.output_proj = nn.Sequential(
            nn.Conv2d(512, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, vit_features, cnn_features):
        """
        Args:
            vit_features: list of [B, 256, 53, 53]
            cnn_features: [B, 256, 53, 53]
        Returns:
            fused_features: [B, 256, 53, 53]
        """
        # 使用最深层的ViT特征
        vit_feat = vit_features[-1]  # [B, 256, 53, 53]
        
        # 特征对齐
        vit_aligned = self.vit_proj(vit_feat)
        cnn_aligned = self.cnn_proj(cnn_features)
        
        # 交叉注意力
        combined = torch.cat([vit_aligned, cnn_aligned], dim=1)  # [B, 512, 53, 53]
        attention_weight = self.cross_attention(combined)
        
        # 加权融合
        weighted_vit = vit_aligned * attention_weight
        weighted_cnn = cnn_aligned * (1 - attention_weight)
        
        # SCSA注意力融合
        fused_input = torch.cat([weighted_vit, weighted_cnn], dim=1)
        attended_features = self.scsa_fusion(fused_input)
        
        # 输出投影
        fused_features = self.output_proj(attended_features)
        
        return fused_features


class GDNetViTOptimizedV2(nn.Module):
    """
    基于Proteus ViT-B的优化玻璃检测网络 - V2版本
    """
    def __init__(self, backbone_path=None, crf_iter=6, trainable_crf=True):
        super(GDNetViTOptimizedV2, self).__init__()
        
        # Proteus ViT-B骨干网络
        self.vit_backbone = ProteusViTBBackbone(
            pretrained_path=backbone_path,
            img_size=420,  # 420能被8整除
            patch_size=8   # 更小的patch_size获得更精细的特征
        )
        
        # 轻量级CNN分支
        self.cnn_branch = LightweightCNNBranch(target_size=(53, 53))
        
        # ViT-CNN混合融合
        self.hybrid_fusion = ViTCNNHybridFusion()
        
        # 玻璃边缘感知注意力
        self.edge_attention = GlassEdgeAwareAttention(in_channels=256)
        
        # 预测头
        self.predictor = nn.Sequential(
            nn.Conv2d(256, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 1, 3, 1, 1)
        )
        
        # 多尺度预测（使用不同层的ViT特征）
        self.multi_scale_predictors = nn.ModuleList([
            nn.Conv2d(256, 1, 3, 1, 1) for _ in range(4)
        ])
        
        # CRF后处理
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=8.0,
            gaussian_weight=4.0,
            bilateral_spatial_sigma=40.0,
            bilateral_color_sigma=3.0,
            gaussian_sigma=1.5,
            trainable=trainable_crf
        )
        
        # 自适应权重
        self.adaptive_weights = nn.Parameter(torch.tensor([0.4, 0.2, 0.2, 0.1, 0.1]), requires_grad=True)
        
        print(f"✅ Proteus ViT优化网络V2创建成功!")
        
    def forward(self, x):
        """
        Args:
            x: [B, 3, H, W]
        Returns:
            predictions dict
        """
        original_size = x.shape[2:]
        
        # 调整输入尺寸到420x420
        if x.shape[2:] != (420, 420):
            x_resized = F.interpolate(x, size=(420, 420), mode='bilinear', align_corners=True)
        else:
            x_resized = x
        
        # ViT多尺度特征提取
        vit_features = self.vit_backbone(x_resized)  # list of [B, 256, 53, 53]
        
        # CNN局部特征提取
        cnn_features = self.cnn_branch(x_resized)  # [B, 256, 53, 53]
        
        # ViT-CNN混合融合
        fused_features = self.hybrid_fusion(vit_features, cnn_features)  # [B, 256, 53, 53]
        
        # 玻璃边缘感知注意力
        enhanced_features = self.edge_attention(fused_features)  # [B, 256, 53, 53]
        
        # 主要预测
        main_pred = self.predictor(enhanced_features)  # [B, 1, 53, 53]
        
        # 多尺度预测
        multi_scale_preds = []
        for i, predictor in enumerate(self.multi_scale_predictors):
            scale_pred = predictor(vit_features[i])  # [B, 1, 53, 53]
            multi_scale_preds.append(scale_pred)
        
        # 上采样到原始尺寸
        main_pred = F.interpolate(main_pred, size=original_size, mode='bilinear', align_corners=True)
        multi_scale_preds = [
            F.interpolate(pred, size=original_size, mode='bilinear', align_corners=True)
            for pred in multi_scale_preds
        ]
        
        # 应用sigmoid
        main_pred_prob = torch.sigmoid(main_pred)
        multi_scale_probs = [torch.sigmoid(pred) for pred in multi_scale_preds]
        
        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = (weights[0] * main_pred_prob + 
                        sum(weights[i+1] * multi_scale_probs[i] for i in range(4)))
        
        ensemble_pred = torch.clamp(ensemble_pred, min=1e-7, max=1.0 - 1e-7)
        
        # CRF后处理
        bg_logits = torch.log(1 - ensemble_pred + 1e-7)
        fg_logits = torch.log(ensemble_pred + 1e-7)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
        
        # 归一化原始图像用于CRF
        normalized_img = self._normalize_image(x)
        
        # 应用CRF
        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - ensemble_pred, ensemble_pred], dim=1)
        
        return {
            'main_pred': main_pred_prob,
            'multi_scale_preds': multi_scale_probs,
            'ensemble_pred': ensemble_pred,
            'refined_pred': refined_predict
        }
    
    def _normalize_image(self, x):
        """图像归一化"""
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        return normalized_img


def create_vit_optimized_v2_model(backbone_path=None, crf_iter=6, trainable_crf=True):
    """创建优化ViT V2模型"""
    return GDNetViTOptimizedV2(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )


if __name__ == "__main__":
    # 测试模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = create_vit_optimized_v2_model(
        backbone_path='/path/to/proteus_vitb_backbone.pth'
    ).to(device)
    
    # 测试前向传播
    with torch.no_grad():
        x = torch.randn(2, 3, 416, 416).to(device)
        outputs = model(x)
        
        print("模型输出:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            elif isinstance(value, list):
                print(f"  {key}: {len(value)} items, shape: {value[0].shape}")
        
        # 计算参数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"\n模型参数统计:")
        print(f"  总参数量: {total_params:,} ({total_params/1e6:.1f}M)")
        print(f"  可训练参数: {trainable_params:,} ({trainable_params/1e6:.1f}M)")
        
        print(f"\n✅ Proteus ViT优化模型V2测试成功!")