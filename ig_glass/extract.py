import os
import cv2
import rosbag
from cv_bridge import CvBridge
from tqdm import tqdm

# 配置参数
bag_file = "/home/<USER>/test/IR12_3/IR12/IR12_3.bag"  # ROS bag 文件名
image_topic = "/stereo_inertial_publisher/color/image"  # 目标topic
output_dir = "/home/<USER>/test/IR12_3/IR12/png"  # 输出文件夹
timestamps_file = "timestamps.txt"  # 时间戳文件名

# 创建输出文件夹
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 初始化CvBridge
bridge = CvBridge()

# 打开ROS bag文件
with rosbag.Bag(bag_file, "r") as bag:
    messages = bag.read_messages(topics=[image_topic])
    timestamps = []

    # 遍历每条消息
    for topic, msg, t in tqdm(messages, desc="Extracting images"):
        # 获取时间戳
        timestamp = "%.2f" % (t.to_sec())
        timestamps.append(timestamp)

        # 将ROS图像消息转换为OpenCV格式
        cv_image = bridge.imgmsg_to_cv2(msg, desired_encoding="bgr8")

        # 保存图像为.png文件
        image_filename = os.path.join(output_dir, f"{timestamp}.png")
        cv2.imwrite(image_filename, cv_image)

# 保存时间戳到timestamps.txt
with open(timestamps_file, "w") as f:
    for timestamp in timestamps:
        f.write(f"{timestamp}\n")

print(f"提取完成！图像保存在: {output_dir}, 时间戳保存在: {timestamps_file}")
