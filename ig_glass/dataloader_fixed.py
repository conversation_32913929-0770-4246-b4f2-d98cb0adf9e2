from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

import sys
import cv2
import numpy as np
import glob

import torch
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms


def pad_resize_image(inp_img, out_img=None, target_size=None):
    """
    Function to pad and resize images to a given size.
    out_img is None only during inference. During training and testing
    out_img is NOT None.
    :param inp_img: A H x W x C input image.
    :param out_img: A H x W input image of mask.
    :param target_size: The size of the final images.
    :return: Re-sized inp_img and out_img
    """
    h, w, c = inp_img.shape
    size = max(h, w)

    padding_h = (size - h) // 2
    padding_w = (size - w) // 2

    if out_img is None:
        # For inference
        temp_x = cv2.copyMakeBorder(inp_img, top=padding_h, bottom=padding_h, left=padding_w, right=padding_w,
                                    borderType=cv2.BORDER_CONSTANT, value=[0, 0, 0])
        if target_size is not None:
            temp_x = cv2.resize(temp_x, (target_size, target_size), interpolation=cv2.INTER_AREA)
        return temp_x
    else:
        # For training and testing
        temp_x = cv2.copyMakeBorder(inp_img, top=padding_h, bottom=padding_h, left=padding_w, right=padding_w,
                                    borderType=cv2.BORDER_CONSTANT, value=[0, 0, 0])
        temp_y = cv2.copyMakeBorder(out_img, top=padding_h, bottom=padding_h, left=padding_w, right=padding_w,
                                    borderType=cv2.BORDER_CONSTANT, value=[0, 0, 0])
        # print(inp_img.shape, temp_x.shape, out_img.shape, temp_y.shape)

        if target_size is not None:
            temp_x = cv2.resize(temp_x, (target_size, target_size), interpolation=cv2.INTER_AREA)
            temp_y = cv2.resize(temp_y, (target_size, target_size), interpolation=cv2.INTER_AREA)
        return temp_x, temp_y


def random_crop_flip(inp_img, out_img):
    """
    Randomly crop and flip the input and output images.
    :param inp_img: input image of size H x W x C
    :param out_img: output image of size H x W
    :return: randomly cropped and flipped images
    """
    # Random Crop
    h, w, c = inp_img.shape
    th, tw = int(h * 0.9), int(w * 0.9)
    if w == tw and h == th:
        i, j = 0, 0
    else:
        i = np.random.randint(0, h - th)
        j = np.random.randint(0, w - tw)

    inp_img = inp_img[i:i + th, j:j + tw, :]
    out_img = out_img[i:i + th, j:j + tw]

    # Random Flip
    if np.random.random() > 0.5:
        inp_img = np.fliplr(inp_img)
        out_img = np.fliplr(out_img)

    return inp_img, out_img


def random_rotate(inp_img, out_img, max_angle=25):
    """
    Randomly rotate the input and output images.
    :param inp_img: input image of size H x W x C
    :param out_img: output image of size H x W
    :param max_angle: maximum rotation angle
    :return: randomly rotated images
    """
    angle = np.random.randint(-max_angle, max_angle)
    h, w = inp_img.shape[:2]
    center = (w // 2, h // 2)

    # Rotation matrix
    M = cv2.getRotationMatrix2D(center, angle, 1.0)

    # Apply rotation
    inp_img = cv2.warpAffine(inp_img, M, (w, h), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT)
    out_img = cv2.warpAffine(out_img, M, (w, h), flags=cv2.INTER_NEAREST, borderMode=cv2.BORDER_REFLECT)

    return inp_img, out_img


def random_rotate_lossy(inp_img, out_img, max_angle=25):
    """
    Randomly rotate the input and output images. (Lossy version)
    :param inp_img: input image of size H x W x C
    :param out_img: output image of size H x W
    :param max_angle: maximum rotation angle
    :return: randomly rotated images
    """
    angle = np.random.randint(-max_angle, max_angle)
    h, w = inp_img.shape[:2]
    center = (w // 2, h // 2)

    # Rotation matrix
    M = cv2.getRotationMatrix2D(center, angle, 1.0)

    # Apply rotation
    inp_img = cv2.warpAffine(inp_img, M, (w, h))
    out_img = cv2.warpAffine(out_img, M, (w, h))

    return inp_img, out_img


def random_brightness(inp_img):
    """
    Randomly adjust the brightness of the input image.
    :param inp_img: input image of size H x W x C
    :return: brightness adjusted image
    """
    alpha = np.random.uniform(0.8, 1.2)
    inp_img = inp_img * alpha
    inp_img = np.clip(inp_img, 0, 255)
    return inp_img


class SODLoader(Dataset):
    """
    DataLoader for DUTS dataset (for training and testing).
    """
    def __init__(self, mode='train', augment_data=False, target_size=256):
        if mode == 'train':
            self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_msd/image'
            self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_msd/mask'
        elif mode == 'train_gdd':
            self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd/image'
            self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd/mask'
        elif mode == 'test_gdd':
            self.inp_path = '/home/<USER>/Documents/ig_slam_maskdata/test_GDD/image'
            self.out_path = '/home/<USER>/Documents/ig_slam_maskdata/test_GDD/mask'
        elif mode == 'test':
            self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_msd/image'
            self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_msd/mask'
        else:
            print("mode should be either 'train' or 'test'.")
            sys.exit(0)

        self.augment_data = augment_data
        self.target_size = target_size
        self.normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                              std=[0.229, 0.224, 0.225])

        self.transform = transforms.Compose([
            transforms.ToTensor(),
            self.normalize,
        ])  # Not used

        self.inp_files = sorted(glob.glob(self.inp_path + '/*'))
        self.out_files = sorted(glob.glob(self.out_path + '/*'))

    def __getitem__(self, idx):
        inp_img = cv2.imread(self.inp_files[idx])
        inp_img = cv2.cvtColor(inp_img, cv2.COLOR_BGR2RGB)
        inp_img = inp_img.astype('float32')

        mask_img = cv2.imread(self.out_files[idx], 0)
        mask_img = mask_img.astype('float32')
        
        # 安全的归一化，避免除零和超出范围
        max_val = np.max(mask_img)
        if max_val > 1.0:  # 如果是0-255范围，先归一化到0-1
            mask_img = mask_img / 255.0
        elif max_val > 0:  # 如果已经在0-1范围但不是标准化的
            mask_img = mask_img / max_val
        # 确保值在[0,1]范围内
        mask_img = np.clip(mask_img, 0.0, 1.0)

        if self.augment_data:
            inp_img, mask_img = random_crop_flip(inp_img, mask_img)
            inp_img, mask_img = random_rotate(inp_img, mask_img)
            inp_img = random_brightness(inp_img)

        # Pad images to target size
        inp_img, mask_img = pad_resize_image(inp_img, mask_img, self.target_size)
        inp_img /= 255.0
        inp_img = np.transpose(inp_img, axes=(2, 0, 1))
        inp_img = torch.from_numpy(inp_img).float()
        inp_img = self.normalize(inp_img)

        mask_img = np.expand_dims(mask_img, axis=0)

        return inp_img, torch.from_numpy(mask_img).float()

    def __len__(self):
        return len(self.inp_files) 