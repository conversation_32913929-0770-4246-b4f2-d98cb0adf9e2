"""
 @Time    : 2024
 <AUTHOR> <PERSON><PERSON>

 @Project : IG_SLAM
 @File    : gdnet_proteus_vits.py
 @Function: Proteus ViT-S融合SCSA和Glass Physics优势模块的玻璃检测网络

融合优势：
- Proteus ViT-S Backbone: 33.4M参数，强大泛化能力
- SCSA LightLCFI: 轻量化大上下文特征集成
- SCSA注意力机制: 空间-通道联合注意力（87.9% IoU核心）
- Glass Physics EdgeAnalyzer: 专业边缘物理分析
- 优化的CRF后处理: SCSA验证的最优参数
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import sys
import os

# 添加Proteus路径到系统路径
proteus_path = '/home/<USER>/ws/IG_SLAM/Proteus-pytorch/pretrain'
if proteus_path not in sys.path:
    sys.path.append(proteus_path)

# 添加ig_glass路径
ig_glass_path = '/home/<USER>/ws/IG_SLAM/ig_glass'
if ig_glass_path not in sys.path:
    sys.path.append(ig_glass_path)

# 使用DINOv2版本的ViT模型（不是CLIP版本）
from models_dinov2 import vit_small

from diff_crf import SimplifiedDiffCRF
from attention_scsa import SCSA

###################################################################
# ############## SCSA LightLCFI 模块 ############################
###################################################################
class LightLCFI(nn.Module):
    """
    从SCSA模型移植的轻量化大上下文特征集成模块
    使用深度可分离卷积，87.9% IoU的核心组件
    """
    def __init__(self, input_channels, dr1=1, dr2=2, dr3=3, dr4=4):
        super(LightLCFI, self).__init__()
        self.input_channels = input_channels
        self.channels_single = int(input_channels / 4)
        self.channels_double = int(input_channels / 2)
        self.dr1 = dr1
        self.dr2 = dr2
        self.dr3 = dr3
        self.dr4 = dr4
        self.padding1 = 1 * dr1
        self.padding2 = 2 * dr2
        self.padding3 = 3 * dr3
        self.padding4 = 4 * dr4

        # Channel reduction with 1x1 convolutions
        self.p1_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p1
        self.p1_d1 = nn.Sequential(
            nn.Conv2d(self.channels_single, self.channels_single, (3, 1), 1, padding=(self.padding1, 0),
                      dilation=(self.dr1, 1), groups=self.channels_single),
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_d2 = nn.Sequential(
            nn.Conv2d(self.channels_single, self.channels_single, (1, 3), 1, padding=(0, self.padding1),
                      dilation=(1, self.dr1), groups=self.channels_single),
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p2
        self.p2_d1 = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_double, (5, 1), 1, padding=(self.padding2, 0),
                      dilation=(self.dr2, 1), groups=self.channels_double),
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_d2 = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_double, (1, 5), 1, padding=(0, self.padding2),
                      dilation=(1, self.dr2), groups=self.channels_double),
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p3
        self.p3_d1 = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_double, (7, 1), 1, padding=(self.padding3, 0),
                      dilation=(self.dr3, 1), groups=self.channels_double),
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_d2 = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_double, (1, 7), 1, padding=(0, self.padding3),
                      dilation=(1, self.dr3), groups=self.channels_double),
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p4
        self.p4_d1 = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_double, (9, 1), 1, padding=(self.padding4, 0),
                      dilation=(self.dr4, 1), groups=self.channels_double),
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_d2 = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_double, (1, 9), 1, padding=(0, self.padding4),
                      dilation=(1, self.dr4), groups=self.channels_double),
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # 使用SCSA注意力机制 - 87.9% IoU的核心
        self.scsa = SCSA(
            dim=self.input_channels,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )

        # Final channel reduction
        self.channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single),
            nn.ReLU())

    def forward(self, x):
        p1_input = self.p1_channel_reduction(x)
        p1 = self.p1_fusion(torch.cat((self.p1_d1(p1_input), self.p1_d2(p1_input)), 1))

        p2_input = torch.cat((self.p2_channel_reduction(x), p1), 1)
        p2 = self.p2_fusion(torch.cat((self.p2_d1(p2_input), self.p2_d2(p2_input)), 1))

        p3_input = torch.cat((self.p3_channel_reduction(x), p2), 1)
        p3 = self.p3_fusion(torch.cat((self.p3_d1(p3_input), self.p3_d2(p3_input)), 1))

        p4_input = torch.cat((self.p4_channel_reduction(x), p3), 1)
        p4 = self.p4_fusion(torch.cat((self.p4_d1(p4_input), self.p4_d2(p4_input)), 1))

        # 使用SCSA处理特征 - 87.9% IoU的关键
        combined_features = torch.cat((p1, p2, p3, p4), 1)
        attended_features = self.scsa(combined_features)
        channel_reduction = self.channel_reduction(attended_features)

        return channel_reduction


###################################################################
# ############## Glass Physics 边缘分析模块 #####################
###################################################################
class PhysicsEdgeAnalyzer(nn.Module):
    """
    从Glass Physics移植的专业边缘物理分析模块
    结合拉普拉斯、Sobel、HSV引导的多层次边缘检测
    """
    def __init__(self, in_channels):
        super(PhysicsEdgeAnalyzer, self).__init__()
        
        # 多尺度边缘检测器
        self.edge_detectors = nn.ModuleList([
            self._make_edge_detector(in_channels, kernel_size=3),
            self._make_edge_detector(in_channels, kernel_size=5),
            self._make_edge_detector(in_channels, kernel_size=7)
        ])
        
        # 专业边缘检测核 - Glass Physics验证的最优配置
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1.,  8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))
        
        self.register_buffer('sobel_x', torch.tensor([
            [-1, 0, 1], 
            [-2, 0, 2], 
            [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y', torch.tensor([
            [-1, -2, -1], 
            [ 0,  0,  0], 
            [ 1,  2,  1]
        ]).float().view(1, 1, 3, 3))
        
        # HSV引导的边缘增强
        self.hsv_edge_enhancer = nn.Sequential(
            nn.Conv2d(3, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, 3, 1, 1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 1),
            nn.Sigmoid()
        )
        
        # 边缘融合模块
        self.edge_fusion = nn.Sequential(
            nn.Conv2d(4, 32, 3, 1, 1),  # 融合4种边缘检测结果
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, 1),
            nn.Sigmoid()
        )
        
        # 边缘锐度分析
        self.sharpness_analyzer = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, 1, 1),
            nn.Sigmoid()
        )
        
    def _make_edge_detector(self, in_channels, kernel_size):
        padding = kernel_size // 2
        return nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, kernel_size, 1, padding),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, in_channels//8, 1)
        )
    
    def _apply_edge_kernels(self, x):
        """应用专业边缘检测核"""
        if x.size(1) > 1:
            gray = torch.mean(x, dim=1, keepdim=True)
        else:
            gray = x
        
        # 拉普拉斯边缘检测
        laplacian_edges = F.relu(torch.tanh(F.conv2d(gray, self.laplacian_kernel, padding=1)))
        
        # Sobel边缘检测
        sobel_x_edges = F.conv2d(gray, self.sobel_x, padding=1)
        sobel_y_edges = F.conv2d(gray, self.sobel_y, padding=1)
        sobel_edges = torch.sqrt(sobel_x_edges**2 + sobel_y_edges**2)
        sobel_edges = torch.sigmoid(sobel_edges)
        
        return laplacian_edges, sobel_edges
    
    def _hsv_guided_edge_detection(self, rgb_input, feature_edges):
        """HSV引导的边缘检测"""
        # HSV边缘增强
        hsv_edge_weight = self.hsv_edge_enhancer(rgb_input)
        
        # 将HSV权重调整到特征尺寸
        if hsv_edge_weight.shape[2:] != feature_edges.shape[2:]:
            hsv_edge_weight = F.interpolate(hsv_edge_weight, size=feature_edges.shape[2:], 
                                          mode='bilinear', align_corners=True)
        
        # 结合特征边缘和HSV信息
        enhanced_edges = feature_edges * hsv_edge_weight
        
        return enhanced_edges
        
    def forward(self, x, rgb_input=None):
        # 多尺度边缘特征
        edge_features = []
        for detector in self.edge_detectors:
            edge_feat = detector(x)
            edge_features.append(edge_feat)
        multi_scale_edges = torch.cat(edge_features, dim=1)
        
        # 专业边缘检测
        laplacian_edges, sobel_edges = self._apply_edge_kernels(x)
        
        # HSV引导的边缘检测（如果提供RGB输入）
        if rgb_input is not None:
            hsv_enhanced_edges = self._hsv_guided_edge_detection(rgb_input, sobel_edges)
        else:
            hsv_enhanced_edges = sobel_edges
        
        # 融合多种边缘检测结果
        edge_stack = torch.cat([laplacian_edges, sobel_edges, hsv_enhanced_edges, 
                               torch.mean(multi_scale_edges, dim=1, keepdim=True)], dim=1)
        fused_edges = self.edge_fusion(edge_stack)
        
        # 边缘锐度分析
        sharpness_map = self.sharpness_analyzer(x)
        
        return fused_edges, sharpness_map


###################################################################
# #################### Proteus ViT-S 特征提取器 ################
###################################################################
class ProteusViTSFeatureExtractor(nn.Module):
    """
    基于Proteus ViT-S的特征提取器，直接使用原版实现
    """
    def __init__(self, pretrained_path=None):
        super(ProteusViTSFeatureExtractor, self).__init__()
        
        # 临时禁用xformers，强制使用普通attention
        import sys
        if 'models_dinov2' in sys.modules:
            sys.modules['models_dinov2'].XFORMERS_AVAILABLE = False
        
        self.vit_backbone = vit_small(patch_size=14)
        
        # 如果有预训练权重，加载它们
        if pretrained_path and os.path.exists(pretrained_path):
            print(f"📦 加载DINOv2 Proteus权重: {pretrained_path}")
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            self.vit_backbone.load_state_dict(checkpoint, strict=False)
        
        # ViT-S配置（DINOv2版本）
        self.embed_dim = 384
        self.patch_size = 14  
        self.num_layers = 12
        self.num_heads = 6
        
        print(f"✅ 使用DINOv2 Proteus ViT-S:")
        print(f"   嵌入维度: {self.embed_dim}")
        print(f"   层数: {self.num_layers}")
        print(f"   注意力头数: {self.num_heads}")
        print(f"   Patch大小: {self.patch_size}x{self.patch_size}")
        
        # 特征适配器 - 将ViT特征转换为CNNe兼容的格式
        self.feature_adapter = nn.Conv2d(self.embed_dim, 256, 1, 1, 0)
        
    def forward(self, x):
        """
        前向传播，提取ViT特征
        Args:
            x: 输入图像 [B, 3, H, W]
        Returns:
            features: ViT特征 [B, 256, H/14, W/14]
        """
        B, C, H, W = x.shape
        
        # Proteus ViT需要固定的输入尺寸
        input_size = 224  # Proteus标准输入尺寸
        if H != input_size or W != input_size:
            x_resized = F.interpolate(x, size=(input_size, input_size), mode='bilinear', align_corners=True)
        else:
            x_resized = x
        
        # 通过DINOv2 ViT骨干网络 - 使用forward_features获取特征
        vit_output = self.vit_backbone.forward_features(x_resized)  
        
        # 提取patch tokens并重塑为特征图格式
        # vit_output["x_norm_patchtokens"]: [B, num_patches, 384]
        patch_tokens = vit_output["x_norm_patchtokens"]  # [B, 256, 384] for 224x224 input with patch_size=14
        
        # 计算特征图尺寸
        num_patches_h = input_size // self.patch_size  # 224 // 14 = 16
        num_patches_w = input_size // self.patch_size  # 224 // 14 = 16
        
        # 重塑为特征图格式 [B, 384, 16, 16]
        vit_features = patch_tokens.transpose(1, 2).reshape(B, self.embed_dim, num_patches_h, num_patches_w)
        
        # 特征适配
        adapted_features = self.feature_adapter(vit_features)  # [B, 256, 16, 16]
        
        # 如果原始输入尺寸不同，调整特征尺寸
        if H != input_size or W != input_size:
            target_h = H // self.patch_size
            target_w = W // self.patch_size
            adapted_features = F.interpolate(adapted_features, size=(target_h, target_w), 
                                           mode='bilinear', align_corners=True)
        
        return adapted_features


###################################################################
# ###################### 多尺度特征融合 ########################
###################################################################
class MultiScaleFusion(nn.Module):
    """多尺度特征融合模块"""
    def __init__(self, vit_dim=256, cnn_dim=512):
        super(MultiScaleFusion, self).__init__()
        
        # 特征融合
        fusion_dim = vit_dim + cnn_dim  # 256 + 512 = 768
        
        # 特征适配器
        self.feature_adapter = nn.Sequential(
            nn.Conv2d(fusion_dim, 1024, 1, 1, 0),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True)
        )
        
        # 使用LightLCFI进行特征融合
        self.fusion_lcfi = LightLCFI(1024)
        
        # SCSA注意力融合  
        self.fusion_scsa = SCSA(
            dim=1024,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        # 输出投影
        self.output_proj = nn.Sequential(
            nn.Conv2d(1024//4 + 1024, 256, 3, 1, 1),  # 256 + 1024 = 1280 -> 256
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, vit_features, cnn_features):
        """
        Args:
            vit_features: [B, 256, H/14, W/14] from ViT
            cnn_features: list of [B, C, H, W] from different CNN layers
        """
        # 统一特征尺寸到vit_features的尺寸
        target_size = vit_features.shape[2:]
        
        # 使用最后一层CNN特征
        cnn_feat = cnn_features[-1]  # [B, 512, H, W]
        if cnn_feat.shape[2:] != target_size:
            cnn_feat = F.interpolate(cnn_feat, size=target_size, mode='bilinear', align_corners=True)
        
        # 融合ViT和CNN特征
        fused_features = torch.cat([vit_features, cnn_feat], dim=1)  # [B, 768, H, W]
        
        # 特征适配
        adapted_features = self.feature_adapter(fused_features)  # [B, 1024, H, W]
        
        # 使用LightLCFI进行上下文特征集成
        lcfi_features = self.fusion_lcfi(adapted_features)  # [B, 256, H, W]
        
        # 使用SCSA进行注意力融合
        attended_features = self.fusion_scsa(adapted_features)  # [B, 1024, H, W]
        
        # 结合LCFI和SCSA特征
        combined_features = torch.cat([lcfi_features, attended_features], dim=1)  # [B, 1280, H, W]
        
        # 输出投影
        output = self.output_proj(combined_features)  # [B, 256, H, W]
        
        return output


###################################################################
# ########################## 主网络 ############################
###################################################################
class GDNetProteusViTS(nn.Module):
    """
    Proteus ViT-S融合SCSA和Glass Physics优势的玻璃检测网络
    
    核心优势：
    - Proteus ViT-S: 33.4M参数，强大泛化能力
    - SCSA LightLCFI: 87.9% IoU验证的轻量化特征集成
    - SCSA注意力: 空间-通道联合注意力机制
    - Physics边缘分析: 专业的物理边缘检测
    - 优化CRF: 原版diff_crf实现
    """
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetProteusViTS, self).__init__()
        
        # Proteus ViT-S 特征提取器
        self.vit_feature_extractor = ProteusViTSFeatureExtractor(pretrained_path=backbone_path)
        
        # CNN特征提取器（用于多尺度融合）
        self.cnn_layers = nn.ModuleList([
            # Layer 1: 416x416 -> 208x208
            nn.Sequential(
                nn.Conv2d(3, 64, 7, 2, 3),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(3, 2, 1)  # 208x208 -> 104x104
            ),
            # Layer 2: 104x104 -> 52x52
            nn.Sequential(
                nn.Conv2d(64, 128, 3, 2, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True)
            ),
            # Layer 3: 52x52 -> 26x26
            nn.Sequential(
                nn.Conv2d(128, 256, 3, 2, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            ),
            # Layer 4: 26x26 -> 26x26 (same size as ViT output)
            nn.Sequential(
                nn.Conv2d(256, 512, 3, 1, 1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True)
            )
        ])
        
        # 多尺度特征融合
        self.multi_scale_fusion = MultiScaleFusion(
            vit_dim=256, 
            cnn_dim=512
        )
        
        # Glass Physics边缘分析器
        self.edge_analyzer = PhysicsEdgeAnalyzer(256)
        
        # SCSA风格的最终融合
        final_dim = 256 + 8  # fusion output + edge features
        self.final_scsa = SCSA(
            dim=final_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.final_conv = nn.Sequential(
            nn.Conv2d(final_dim, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )
        
        # 预测头
        self.main_predict = nn.Conv2d(256, 1, 3, 1, 1)
        self.edge_predict = nn.Conv2d(8, 1, 3, 1, 1)
        self.final_predict = nn.Conv2d(64, 1, 3, 1, 1)
        
        # 使用SimplifiedDiffCRF
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=5.0,
            gaussian_weight=3.0,
            bilateral_spatial_sigma=49.0,
            bilateral_color_sigma=5.0,
            gaussian_sigma=3.0,
            trainable=trainable_crf
        )
        
        # 自适应权重 - 学习最优融合比例
        self.adaptive_weights = nn.Parameter(torch.tensor([0.4, 0.3, 0.3]), requires_grad=True)
        
        print(f"✅ Proteus ViT-S GDNet模型创建成功!")

    def forward(self, x):
        """
        Args:
            x: [B, 3, 416, 416]
        Returns:
            dict with predictions and intermediate features
        """
        batch_size = x.size(0)
        
        # ViT特征提取
        vit_features = self.vit_feature_extractor(x)  # [B, 256, H/14, W/14]
        
        # CNN多尺度特征提取
        cnn_features = []
        cnn_x = x
        for layer in self.cnn_layers:
            cnn_x = layer(cnn_x)
            cnn_features.append(cnn_x)
        
        # 多尺度特征融合
        fused_features = self.multi_scale_fusion(vit_features, cnn_features)  # [B, 256, H, W]
        
        # Glass Physics边缘分析
        edge_features, sharpness_map = self.edge_analyzer(fused_features, x)  # [B, 8, H, W]
        
        # 最终SCSA融合
        final_input = torch.cat([fused_features, edge_features], dim=1)  # [B, 264, H, W]
        final_attended = self.final_scsa(final_input)
        final_features = self.final_conv(final_attended)  # [B, 64, H, W]
        
        # 预测
        main_pred = self.main_predict(fused_features)  # [B, 1, H, W]
        edge_pred = self.edge_predict(edge_features)   # [B, 1, H, W]
        final_pred = self.final_predict(final_features) # [B, 1, H, W]
        
        # 上采样到原始尺寸
        main_pred = F.interpolate(main_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        edge_pred = F.interpolate(edge_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        final_pred = F.interpolate(final_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        
        # 应用sigmoid
        main_pred_prob = torch.sigmoid(main_pred)
        edge_pred_prob = torch.sigmoid(edge_pred)
        final_pred_prob = torch.sigmoid(final_pred)
        
        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = (weights[0] * main_pred_prob + 
                        weights[1] * edge_pred_prob + 
                        weights[2] * final_pred_prob)
        
        # 原版CRF后处理 - 直接使用，不做修改
        # 准备CRF输入：将单通道概率转换为双通道logits
        bg_prob = 1 - ensemble_pred
        fg_prob = ensemble_pred
        # 加小epsilon避免log(0)
        eps = 1e-7
        bg_prob = torch.clamp(bg_prob, eps, 1.0 - eps)
        fg_prob = torch.clamp(fg_prob, eps, 1.0 - eps)
        # 转换为logits格式 [B, 2, H, W]
        unary_logits = torch.cat([torch.log(bg_prob), torch.log(fg_prob)], dim=1)
        
        # 应用原版CRF
        refined_predict = self.crf(unary_logits, x)  # 返回 [B, 1, H, W]
        
        return {
            'main_pred': main_pred_prob,
            'edge_pred': edge_pred_prob,
            'final_pred': final_pred_prob,
            'ensemble_pred': ensemble_pred,
            'refined_pred': refined_predict,
            'sharpness_map': F.interpolate(sharpness_map, size=x.size()[2:], mode='bilinear', align_corners=True)
        }


###################################################################
# ################ Proteus ViT-B Feature Extractor ##############
###################################################################
class ProteusViTBFeatureExtractor(nn.Module):
    """Proteus ViT-Base特征提取器 - 768维度版本"""
    def __init__(self, pretrained_path=None):
        super(ProteusViTBFeatureExtractor, self).__init__()
        
        # 检查预训练权重
        if pretrained_path and os.path.exists(pretrained_path):
            print(f"📦 加载DINOv2 Proteus权重: {pretrained_path}")
            
            # 使用真正的ViT-B架构，patch_size=14匹配Proteus权重，强制使用标准Attention
            from models_dinov2 import DinoVisionTransformer, Attention, Block
            from functools import partial
            
            # 直接创建DinoVisionTransformer，避免vit_base的默认MemEffAttention
            self.vit_model = DinoVisionTransformer(
                patch_size=14,
                embed_dim=768,
                depth=12,
                num_heads=12,
                mlp_ratio=4,
                block_fn=partial(Block, attn_class=Attention),  # 使用标准Attention
                num_register_tokens=0
            )
            
            # 加载权重 - 只使用student模型部分
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            if 'model' in checkpoint:
                model_weights = checkpoint['model']
                # 过滤出student.backbone的权重
                student_weights = {}
                for key, value in model_weights.items():
                    if key.startswith('student.backbone.'):
                        # 移除student.backbone前缀
                        new_key = key.replace('student.backbone.', '')
                        student_weights[new_key] = value
                
                print(f"   提取Student权重: {len(student_weights)}个参数")
                self.vit_model.load_state_dict(student_weights, strict=False)
            else:
                self.vit_model.load_state_dict(checkpoint, strict=False)
            
            print(f"✅ 使用DINOv2 Proteus ViT-B:")
            print(f"   嵌入维度: 768")
            print(f"   层数: 12") 
            print(f"   注意力头数: 12")
            print(f"   Patch大小: 14x14")
        else:
            print(f"⚠️  Proteus权重路径无效，使用随机初始化")
            from models_dinov2 import DinoVisionTransformer, Attention, Block
            from functools import partial
            
            # 直接创建DinoVisionTransformer，避免vit_base的默认MemEffAttention
            self.vit_model = DinoVisionTransformer(
                patch_size=14,
                embed_dim=768,
                depth=12,
                num_heads=12,
                mlp_ratio=4,
                block_fn=partial(Block, attn_class=Attention),  # 使用标准Attention
                num_register_tokens=0
            )
            
        # ViT-B输出768维，直接适配到256维
        self.feature_adapter = nn.Sequential(
            nn.Conv2d(768, 512, 1, 1, 0),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 256, 1, 1, 0),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        
        self.patch_size = 14
        
    def forward(self, x):
        """
        Args:
            x: [B, 3, H, W]
        Returns:
            features: [B, 256, H/14, W/14]
        """
        B, C, H, W = x.shape
        
        # ViT前向传播
        vit_output = self.vit_model.forward_features(x)
        
        # DINOv2返回字典格式，提取patch tokens (已移除CLS token)
        vit_features = vit_output["x_norm_patchtokens"]  # [B, N, 768]
        
        # 重塑为特征图
        input_size = 416  # 假设输入是416x416
        patch_h = input_size // self.patch_size  # 416 // 14 = 29.7 -> 30
        patch_w = input_size // self.patch_size
        
        # 实际计算patch数量
        actual_patches = int((input_size / self.patch_size) ** 2)
        if vit_features.size(1) != actual_patches:
            # 如果patch数量不匹配，使用自适应方法
            patch_side = int(vit_features.size(1) ** 0.5)
            patch_h = patch_w = patch_side
        
        vit_features = vit_features.transpose(1, 2).reshape(B, 768, patch_h, patch_w)
        
        # 特征适配
        adapted_features = self.feature_adapter(vit_features)  # [B, 256, 30, 30]
        
        # 如果原始输入尺寸不同，调整特征尺寸
        if H != input_size or W != input_size:
            target_h = H // self.patch_size
            target_w = W // self.patch_size
            adapted_features = F.interpolate(adapted_features, size=(target_h, target_w), 
                                           mode='bilinear', align_corners=True)
        
        return adapted_features


###################################################################
# ####################### ViT-B主网络 ############################
###################################################################
class GDNetProteusViTB(nn.Module):
    """
    Proteus ViT-B融合SCSA和Glass Physics优势的玻璃检测网络
    
    核心优势：
    - Proteus ViT-B: ~86M参数，更强的特征表示能力
    - SCSA LightLCFI: 87.9% IoU验证的轻量化特征集成
    - SCSA注意力: 空间-通道联合注意力机制
    - Physics边缘分析: 专业的物理边缘检测
    - 优化CRF: 原版diff_crf实现
    """
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetProteusViTB, self).__init__()
        
        # Proteus ViT-B 特征提取器
        self.vit_feature_extractor = ProteusViTBFeatureExtractor(pretrained_path=backbone_path)
        
        # CNN特征提取器（用于多尺度融合）
        self.cnn_layers = nn.ModuleList([
            # Layer 1: 416x416 -> 208x208
            nn.Sequential(
                nn.Conv2d(3, 64, 7, 2, 3),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(3, 2, 1)  # 208x208 -> 104x104
            ),
            # Layer 2: 104x104 -> 52x52
            nn.Sequential(
                nn.Conv2d(64, 128, 3, 2, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True)
            ),
            # Layer 3: 52x52 -> 26x26
            nn.Sequential(
                nn.Conv2d(128, 256, 3, 2, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            ),
            # Layer 4: 26x26 -> 26x26 (same size as ViT output)
            nn.Sequential(
                nn.Conv2d(256, 512, 3, 1, 1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True)
            )
        ])
        
        # 多尺度特征融合
        self.multi_scale_fusion = MultiScaleFusion(
            vit_dim=256, 
            cnn_dim=512
        )
        
        # Glass Physics边缘分析器
        self.edge_analyzer = PhysicsEdgeAnalyzer(256)
        
        # SCSA风格的最终融合
        final_dim = 256 + 8  # fusion output + edge features
        self.final_scsa = SCSA(
            dim=final_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.final_conv = nn.Sequential(
            nn.Conv2d(final_dim, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )
        
        # 预测头
        self.main_predict = nn.Conv2d(256, 1, 3, 1, 1)
        self.edge_predict = nn.Conv2d(8, 1, 3, 1, 1)
        self.final_predict = nn.Conv2d(64, 1, 3, 1, 1)
        
        # 使用SimplifiedDiffCRF
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=5.0,
            gaussian_weight=3.0,
            bilateral_spatial_sigma=49.0,
            bilateral_color_sigma=5.0,
            gaussian_sigma=3.0,
            trainable=trainable_crf
        )
        
        # 自适应权重 - 学习最优融合比例
        self.adaptive_weights = nn.Parameter(torch.tensor([0.4, 0.3, 0.3]), requires_grad=True)
        
        print(f"✅ Proteus ViT-B GDNet模型创建成功!")

    def forward(self, x):
        """
        Args:
            x: [B, 3, 416, 416]
        Returns:
            dict with predictions and intermediate features
        """
        batch_size = x.size(0)
        
        # ViT特征提取
        vit_features = self.vit_feature_extractor(x)  # [B, 256, H/14, W/14]
        
        # CNN多尺度特征提取
        cnn_features = []
        cnn_x = x
        for layer in self.cnn_layers:
            cnn_x = layer(cnn_x)
            cnn_features.append(cnn_x)
        
        # 多尺度特征融合
        fused_features = self.multi_scale_fusion(vit_features, cnn_features)  # [B, 256, H, W]
        
        # Glass Physics边缘分析
        edge_features, sharpness_map = self.edge_analyzer(fused_features, x)  # [B, 8, H, W]
        
        # 最终SCSA融合
        final_input = torch.cat([fused_features, edge_features], dim=1)  # [B, 264, H, W]
        final_attended = self.final_scsa(final_input)
        final_features = self.final_conv(final_attended)  # [B, 64, H, W]
        
        # 预测
        main_pred = self.main_predict(fused_features)  # [B, 1, H, W]
        edge_pred = self.edge_predict(edge_features)   # [B, 1, H, W]
        final_pred = self.final_predict(final_features) # [B, 1, H, W]
        
        # 上采样到原始尺寸
        main_pred = F.interpolate(main_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        edge_pred = F.interpolate(edge_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        final_pred = F.interpolate(final_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        
        # 应用sigmoid
        main_pred_prob = torch.sigmoid(main_pred)
        edge_pred_prob = torch.sigmoid(edge_pred)
        final_pred_prob = torch.sigmoid(final_pred)
        
        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = (weights[0] * main_pred_prob + 
                        weights[1] * edge_pred_prob + 
                        weights[2] * final_pred_prob)
        
        # 原版CRF后处理 - 直接使用，不做修改
        # 准备CRF输入：将单通道概率转换为双通道logits
        bg_prob = 1 - ensemble_pred
        fg_prob = ensemble_pred
        # 加小epsilon避免log(0)
        eps = 1e-7
        bg_prob = torch.clamp(bg_prob, eps, 1.0 - eps)
        fg_prob = torch.clamp(fg_prob, eps, 1.0 - eps)
        # 转换为logits格式 [B, 2, H, W]
        unary_logits = torch.cat([torch.log(bg_prob), torch.log(fg_prob)], dim=1)
        
        # 应用原版CRF
        refined_predict = self.crf(unary_logits, x)  # 返回 [B, 1, H, W]
        
        return {
            'main_pred': main_pred_prob,
            'edge_pred': edge_pred_prob,
            'final_pred': final_pred_prob,
            'ensemble_pred': ensemble_pred,
            'refined_pred': refined_predict,
            'sharpness_map': F.interpolate(sharpness_map, size=x.size()[2:], mode='bilinear', align_corners=True)
        }


def create_proteus_vits_model(backbone_path=None, crf_iter=5, trainable_crf=True, vit_type='vitb'):
    """创建Proteus ViT模型
    
    Args:
        backbone_path: 预训练权重路径
        crf_iter: CRF迭代次数
        trainable_crf: CRF是否可训练
        vit_type: 'vits' for ViT-Small (~32M), 'vitb' for ViT-Base (~86M)
    """
    if vit_type.lower() == 'vitb':
        return GDNetProteusViTB(
            backbone_path=backbone_path,
            crf_iter=crf_iter,
            trainable_crf=trainable_crf
        )
    else:
        return GDNetProteusViTS(
            backbone_path=backbone_path,
            crf_iter=crf_iter,
            trainable_crf=trainable_crf
        )


if __name__ == "__main__":
    # 测试模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = create_proteus_vits_model(
        backbone_path='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/proteus_vits_backbone.pth',
        crf_iter=5
    ).to(device)
    
    # 测试前向传播
    with torch.no_grad():
        x = torch.randn(2, 3, 416, 416).to(device)
        outputs = model(x)
        
        print("模型输出:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
        
        # 计算参数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"\n模型参数统计:")
        print(f"  总参数量: {total_params:,} ({total_params/1e6:.1f}M)")
        print(f"  可训练参数: {trainable_params:,} ({trainable_params/1e6:.1f}M)")
        
        print(f"\n✅ Proteus ViT-S GDNet模型创建成功!")
        print(f"   - 模型大小: ~{trainable_params/1e6:.1f}M参数")
        print(f"   - 输入尺寸: 416x416")
        print(f"   - 输出: 多尺度预测 + 原版CRF后处理") 