"""
 @Time    : 2024
 <AUTHOR> <PERSON>ine
 @Project : IG_SLAM
 @File    : gdnet_vit_physics_hybrid.py
 @Function: ViT+精选物理特性的混合玻璃检测网络，目标突破90% IoU

融合优势：
1. Proteus ViT-B精细特征 (patch_size=8, 53x53分辨率)
2. 精选的玻璃物理特性 (反射+透明度，简化版)
3. 物理-ViT协同注意力机制
4. 多尺度预测融合
5. 数值稳定性优化
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os
from functools import partial

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
proteus_path = os.path.join(project_root, 'Proteus-pytorch', 'pretrain')

if proteus_path not in sys.path:
    sys.path.append(proteus_path)
if current_dir not in sys.path:
    sys.path.append(current_dir)

try:
    from models_dinov2 import DinoVisionTransformer, Attention, Block
    PROTEUS_AVAILABLE = True
    print("✅ Proteus DINOv2框架加载成功")
except ImportError:
    print("⚠️ Proteus框架未找到，使用fallback实现")
    PROTEUS_AVAILABLE = False

from diff_crf import SimplifiedDiffCRF
from attention_scsa import SCSA


class ProteusViTBackbone(nn.Module):
    """
    Proteus ViT-B骨干网络，专门优化
    """
    def __init__(self, pretrained_path=None, img_size=420, patch_size=8):
        super(ProteusViTBackbone, self).__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        
        if PROTEUS_AVAILABLE:
            self.vit_model = DinoVisionTransformer(
                patch_size=patch_size,
                embed_dim=768,
                depth=12,
                num_heads=12,
                mlp_ratio=4,
                block_fn=partial(Block, attn_class=Attention),
                num_register_tokens=0
            )
            self.embed_dim = 768
            self._load_proteus_weights(pretrained_path)
        else:
            self.vit_model = self._create_fallback_vit()
            self.embed_dim = 768
            
        # 多层特征提取
        self.feature_layers = [3, 6, 9, 12]
        
        # 特征适配器
        self.layer_adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(self.embed_dim, 256, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            ) for _ in self.feature_layers
        ])
        
        print(f"✅ Proteus ViT-B (patch_size={patch_size}, 特征图={img_size//patch_size}×{img_size//patch_size})")
        
    def _create_fallback_vit(self):
        """Fallback ViT实现"""
        class FallbackViT(nn.Module):
            def __init__(self, patch_size, embed_dim, img_size):
                super().__init__()
                self.patch_embed = nn.Conv2d(3, embed_dim, kernel_size=patch_size, stride=patch_size)
                num_patches = (img_size // patch_size) ** 2
                self.pos_embed = nn.Parameter(torch.randn(1, num_patches, embed_dim) * 0.02)
                self.blocks = nn.ModuleList([
                    nn.TransformerEncoderLayer(embed_dim, 12, embed_dim*4, dropout=0.1, batch_first=True)
                    for _ in range(12)
                ])
                
            def forward_features(self, x):
                B, C, H, W = x.shape
                x = self.patch_embed(x)
                x = x.flatten(2).transpose(1, 2)
                x = x + self.pos_embed
                
                intermediate_features = []
                for i, block in enumerate(self.blocks):
                    x = block(x)
                    if i + 1 in [3, 6, 9, 12]:
                        intermediate_features.append(x)
                
                return {
                    "intermediate_features": intermediate_features,
                    "x_norm_patchtokens": intermediate_features[-1]
                }
        
        return FallbackViT(self.patch_size, self.embed_dim, self.img_size)
        
    def _load_proteus_weights(self, pretrained_path):
        """加载Proteus权重"""
        if pretrained_path and os.path.exists(pretrained_path):
            print(f"📦 加载Proteus权重: {pretrained_path}")
            try:
                checkpoint = torch.load(pretrained_path, map_location='cpu')
                if 'model' in checkpoint:
                    model_weights = checkpoint['model']
                    student_weights = {}
                    for key, value in model_weights.items():
                        if key.startswith('student.backbone.'):
                            new_key = key.replace('student.backbone.', '')
                            student_weights[new_key] = value
                    
                    self.vit_model.load_state_dict(student_weights, strict=False)
                    print(f"✅ 权重加载成功")
                else:
                    self.vit_model.load_state_dict(checkpoint, strict=False)
            except Exception as e:
                print(f"⚠️ 权重加载失败: {e}")
                
    def forward(self, x):
        """提取多层次ViT特征"""
        B, C, H, W = x.shape
        
        if H != self.img_size or W != self.img_size:
            x = F.interpolate(x, size=(self.img_size, self.img_size), mode='bilinear', align_corners=True)
        
        # 禁用xformers
        if PROTEUS_AVAILABLE:
            try:
                import models_dinov2
                models_dinov2.XFORMERS_AVAILABLE = False
            except:
                pass
        
        vit_output = self.vit_model.forward_features(x)
        
        if "intermediate_features" in vit_output:
            intermediate_features = vit_output["intermediate_features"]
        else:
            final_features = vit_output["x_norm_patchtokens"]
            intermediate_features = [final_features] * len(self.feature_layers)
        
        multi_scale_features = []
        patch_h = patch_w = self.img_size // self.patch_size
        
        for i, features in enumerate(intermediate_features):
            feature_map = features.transpose(1, 2).reshape(B, self.embed_dim, patch_h, patch_w)
            adapted_features = self.layer_adapters[i](feature_map)
            multi_scale_features.append(adapted_features)
        
        return multi_scale_features


class LightweightGlassPhysics(nn.Module):
    """
    轻量化玻璃物理特性模块 - 精选最有效的物理特性
    """
    def __init__(self, in_channels=256):
        super(LightweightGlassPhysics, self).__init__()
        
        self.in_channels = in_channels
        
        # 1. 简化的反射检测 - 玻璃镜面反射特性
        self.reflection_detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, 1, 1),
            nn.Sigmoid()
        )
        
        # 2. 透明度分析 - 玻璃透光特性
        self.transparency_detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, 5, 1, 2),  # 更大感受野
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, in_channels//8, 3, 1, 1),
            nn.BatchNorm2d(in_channels//8),
            nn.ReLU(inplace=True)
        )
        
        # 3. 光学扭曲检测 - 玻璃折射效应
        self.distortion_detector_h = nn.Conv2d(in_channels, in_channels//8, (1, 5), 1, (0, 2))
        self.distortion_detector_v = nn.Conv2d(in_channels, in_channels//8, (5, 1), 1, (2, 0))
        
        # 4. 物理特征融合
        physics_channels = in_channels//8 + in_channels//8 + in_channels//8  # transparency + distortion_h + distortion_v
        self.physics_fusion = nn.Sequential(
            nn.Conv2d(physics_channels, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, in_channels//8, 1),
            nn.BatchNorm2d(in_channels//8),
            nn.ReLU(inplace=True)
        )
        
        # 5. 物理置信度估计
        self.confidence_estimator = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels//8, in_channels//16, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//16, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x, rgb_input=None):
        """
        Args:
            x: ViT特征 [B, 256, 53, 53]
            rgb_input: 原始RGB图像 [B, 3, H, W] (可选)
        Returns:
            physics_features: [B, 32, 53, 53]
            reflection_map: [B, 1, 53, 53]
            confidence: [B, 1, 1, 1]
        """
        # 1. 反射检测
        reflection_map = self.reflection_detector(x)  # [B, 1, 53, 53]
        
        # 2. 透明度分析
        transparency_features = self.transparency_detector(x)  # [B, 32, 53, 53]
        
        # 3. 光学扭曲检测
        distortion_h = self.distortion_detector_h(x)  # [B, 32, 53, 53]
        distortion_v = self.distortion_detector_v(x)  # [B, 32, 53, 53]
        
        # 4. 物理特征融合
        physics_input = torch.cat([transparency_features, distortion_h, distortion_v], dim=1)
        physics_features = self.physics_fusion(physics_input)  # [B, 32, 53, 53]
        
        # 5. 置信度估计
        confidence = self.confidence_estimator(physics_features)  # [B, 1, 1, 1]
        
        return physics_features, reflection_map, confidence


class PhysicsViTFusion(nn.Module):
    """
    物理特性-ViT融合模块
    """
    def __init__(self, vit_dim=256, physics_dim=32):
        super(PhysicsViTFusion, self).__init__()
        
        # 特征对齐
        self.vit_proj = nn.Conv2d(vit_dim, 256, 1)
        self.physics_proj = nn.Conv2d(physics_dim, 64, 1)
        
        # 物理引导的注意力
        self.physics_attention = nn.Sequential(
            nn.Conv2d(physics_dim, 1, 3, 1, 1),
            nn.Sigmoid()
        )
        
        # 跨模态融合
        self.cross_fusion = nn.Sequential(
            nn.Conv2d(320, 256, 3, 1, 1),  # 256 + 64 = 320
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        
        # SCSA注意力增强
        self.scsa_enhance = SCSA(
            dim=256,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
    def forward(self, vit_features, physics_features, physics_confidence):
        """
        Args:
            vit_features: [B, 256, 53, 53]
            physics_features: [B, 32, 53, 53]
            physics_confidence: [B, 1, 1, 1]
        Returns:
            fused_features: [B, 256, 53, 53]
        """
        # 特征投影
        vit_aligned = self.vit_proj(vit_features)       # [B, 256, 53, 53]
        physics_aligned = self.physics_proj(physics_features)  # [B, 64, 53, 53]
        
        # 物理引导的注意力
        physics_att = self.physics_attention(physics_features)  # [B, 1, 53, 53]
        physics_att = physics_att * physics_confidence  # 使用置信度调节
        
        # 应用物理注意力到ViT特征
        enhanced_vit = vit_aligned * (1 + physics_att)
        
        # 跨模态融合
        combined = torch.cat([enhanced_vit, physics_aligned], dim=1)  # [B, 320, 53, 53]
        fused = self.cross_fusion(combined)  # [B, 256, 53, 53]
        
        # SCSA注意力增强
        enhanced_fused = self.scsa_enhance(fused)  # [B, 256, 53, 53]
        
        return enhanced_fused


class GDNetViTPhysicsHybrid(nn.Module):
    """
    ViT+物理特性混合玻璃检测网络
    """
    def __init__(self, backbone_path=None, crf_iter=6, trainable_crf=True):
        super(GDNetViTPhysicsHybrid, self).__init__()
        
        # Proteus ViT骨干
        self.vit_backbone = ProteusViTBackbone(
            pretrained_path=backbone_path,
            img_size=420,
            patch_size=8
        )
        
        # 轻量化玻璃物理特性
        self.glass_physics = LightweightGlassPhysics(in_channels=256)
        
        # 物理-ViT融合
        self.physics_vit_fusion = PhysicsViTFusion(vit_dim=256, physics_dim=32)
        
        # 多尺度预测头
        self.main_predictor = nn.Sequential(
            nn.Conv2d(256, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 1, 3, 1, 1)
        )
        
        # 物理预测头
        self.physics_predictor = nn.Conv2d(32, 1, 3, 1, 1)
        
        # 多层ViT预测头
        self.vit_predictors = nn.ModuleList([
            nn.Conv2d(256, 1, 3, 1, 1) for _ in range(4)
        ])
        
        # CRF后处理
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=8.0,
            gaussian_weight=4.0,
            bilateral_spatial_sigma=40.0,
            bilateral_color_sigma=3.0,
            gaussian_sigma=1.5,
            trainable=trainable_crf
        )
        
        # 自适应权重 - 主预测、物理预测、4个ViT层预测
        self.adaptive_weights = nn.Parameter(
            torch.tensor([0.4, 0.2, 0.1, 0.1, 0.1, 0.1]), requires_grad=True
        )
        
        print(f"✅ ViT+物理特性混合网络创建成功!")
        
    def forward(self, x):
        """
        Args:
            x: [B, 3, H, W]
        Returns:
            predictions dict
        """
        original_size = x.shape[2:]
        
        # 调整输入尺寸
        if x.shape[2:] != (420, 420):
            x_resized = F.interpolate(x, size=(420, 420), mode='bilinear', align_corners=True)
        else:
            x_resized = x
        
        # ViT多层特征提取
        vit_features = self.vit_backbone(x_resized)  # list of [B, 256, 53, 53]
        
        # 使用最深层ViT特征进行物理分析
        deepest_vit = vit_features[-1]  # [B, 256, 53, 53]
        
        # 玻璃物理特性分析
        physics_features, reflection_map, physics_confidence = self.glass_physics(
            deepest_vit, x_resized
        )
        
        # 物理-ViT融合
        fused_features = self.physics_vit_fusion(
            deepest_vit, physics_features, physics_confidence
        )  # [B, 256, 53, 53]
        
        # 预测
        main_pred = self.main_predictor(fused_features)      # [B, 1, 53, 53]
        physics_pred = self.physics_predictor(physics_features)  # [B, 1, 53, 53]
        
        # 多层ViT预测
        vit_preds = []
        for i, predictor in enumerate(self.vit_predictors):
            vit_pred = predictor(vit_features[i])
            vit_preds.append(vit_pred)
        
        # 上采样到原始尺寸
        main_pred = F.interpolate(main_pred, size=original_size, mode='bilinear', align_corners=True)
        physics_pred = F.interpolate(physics_pred, size=original_size, mode='bilinear', align_corners=True)
        reflection_map = F.interpolate(reflection_map, size=original_size, mode='bilinear', align_corners=True)
        
        vit_preds = [
            F.interpolate(pred, size=original_size, mode='bilinear', align_corners=True)
            for pred in vit_preds
        ]
        
        # 应用sigmoid
        main_pred_prob = torch.sigmoid(main_pred)
        physics_pred_prob = torch.sigmoid(physics_pred)
        vit_pred_probs = [torch.sigmoid(pred) for pred in vit_preds]
        
        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = (weights[0] * main_pred_prob + 
                        weights[1] * physics_pred_prob +
                        sum(weights[i+2] * vit_pred_probs[i] for i in range(4)))
        
        ensemble_pred = torch.clamp(ensemble_pred, min=1e-7, max=1.0 - 1e-7)
        
        # CRF后处理
        bg_logits = torch.log(1 - ensemble_pred + 1e-7)
        fg_logits = torch.log(ensemble_pred + 1e-7)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
        
        normalized_img = self._normalize_image(x)
        
        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - ensemble_pred, ensemble_pred], dim=1)
        
        return {
            'main_pred': main_pred_prob,
            'physics_pred': physics_pred_prob,
            'vit_preds': vit_pred_probs,
            'ensemble_pred': ensemble_pred,
            'refined_pred': refined_predict,
            'reflection_map': reflection_map,
            'physics_confidence': physics_confidence
        }
    
    def _normalize_image(self, x):
        """图像归一化"""
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        return normalized_img


def create_vit_physics_hybrid_model(backbone_path=None, crf_iter=6, trainable_crf=True):
    """创建ViT+物理特性混合模型"""
    return GDNetViTPhysicsHybrid(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )


if __name__ == "__main__":
    # 测试模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = create_vit_physics_hybrid_model().to(device)
    
    with torch.no_grad():
        x = torch.randn(2, 3, 416, 416).to(device)
        outputs = model(x)
        
        print("模型输出:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            elif isinstance(value, list):
                print(f"  {key}: {len(value)} items")
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"\n参数量: {total_params/1e6:.1f}M")
        print(f"✅ ViT+物理特性混合模型测试成功!")