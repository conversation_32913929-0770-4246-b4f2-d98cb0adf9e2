"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_glass_physics.py
 @Function: 基于玻璃物理特性的检测网络

基于玻璃的物理特性（反射、透射、边缘效应、偏振等）进行检测，
移除HDCN复杂度，提高实时性能。
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF
from ig_glass.attention_scsa import SCSA


###################################################################
# ############## 玻璃物理特性分析模块 ##########################
###################################################################

class ReflectionAnalyzer(nn.Module):
    """
    反射分析模块：利用玻璃表面的反射特性
    玻璃通常会产生镜面反射，造成亮度突变和颜色失真
    """
    def __init__(self, in_channels):
        super(ReflectionAnalyzer, self).__init__()
        self.in_channels = in_channels
        
        # 反射检测：检测亮度和颜色的局部异常
        self.luminance_detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, 1, 1),
            nn.Sigmoid()
        )
        
        # 颜色失真检测
        self.color_distortion_detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True)
        )
        
        # 反射一致性检测：使用固定的反射核，避免梯度爆炸
        # 注册为buffer而非Parameter，不参与梯度更新
        reflection_kernels = torch.randn(8, 1, 5, 5) * 0.1
        self.register_buffer('reflection_kernels', reflection_kernels)
        
    def forward(self, x):
        # 亮度异常检测
        luminance_map = self.luminance_detector(x)
        
        # 颜色失真特征
        color_features = self.color_distortion_detector(x)
        
        # 反射模式检测
        reflection_responses = []
        for i in range(8):
            response = F.conv2d(luminance_map, self.reflection_kernels[i:i+1], padding=2)
            reflection_responses.append(response)
        reflection_pattern = torch.cat(reflection_responses, dim=1)
        
        # 融合反射特征
        reflection_features = torch.cat([color_features, reflection_pattern], dim=1)
        
        return reflection_features, luminance_map


class TransparencyAnalyzer(nn.Module):
    """
    透明度分析模块：分析玻璃的透明特性
    透明玻璃会传递背景信息，但会产生细微的光学扭曲
    """
    def __init__(self, in_channels):
        super(TransparencyAnalyzer, self).__init__()
        
        # 背景一致性检测
        self.background_consistency = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 7, 1, 3),  # 更大的感受野
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//4, 5, 1, 2),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True)
        )
        
        # 光学扭曲检测：使用方向性卷积
        self.distortion_detector_h = nn.Conv2d(in_channels, in_channels//4, (1, 7), 1, (0, 3))
        self.distortion_detector_v = nn.Conv2d(in_channels, in_channels//4, (7, 1), 1, (3, 0))
        
        # 透明度估计
        self.transparency_estimator = nn.Sequential(
            nn.Conv2d(in_channels//2, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 背景一致性分析
        bg_features = self.background_consistency(x)
        
        # 光学扭曲检测
        distortion_h = self.distortion_detector_h(x)
        distortion_v = self.distortion_detector_v(x)
        distortion_features = torch.cat([distortion_h, distortion_v], dim=1)
        
        # 透明度估计
        transparency_map = self.transparency_estimator(distortion_features)
        
        # 融合透明度特征
        transparency_features = torch.cat([bg_features, distortion_features], dim=1)
        
        return transparency_features, transparency_map


class EdgePhysicsAnalyzer(nn.Module):
    """
    边缘物理分析模块：基于玻璃边缘的光学特性
    玻璃边缘通常有折射、散射等独特的光学现象
    改进版本：加入拉普拉斯算子和HSV引导的边缘检测
    """
    def __init__(self, in_channels):
        super(EdgePhysicsAnalyzer, self).__init__()
        
        # 多尺度边缘检测
        self.edge_detectors = nn.ModuleList([
            self._make_edge_detector(in_channels, kernel_size=3),
            self._make_edge_detector(in_channels, kernel_size=5),
            self._make_edge_detector(in_channels, kernel_size=7)
        ])
        
        # 专业边缘检测核
        # 拉普拉斯算子 - 与EdgeSaliencyLoss一致
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1.,  8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))
        
        # Sobel算子 - 用于方向性边缘检测
        self.register_buffer('sobel_x', torch.tensor([
            [-1, 0, 1], 
            [-2, 0, 2], 
            [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y', torch.tensor([
            [-1, -2, -1], 
            [ 0,  0,  0], 
            [ 1,  2,  1]
        ]).float().view(1, 1, 3, 3))
        
        # 折射模式检测：使用固定的折射核，避免梯度爆炸
        # 注册为buffer而非Parameter，不参与梯度更新
        refraction_patterns = torch.randn(6, 1, 3, 3) * 0.1
        self.register_buffer('refraction_patterns', refraction_patterns)
        
        # 边缘锐度分析 - 改进版本
        self.sharpness_analyzer = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, 1, 1),
            nn.Sigmoid()
        )
        
        # HSV引导的边缘增强
        self.hsv_edge_enhancer = nn.Sequential(
            nn.Conv2d(3, 16, 3, 1, 1),  # RGB输入
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, 3, 1, 1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 1),
            nn.Sigmoid()
        )
        
        # 边缘融合模块
        self.edge_fusion = nn.Sequential(
            nn.Conv2d(4, 16, 3, 1, 1),  # 融合4种边缘检测结果
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, 3, 1, 1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True)
        )
        
    def _make_edge_detector(self, in_channels, kernel_size):
        padding = kernel_size // 2
        return nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, kernel_size, 1, padding),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, in_channels//4, 1)
        )
    
    def _apply_edge_kernels(self, x):
        """应用专业边缘检测核"""
        # 转换为灰度图像
        if x.size(1) > 1:
            gray = torch.mean(x, dim=1, keepdim=True)
        else:
            gray = x
        
        # 拉普拉斯边缘检测
        laplacian_edges = F.relu(torch.tanh(F.conv2d(gray, self.laplacian_kernel, padding=1)))
        
        # Sobel边缘检测
        sobel_x_edges = F.conv2d(gray, self.sobel_x, padding=1)
        sobel_y_edges = F.conv2d(gray, self.sobel_y, padding=1)
        sobel_edges = torch.sqrt(sobel_x_edges**2 + sobel_y_edges**2)
        sobel_edges = torch.sigmoid(sobel_edges)
        
        return laplacian_edges, sobel_edges
    
    def _hsv_guided_edge_detection(self, rgb_input, feature_edges):
        """HSV引导的边缘检测"""
        # HSV转换近似
        r, g, b = rgb_input[:, 0:1], rgb_input[:, 1:2], rgb_input[:, 2:3]
        max_rgb = torch.max(torch.max(r, g), b)
        min_rgb = torch.min(torch.min(r, g), b)
        diff = max_rgb - min_rgb
        
        # 饱和度（玻璃通常低饱和度）
        saturation = diff / (max_rgb + 1e-7)
        
        # HSV边缘增强
        hsv_edge_weight = self.hsv_edge_enhancer(rgb_input)
        
        # 结合特征边缘和HSV信息
        enhanced_edges = feature_edges * hsv_edge_weight
        
        return enhanced_edges, saturation
        
    def forward(self, x, rgb_input=None):
        # 多尺度边缘特征
        edge_features = []
        for detector in self.edge_detectors:
            edge_feat = detector(x)
            edge_features.append(edge_feat)
        multi_scale_edges = torch.cat(edge_features, dim=1)
        
        # 专业边缘检测
        laplacian_edges, sobel_edges = self._apply_edge_kernels(x)
        
        # HSV引导的边缘检测（如果提供RGB输入）
        if rgb_input is not None:
            # 将特征边缘上采样到RGB尺寸进行HSV引导
            feature_edge_up = F.interpolate(sobel_edges, size=rgb_input.shape[2:], mode='bilinear', align_corners=True)
            hsv_enhanced_edges, saturation_map = self._hsv_guided_edge_detection(rgb_input, feature_edge_up)
            # 下采样回特征尺寸
            hsv_enhanced_edges = F.interpolate(hsv_enhanced_edges, size=x.shape[2:], mode='bilinear', align_corners=True)
        else:
            hsv_enhanced_edges = sobel_edges
            saturation_map = torch.ones_like(sobel_edges)
        
        # 融合多种边缘检测结果
        edge_stack = torch.cat([laplacian_edges, sobel_edges, hsv_enhanced_edges, 
                               torch.mean(multi_scale_edges, dim=1, keepdim=True)], dim=1)
        fused_edges = self.edge_fusion(edge_stack)
        
        # 折射模式检测
        refraction_responses = []
        edge_gray = torch.mean(x, dim=1, keepdim=True)
        for i in range(6):
            response = F.conv2d(edge_gray, self.refraction_patterns[i:i+1], padding=1)
            refraction_responses.append(response)
        refraction_pattern = torch.cat(refraction_responses, dim=1)
        
        # 边缘锐度分析
        sharpness_map = self.sharpness_analyzer(x)
        
        # 融合边缘物理特征
        edge_physics_features = torch.cat([multi_scale_edges, refraction_pattern, fused_edges], dim=1)
        
        # 调整通道数到56 (当前是48+6+8=62，需要减少6个通道)
        if edge_physics_features.size(1) > 56:
            # 使用1x1卷积调整通道数
            if not hasattr(self, 'channel_adjuster'):
                self.channel_adjuster = nn.Conv2d(edge_physics_features.size(1), 56, 1).to(edge_physics_features.device)
            edge_physics_features = self.channel_adjuster(edge_physics_features)
        elif edge_physics_features.size(1) < 56:
            padding_channels = 56 - edge_physics_features.size(1)
            padding = torch.zeros(edge_physics_features.size(0), padding_channels, 
                                edge_physics_features.size(2), edge_physics_features.size(3), 
                                device=edge_physics_features.device)
            edge_physics_features = torch.cat([edge_physics_features, padding], dim=1)
        
        return edge_physics_features, sharpness_map


class HSVGlassAnalyzer(nn.Module):
    """
    HSV颜色空间分析模块：利用玻璃在HSV空间的特殊性质
    玻璃通常在HSV空间表现为低饱和度、特定的色调分布
    """
    def __init__(self, in_channels):
        super(HSVGlassAnalyzer, self).__init__()
        
        # HSV特征提取
        self.hsv_processor = nn.Sequential(
            nn.Conv2d(3, 32, 3, 1, 1),  # 直接处理RGB转HSV
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )
        
        # 饱和度分析
        self.saturation_analyzer = nn.Sequential(
            nn.Conv2d(64, 32, 3, 1, 1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 1, 1),
            nn.Sigmoid()
        )
        
        # 色调一致性检测 - 添加激活函数和数值稳定性
        self.hue_consistency = nn.Sequential(
            nn.Conv2d(64, 32, 5, 1, 2),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True)  # 添加激活函数确保数值稳定
        )
        
    def rgb_to_hsv_approx(self, rgb):
        """
        近似RGB到HSV的可微分转换 - 增强数值稳定性
        """
        # 确保输入在有效范围内
        rgb = torch.clamp(rgb, min=0.0, max=1.0)
        
        r, g, b = rgb[:, 0:1], rgb[:, 1:2], rgb[:, 2:3]
        
        max_rgb = torch.max(torch.max(r, g), b)
        min_rgb = torch.min(torch.min(r, g), b)
        diff = max_rgb - min_rgb
        
        # 计算饱和度（近似），增加数值稳定性
        saturation = diff / (max_rgb + 1e-7)
        saturation = torch.clamp(saturation, min=0.0, max=1.0)
        
        # 计算亮度
        value = max_rgb
        value = torch.clamp(value, min=0.0, max=1.0)
        
        # 检查结果的数值稳定性
        hsv_result = torch.cat([saturation, value], dim=1)
        if torch.isnan(hsv_result).any() or torch.isinf(hsv_result).any():
            print("警告: HSV转换产生NaN/Inf，使用安全值")
            hsv_result = torch.nan_to_num(hsv_result, nan=0.5, posinf=1.0, neginf=0.0)
        
        return hsv_result
        
    def forward(self, rgb_input, features):
        # 简化的输入处理 - 移除复杂的检查和缩放
        rgb_input = torch.clamp(rgb_input, min=0.0, max=1.0)
        
        # 基本的NaN检查
        if torch.isnan(rgb_input).any() or torch.isinf(rgb_input).any():
            rgb_input = torch.nan_to_num(rgb_input, nan=0.5, posinf=1.0, neginf=0.0)
        
        # 直接处理，不使用梯度检查点
        hsv_features = self.hsv_processor(rgb_input)
        
        # 饱和度分析
        saturation_map = self.saturation_analyzer(hsv_features)
        
        # 色调一致性 - 简化处理
        hue_features = self.hue_consistency(hsv_features)
        
        # 基本的数值检查
        if torch.isnan(hue_features).any() or torch.isinf(hue_features).any():
            hue_features = torch.zeros_like(hue_features)
        
        if torch.isnan(saturation_map).any() or torch.isinf(saturation_map).any():
            saturation_map = torch.zeros_like(saturation_map)
        
        return hue_features, saturation_map


###################################################################
# ############## 物理特性融合模块 #############################
###################################################################

class GlassPhysicsFusion(nn.Module):
    """
    玻璃物理特性融合模块：整合各种物理分析结果
    """
    def __init__(self, reflection_dim, transparency_dim, edge_dim, hsv_dim):
        super(GlassPhysicsFusion, self).__init__()
        
        total_dim = reflection_dim + transparency_dim + edge_dim + hsv_dim
        # 确保输出维度能被4整除，SCSA要求
        output_dim = ((total_dim // 4) // 4) * 4  # 确保是4的倍数
        if output_dim < 4:
            output_dim = 4
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(total_dim, total_dim//2, 3, 1, 1),
            nn.BatchNorm2d(total_dim//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(total_dim//2, output_dim, 3, 1, 1),
            nn.BatchNorm2d(output_dim),
            nn.ReLU(inplace=True)
        )
        
        # 注意力机制
        self.attention = SCSA(
            dim=output_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        # 物理置信度估计
        self.confidence_estimator = nn.Sequential(
            nn.Conv2d(output_dim, output_dim//2, 3, 1, 1),
            nn.BatchNorm2d(output_dim//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(output_dim//2, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, reflection_feat, transparency_feat, edge_feat, hsv_feat):
        # 融合所有物理特征
        fused_features = torch.cat([reflection_feat, transparency_feat, edge_feat, hsv_feat], dim=1)
        
        # 特征融合
        fused_features = self.feature_fusion(fused_features)
        
        # 应用注意力
        attended_features = self.attention(fused_features)
        
        # 物理置信度
        confidence_map = self.confidence_estimator(attended_features)
        
        return attended_features, confidence_map


###################################################################
# ############## 主网络架构 ################################
###################################################################

class GDNetGlassPhysics(nn.Module):
    """
    基于玻璃物理特性的检测网络
    移除HDCN复杂度，专注于玻璃的物理光学特性
    """
    def __init__(self, backbone_path=None, crf_iter=3, trainable_crf=True):
        super(GDNetGlassPhysics, self).__init__()
        
        # 骨干网络
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0
        self.layer1 = resnext.layer1
        self.layer2 = resnext.layer2
        self.layer3 = resnext.layer3
        self.layer4 = resnext.layer4

        # 物理特性分析模块
        self.reflection_analyzer = ReflectionAnalyzer(2048)  # 高层特征
        self.transparency_analyzer = TransparencyAnalyzer(1024)  # 中层特征
        self.edge_analyzer = EdgePhysicsAnalyzer(512)  # 低层特征
        self.hsv_analyzer = HSVGlassAnalyzer(256)  # 浅层特征

        # 简化物理特性融合，直接使用固定维度
        self.physics_fusion = nn.Sequential(
            nn.Conv2d(256, 128, 3, 1, 1),  # 固定输入256维到128维
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, 1, 1),   # 128维到64维，64能被4整除
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )
        
        # 可训练的通道调整层
        self.channel_adapter = nn.Conv2d(1, 256, 1, 1, 0)

        # 多尺度特征融合
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        
        # 固定的通道调整层 - 修复动态创建问题
        self.h5_conv_layer = nn.Conv2d(2048, 512, 1, bias=False)
        self.h4_conv_layer = nn.Conv2d(1024, 256, 1, bias=False)
        self.h3_conv_layer = nn.Conv2d(512, 128, 1, bias=False)
        
        # 使用Xavier初始化
        nn.init.xavier_uniform_(self.h5_conv_layer.weight)
        nn.init.xavier_uniform_(self.h4_conv_layer.weight)
        nn.init.xavier_uniform_(self.h3_conv_layer.weight)
        
        # 主特征融合 - 暂时禁用SCSA，使用简单卷积
        self.main_fusion = nn.Sequential(
            nn.Conv2d(512+256+128, 896, 3, 1, 1),
            nn.BatchNorm2d(896),
            nn.ReLU(inplace=True)
        )

        self.main_fusion_conv = nn.Sequential(
            nn.Conv2d(896, 512, 3, 1, 1),
            nn.BatchNorm2d(512), 
            nn.ReLU()
        )

        # 物理与主特征融合 - 简化为固定维度
        physics_feat_dim = 64  # 从简化的physics_fusion得到
        self.final_fusion = nn.Sequential(
            nn.Conv2d(512 + physics_feat_dim, 320, 3, 1, 1),  # 512+64=576
            nn.BatchNorm2d(320),
            nn.ReLU(),
            nn.Conv2d(320, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU()
        )

        # 预测头
        self.main_predict = nn.Conv2d(512, 1, 3, 1, 1)
        self.physics_predict = nn.Conv2d(physics_feat_dim, 1, 3, 1, 1)
        self.final_predict = nn.Conv2d(256, 1, 3, 1, 1)

        # CRF后处理
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=8.0,
            gaussian_weight=4.0,
            bilateral_spatial_sigma=35.0,
            bilateral_color_sigma=2.5,
            gaussian_sigma=1.2,
            trainable=trainable_crf
        )

        # 自适应权重
        self.adaptive_weights = nn.Parameter(torch.tensor([0.4, 0.3, 0.3]), requires_grad=True)

        for m in self.modules():
            if isinstance(m, nn.ReLU):
                m.inplace = True

    def forward(self, x):
        # 提取骨干特征
        layer0 = self.layer0(x)  # [B, 64, H/2, W/2]
        layer1 = self.layer1(layer0)  # [B, 256, H/4, W/4]
        layer2 = self.layer2(layer1)  # [B, 512, H/8, W/8]
        layer3 = self.layer3(layer2)  # [B, 1024, H/16, W/16]
        layer4 = self.layer4(layer3)  # [B, 2048, H/32, W/32]

        # 物理特性分析
        reflection_feat, luminance_map = self.reflection_analyzer(layer4)
        transparency_feat, transparency_map = self.transparency_analyzer(layer3)
        edge_feat, sharpness_map = self.edge_analyzer(layer2, x)
        hsv_feat, saturation_map = self.hsv_analyzer(x, layer1)

        # 上采样物理特征到统一尺度（以layer3的尺度为准）
        target_size = layer3.shape[2:]
        reflection_feat_up = F.interpolate(reflection_feat, size=target_size, mode='bilinear', align_corners=True)
        edge_feat_up = F.interpolate(edge_feat, size=target_size, mode='bilinear', align_corners=True)
        hsv_feat_up = F.interpolate(hsv_feat, size=target_size, mode='bilinear', align_corners=True)

        # 物理特性融合 - 简化版本
        # 使用平均池化将所有特征统一到相同尺寸，然后取平均作为简单融合
        reflection_pooled = F.adaptive_avg_pool2d(reflection_feat_up, target_size)
        transparency_pooled = F.adaptive_avg_pool2d(transparency_feat, target_size)  
        edge_pooled = F.adaptive_avg_pool2d(edge_feat_up, target_size)
        hsv_pooled = F.adaptive_avg_pool2d(hsv_feat_up, target_size)
        
        # 取所有物理特征的平均，然后通过channel_adapter调整到256通道
        avg_physics = (reflection_pooled.mean(dim=1, keepdim=True) + 
                      transparency_pooled.mean(dim=1, keepdim=True) + 
                      edge_pooled.mean(dim=1, keepdim=True) + 
                      hsv_pooled.mean(dim=1, keepdim=True)) / 4
        
        # 通过可训练的适配器调整到256通道
        physics_input = self.channel_adapter(avg_physics)
        
        # 通过物理特性融合网络
        physics_features = self.physics_fusion(physics_input)
        confidence_map = torch.mean(physics_features, dim=1, keepdim=True).sigmoid()

        # 主特征融合（简化的LCFI替代）
        h5_conv = F.adaptive_avg_pool2d(layer4, target_size)  # 简化处理
        h4_conv = layer3
        h3_conv = F.interpolate(layer2, size=target_size, mode='bilinear', align_corners=True)
        
        # 通道调整 - 使用预定义的卷积层
        h5_conv = self.h5_conv_layer(h5_conv)
        h4_conv = self.h4_conv_layer(h4_conv)
        h3_conv = self.h3_conv_layer(h3_conv)
        
        main_features = torch.cat([h5_conv, h4_conv, h3_conv], dim=1)
        main_features = self.main_fusion(main_features)
        main_features = self.main_fusion_conv(main_features)

        # 上采样物理特征到主特征尺度
        physics_features_up = F.interpolate(physics_features, size=main_features.shape[2:], 
                                          mode='bilinear', align_corners=True)

        # 最终融合
        final_features = torch.cat([main_features, physics_features_up], dim=1)
        final_features = self.final_fusion(final_features)

        # 预测
        main_pred = self.main_predict(main_features)
        physics_pred = self.physics_predict(physics_features)
        final_pred = self.final_predict(final_features)

        # 上采样到原始尺寸
        main_pred = F.interpolate(main_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        physics_pred = F.interpolate(physics_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        final_pred = F.interpolate(final_pred, size=x.size()[2:], mode='bilinear', align_corners=True)

        # 应用sigmoid
        main_pred_prob = torch.sigmoid(main_pred)
        physics_pred_prob = torch.sigmoid(physics_pred)
        final_pred_prob = torch.sigmoid(final_pred)

        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = (weights[0] * main_pred_prob + 
                        weights[1] * physics_pred_prob + 
                        weights[2] * final_pred_prob)
        
        ensemble_pred = torch.clamp(ensemble_pred, min=1e-7, max=1.0 - 1e-7)

        # CRF后处理
        bg_logits = torch.log(1 - ensemble_pred)
        fg_logits = torch.log(ensemble_pred)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # 归一化输入图像
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)

        # 应用CRF
        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - ensemble_pred, ensemble_pred], dim=1)

        return {
            'main_pred': main_pred_prob,
            'physics_pred': physics_pred_prob,
            'final_pred': final_pred_prob,
            'ensemble_pred': ensemble_pred,
            'refined_pred': refined_predict,
            'physics_maps': {
                'luminance': F.interpolate(luminance_map, size=x.size()[2:], mode='bilinear', align_corners=True),
                'transparency': F.interpolate(transparency_map, size=x.size()[2:], mode='bilinear', align_corners=True),
                'sharpness': F.interpolate(sharpness_map, size=x.size()[2:], mode='bilinear', align_corners=True),
                'saturation': F.interpolate(saturation_map, size=x.size()[2:], mode='bilinear', align_corners=True),
                'confidence': F.interpolate(confidence_map, size=x.size()[2:], mode='bilinear', align_corners=True)
            }
        }


def create_glass_physics_model(backbone_path=None, crf_iter=3, trainable_crf=True):
    """创建基于玻璃物理特性的模型"""
    return GDNetGlassPhysics(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )