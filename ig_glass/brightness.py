import os
import cv2
import numpy as np

# 动态亮度调整目标范围
BRIGHTNESS_TARGET_MIN = 100
BRIGHTNESS_TARGET_MAX = 120


def adjust_image_brightness(image, adjustment):
    """根据调整值调整图像亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    v = np.clip(v.astype(np.int32) + int(adjustment), 0, 255).astype(np.uint8)
    hsv = cv2.merge((h, s, v))
    return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)


def adjust_image_brightness_dynamically(image, target_min, target_max):
    """根据图像亮度的分布动态调整亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    v = hsv[:, :, 2]  # 提取亮度通道

    # 计算亮度的直方图
    hist = cv2.calcHist([v], [0], None, [256], [0, 256])
    cumulative_sum = np.cumsum(hist)
    median_brightness = np.searchsorted(cumulative_sum, cumulative_sum[-1] // 2)

    # 动态目标亮度，基于图像的亮度分布
    current_mean = np.mean(v)
    current_std = np.std(v)

    # 根据当前亮度的均值和标准差，动态调整目标亮度
    if current_mean < target_min:
        adjustment = target_min - median_brightness
    elif current_mean > target_max:
        adjustment = target_max - median_brightness
    else:
        adjustment = (target_max + target_min) / 2 - median_brightness

    # 调整图像亮度
    adjusted_image = adjust_image_brightness(image, adjustment)

    return adjusted_image


def process_images(input_folder, output_folder):
    """读取输入文件夹中的图像，动态调整亮度并保存到输出文件夹"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(input_folder) if os.path.isfile(os.path.join(input_folder, f))]

    for image_file in image_files:
        image_path = os.path.join(input_folder, image_file)
        output_path = os.path.join(output_folder, image_file)

        # 读取图像
        original_image = cv2.imread(image_path)

        if original_image is None:
            print(f"无法读取图像: {image_path}")
            continue

        # 动态调整亮度
        adjusted_image = adjust_image_brightness_dynamically(original_image, BRIGHTNESS_TARGET_MIN, BRIGHTNESS_TARGET_MAX)

        # 保存调整后的图像
        cv2.imwrite(output_path, adjusted_image)

        print(f"处理完成: {image_file} -> {output_path}")


def main():
    # 输入和输出文件夹路径
    input_folder = "/home/<USER>/test/room"  # 修改为您的输入文件夹路径
    output_folder = "/home/<USER>/test/room_brightness"  # 修改为您的输出文件夹路径

    # 处理图像，动态调整亮度
    process_images(input_folder, output_folder)


if __name__ == '__main__':
    main()
