import os
import cv2
import numpy as np
import pandas as pd  # 用于保存和读取数据
import matplotlib.pyplot as plt
from openpyxl import Workbook  # 用于生成 Excel 文件

# ORB-SLAM3 功能模块（部分来自 Light.py）
def calculate_brightness(image_path):
    """计算场景亮度"""
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])  # V 通道表示亮度
    return brightness

def calculate_feature_points(image_path):
    """计算场景特征点数"""
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    orb = cv2.ORB_create(nfeatures=15000)  # 最大检测 10000 个特征点
    keypoints = orb.detect(image, None)
    return len(keypoints)

def process_orb_slam3(image_folder, output_file):
    """处理 ORB-SLAM3 的亮度和特征点数，保存到 Excel 文件"""
    data = []
    image_files = sorted([f for f in os.listdir(image_folder) if f.endswith('.jpg')])
    
    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)
        brightness = calculate_brightness(image_path)
        feature_points = calculate_feature_points(image_path)
        data.append([image_file, brightness, feature_points])
    
    # 保存到 Excel 文件
    df = pd.DataFrame(data, columns=['Image', 'Brightness', 'Feature Points'])
    df.to_excel(output_file, index=False)
    print(f"ORB-SLAM3 数据已保存到 {output_file}")

# IG-SLAM 功能模块（部分来自 main.py）
def adjust_image_brightness(image, target_min, target_max):
    """动态调整图像亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    v = hsv[:, :, 2]
    median_brightness = np.median(v)
    
    if median_brightness < target_min:
        adjustment = target_min - median_brightness
    elif median_brightness > target_max:
        adjustment = target_max - median_brightness
    else:
        adjustment = (target_max + target_min) / 2 - median_brightness
    
    v = np.clip(v + adjustment, 0, 255).astype(np.uint8)
    hsv[:, :, 2] = v
    return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

def process_ig_slam(image_folder, output_file, target_min=100, target_max=120):
    """处理 IG-SLAM 的亮度调整和特征点数，保存到 Excel 文件"""
    data = []
    image_files = sorted([f for f in os.listdir(image_folder) if f.endswith('.jpg')])
    
    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)
        image = cv2.imread(image_path)
        
        # 调整亮度
        adjusted_image = adjust_image_brightness(image, target_min, target_max)
        adjusted_brightness = np.mean(cv2.cvtColor(adjusted_image, cv2.COLOR_BGR2HSV)[:, :, 2])
        
        # 计算调整后的特征点数
        temp_path = "temp.jpg"
        cv2.imwrite(temp_path, adjusted_image)
        feature_points = calculate_feature_points(temp_path)
        os.remove(temp_path)
        
        data.append([image_file, adjusted_brightness, feature_points])
    
    # 保存到 Excel 文件
    df = pd.DataFrame(data, columns=['Image', 'Brightness', 'Feature Points'])
    df.to_excel(output_file, index=False)
    print(f"IG-SLAM 数据已保存到 {output_file}")

# 对照图生成模块（来自 compare.py）
def plot_comparison(orb_file, ig_file, output_folder):
    """生成 ORB-SLAM3 和 IG-SLAM 的对照图"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 读取数据
    orb_data = pd.read_excel(orb_file)
    ig_data = pd.read_excel(ig_file)
    
    orb_brightness = orb_data['Brightness']
    orb_features = orb_data['Feature Points']
    ig_brightness = ig_data['Brightness']
    ig_features = ig_data['Feature Points']
    
    plt.figure(figsize=(20, 15))

    # 1. 散点图
    plt.figure(figsize=(10, 6))
    plt.scatter(orb_brightness, orb_features, color="blue", label="ORB-SLAM3")
    plt.scatter(ig_brightness, ig_features, color="red", label="IG-SLAM")
    plt.xlabel("Brightness")
    plt.ylabel("Feature Points")
    plt.title("Feature Points vs Brightness (ORB-SLAM3 vs IG-SLAM)")
    plt.legend()
    plt.grid()
    plt.savefig(os.path.join(output_folder, "Scatter_Plot.png"))
    plt.close()

    # 2. 回归曲线
    plt.figure(figsize=(10, 6))
    coef_orb = np.polyfit(orb_brightness, orb_features, 2)
    coef_ig = np.polyfit(ig_brightness, ig_features, 2)
    orb_poly = np.poly1d(coef_orb)
    ig_poly = np.poly1d(coef_ig)
    x = np.linspace(min(orb_brightness.min(), ig_brightness.min()), max(orb_brightness.max(), ig_brightness.max()), 100)
    plt.plot(x, orb_poly(x), color="blue", label="ORB-SLAM3 Regression")
    plt.plot(x, ig_poly(x), color="red", label="IG-SLAM Regression")
    plt.scatter(orb_brightness, orb_features, color="blue", alpha=0.5)
    plt.scatter(ig_brightness, ig_features, color="red", alpha=0.5)
    plt.xlabel("Brightness")
    plt.ylabel("Feature Points")
    plt.title("Regression Curve Comparison")
    plt.legend()
    plt.grid()
    plt.savefig(os.path.join(output_folder, "Regression_Curve.png"))
    plt.close()

    # 3. 差值曲线
    plt.figure(figsize=(10, 6))
    brightness_common = sorted(set(orb_brightness) & set(ig_brightness))
    orb_features_interpolated = np.interp(brightness_common, orb_brightness, orb_features)
    ig_features_interpolated = np.interp(brightness_common, ig_brightness, ig_features)
    feature_difference = np.array(ig_features_interpolated) - np.array(orb_features_interpolated)
    plt.plot(brightness_common, feature_difference, color="green", label="IG-SLAM - ORB-SLAM3")
    plt.axhline(0, color="red", linestyle="--")
    plt.xlabel("Brightness")
    plt.ylabel("Feature Points Difference")
    plt.title("Feature Points Difference (IG-SLAM - ORB-SLAM3)")
    plt.legend()
    plt.grid()
    plt.savefig(os.path.join(output_folder, "Feature_Difference.png"))
    plt.close()

    # 保存对照图
    plt.tight_layout()
    output_path = os.path.join(output_folder)
    plt.savefig(output_path)
    print(f"对照图已保存到 {output_path}")

# 主函数
def main():
    # 路径配置
    image_folder = "/home/<USER>/orb_slam3_ws/src/SLAM/Task/jpg"  # 原始数据集路径
    orb_output_file = "/home/<USER>/orb_slam3_ws/src/SLAM/Task/Comparison_Results/orb.xlsx"             # ORB-SLAM3 输出文件
    ig_output_file = "/home/<USER>/orb_slam3_ws/src/SLAM/Task/Comparison_Results/ig.xlsx"               # IG-SLAM 输出文件
    comparison_output_folder = "/home/<USER>/orb_slam3_ws/src/SLAM/Task/Comparison_Results"  # 对照图输出文件夹

    # Step 1: ORB-SLAM3 数据处理
    process_orb_slam3(image_folder, orb_output_file)

    # Step 2: IG-SLAM 数据处理
    process_ig_slam(image_folder, ig_output_file)

    # Step 3: 生成对照图
    plot_comparison(orb_output_file, ig_output_file, comparison_output_folder)

if __name__ == "__main__":
    main()
