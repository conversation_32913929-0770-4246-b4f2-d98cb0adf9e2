"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : physics_lite_enhancer.py
 @Function: 轻量化物理特性增强器 - 只保留最有效的物理特性

基于Glass Physics (85.63% IoU) 的经验，提取最有价值的物理特性：
1. 边缘检测增强 (最有效)
2. 简化的透明度分析 (有一定效果)
3. 移除复杂的HSV和反射分析 (数值不稳定)

目标：在SCSA基础上增加2-3%的IoU提升，达到90%+
"""
import torch
import torch.nn as nn
import torch.nn.functional as F


class LightweightEdgeEnhancer(nn.Module):
    """
    轻量化边缘增强器
    只保留最有效的边缘检测方法，避免复杂的物理建模
    """
    def __init__(self, in_channels):
        super(LightweightEdgeEnhancer, self).__init__()
        
        # 固定的边缘检测核 - 避免训练不稳定
        self.register_buffer('sobel_x', torch.tensor([
            [-1, 0, 1], [-2, 0, 2], [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y', torch.tensor([
            [-1, -2, -1], [0, 0, 0], [1, 2, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('laplacian', torch.tensor([
            [0, -1, 0], [-1, 4, -1], [0, -1, 0]
        ]).float().view(1, 1, 3, 3))
        
        # 轻量化边缘处理网络
        self.edge_processor = nn.Sequential(
            nn.Conv2d(3, 16, 3, 1, 1),  # 3个边缘响应 -> 16通道
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, 3, 1, 1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 1),
            nn.Sigmoid()
        )
        
        # 特征增强
        self.feature_enhancer = nn.Sequential(
            nn.Conv2d(in_channels + 1, in_channels, 3, 1, 1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, features, rgb_input=None):
        """
        Args:
            features: CNN特征 [B, C, H, W]
            rgb_input: 原始RGB输入 [B, 3, H, W] (可选)
        Returns:
            enhanced_features: 边缘增强后的特征
            edge_map: 边缘响应图
        """
        if rgb_input is not None:
            # 转换为灰度
            gray = torch.mean(rgb_input, dim=1, keepdim=True)
            
            # 应用边缘检测核
            sobel_x_resp = F.conv2d(gray, self.sobel_x, padding=1)
            sobel_y_resp = F.conv2d(gray, self.sobel_y, padding=1)
            laplacian_resp = F.conv2d(gray, self.laplacian, padding=1)
            
            # 计算边缘强度
            sobel_magnitude = torch.sqrt(sobel_x_resp**2 + sobel_y_resp**2)
            
            # 组合边缘响应
            edge_responses = torch.cat([sobel_magnitude, laplacian_resp, gray], dim=1)
            
            # 处理边缘响应
            edge_map = self.edge_processor(edge_responses)
            
            # 调整到特征尺寸
            if edge_map.shape[2:] != features.shape[2:]:
                edge_map = F.interpolate(edge_map, size=features.shape[2:], 
                                       mode='bilinear', align_corners=True)
        else:
            # 如果没有RGB输入，创建默认的边缘图
            edge_map = torch.ones(features.size(0), 1, features.size(2), features.size(3), 
                                device=features.device)
        
        # 特征增强
        enhanced_input = torch.cat([features, edge_map], dim=1)
        enhanced_features = self.feature_enhancer(enhanced_input)
        
        return enhanced_features, edge_map


class SimpleTransparencyAnalyzer(nn.Module):
    """
    简化的透明度分析器
    只保留最核心的透明度检测功能
    """
    def __init__(self, in_channels):
        super(SimpleTransparencyAnalyzer, self).__init__()
        
        # 背景一致性检测 - 简化版本
        self.consistency_detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 4, 5, 1, 2),
            nn.BatchNorm2d(in_channels // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 4, 1, 1),
            nn.Sigmoid()
        )
        
        # 特征增强
        self.feature_enhancer = nn.Sequential(
            nn.Conv2d(in_channels + 1, in_channels, 3, 1, 1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, features):
        """
        Args:
            features: CNN特征 [B, C, H, W]
        Returns:
            enhanced_features: 透明度增强后的特征
            transparency_map: 透明度响应图
        """
        # 透明度检测
        transparency_map = self.consistency_detector(features)
        
        # 特征增强
        enhanced_input = torch.cat([features, transparency_map], dim=1)
        enhanced_features = self.feature_enhancer(enhanced_input)
        
        return enhanced_features, transparency_map


class PhysicsLiteEnhancer(nn.Module):
    """
    轻量化物理特性增强器
    结合SCSA的优势，添加最有效的物理特性增强
    """
    def __init__(self, feature_channels=[512, 1024, 2048]):
        super(PhysicsLiteEnhancer, self).__init__()
        
        # 为不同层级的特征创建增强器
        self.edge_enhancers = nn.ModuleList([
            LightweightEdgeEnhancer(channels) for channels in feature_channels
        ])
        
        self.transparency_enhancers = nn.ModuleList([
            SimpleTransparencyAnalyzer(channels) for channels in feature_channels
        ])
        
        # 物理特性融合权重 - 可学习的
        self.fusion_weights = nn.ParameterList([
            nn.Parameter(torch.tensor([0.7, 0.2, 0.1])) for _ in feature_channels
        ])
    
    def forward(self, feature_list, rgb_input=None):
        """
        Args:
            feature_list: 多层CNN特征列表 [feat1, feat2, feat3]
            rgb_input: 原始RGB输入 (可选)
        Returns:
            enhanced_features: 物理增强后的特征列表
            physics_maps: 物理响应图字典
        """
        enhanced_features = []
        edge_maps = []
        transparency_maps = []
        
        for i, features in enumerate(feature_list):
            # 边缘增强
            edge_enhanced, edge_map = self.edge_enhancers[i](features, rgb_input)
            
            # 透明度增强
            transparency_enhanced, transparency_map = self.transparency_enhancers[i](features)
            
            # 融合增强特征
            weights = F.softmax(self.fusion_weights[i], dim=0)
            final_enhanced = (weights[0] * features + 
                            weights[1] * edge_enhanced + 
                            weights[2] * transparency_enhanced)
            
            enhanced_features.append(final_enhanced)
            edge_maps.append(edge_map)
            transparency_maps.append(transparency_map)
        
        physics_maps = {
            'edge_maps': edge_maps,
            'transparency_maps': transparency_maps
        }
        
        return enhanced_features, physics_maps


class SCSAWithPhysicsLite(nn.Module):
    """
    SCSA + 轻量化物理特性增强
    在SCSA基础上添加最有效的物理特性，目标90%+ IoU
    """
    def __init__(self, scsa_model, feature_channels=[512, 1024, 2048]):
        super(SCSAWithPhysicsLite, self).__init__()
        
        # 保持原有的SCSA模型结构
        self.scsa_model = scsa_model
        
        # 添加轻量化物理增强器
        self.physics_enhancer = PhysicsLiteEnhancer(feature_channels)
        
        # 物理特性权重 - 控制物理增强的强度
        self.physics_weight = nn.Parameter(torch.tensor(0.1), requires_grad=True)
        
        # 冻结SCSA的主要参数，只训练物理增强部分
        self._freeze_scsa_backbone()
    
    def _freeze_scsa_backbone(self):
        """冻结SCSA的主要参数，只训练物理增强部分"""
        for name, param in self.scsa_model.named_parameters():
            if 'layer' in name:  # 冻结backbone
                param.requires_grad = False
            # 保持SCSA注意力和预测头可训练
    
    def forward(self, x):
        """
        前向传播：在SCSA基础上添加物理增强
        """
        # 提取SCSA的中间特征
        layer0 = self.scsa_model.layer0(x)
        layer1 = self.scsa_model.layer1(layer0)
        layer2 = self.scsa_model.layer2(layer1)
        layer3 = self.scsa_model.layer3(layer2)
        layer4 = self.scsa_model.layer4(layer3)
        
        # 物理特性增强
        feature_list = [layer2, layer3, layer4]
        enhanced_features, physics_maps = self.physics_enhancer(feature_list, x)
        
        # 将增强后的特征重新组装
        enhanced_layer2, enhanced_layer3, enhanced_layer4 = enhanced_features
        
        # 使用增强后的特征继续SCSA的处理流程
        # 这里需要根据具体的SCSA实现来调整
        # 假设SCSA有类似的特征融合过程
        
        # 简化处理：直接使用物理权重混合原始和增强特征
        mixed_layer2 = (1 - self.physics_weight) * layer2 + self.physics_weight * enhanced_layer2
        mixed_layer3 = (1 - self.physics_weight) * layer3 + self.physics_weight * enhanced_layer3
        mixed_layer4 = (1 - self.physics_weight) * layer4 + self.physics_weight * enhanced_layer4
        
        # 继续SCSA的后续处理（这里需要根据实际SCSA实现调整）
        # 暂时返回基本结果
        return {
            'enhanced_features': enhanced_features,
            'physics_maps': physics_maps,
            'physics_weight': self.physics_weight
        }


def create_scsa_with_physics_lite(scsa_model_path, feature_channels=[512, 1024, 2048]):
    """
    创建SCSA + 轻量化物理增强模型
    
    Args:
        scsa_model_path: SCSA模型权重路径
        feature_channels: 特征通道数列表
    
    Returns:
        增强后的模型
    """
    # 加载SCSA模型
    from gdnet_scsa import GDNetSCSA
    scsa_model = GDNetSCSA()
    
    if scsa_model_path:
        checkpoint = torch.load(scsa_model_path, map_location='cpu')
        scsa_model.load_state_dict(checkpoint)
    
    # 创建增强模型
    enhanced_model = SCSAWithPhysicsLite(scsa_model, feature_channels)
    
    return enhanced_model


# 使用示例
if __name__ == "__main__":
    # 测试轻量化物理增强器
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试数据
    batch_size = 2
    rgb_input = torch.randn(batch_size, 3, 416, 416).to(device)
    feature_list = [
        torch.randn(batch_size, 512, 52, 52).to(device),
        torch.randn(batch_size, 1024, 26, 26).to(device),
        torch.randn(batch_size, 2048, 13, 13).to(device)
    ]
    
    # 创建增强器
    enhancer = PhysicsLiteEnhancer().to(device)
    
    # 测试
    enhanced_features, physics_maps = enhancer(feature_list, rgb_input)
    
    print("✅ 轻量化物理增强器测试成功")
    print(f"   输入特征数量: {len(feature_list)}")
    print(f"   输出特征数量: {len(enhanced_features)}")
    print(f"   物理响应图: {list(physics_maps.keys())}")
    
    # 计算参数量
    total_params = sum(p.numel() for p in enhancer.parameters())
    print(f"   参数量: {total_params:,} ({total_params/1e6:.2f}M)")
