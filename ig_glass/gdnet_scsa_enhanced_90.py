"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_scsa_enhanced_90.py
 @Function: 基于87.95% IoU的SCSA优化版本，目标突破90% IoU

核心优化策略：
1. 增强SCSA注意力机制 - 更强的边缘感知
2. 改进特征融合策略 - 更精确的多尺度融合
3. 优化损失函数权重 - 边缘感知损失
4. 数值稳定性增强 - 避免梯度消失
5. 更精确的CRF后处理 - 7次迭代
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF
from ig_glass.attention_scsa import SCSA


class EnhancedEdgeDetector(nn.Module):
    """
    增强边缘检测模块 - 专门针对玻璃边缘优化
    """
    def __init__(self, in_channels):
        super(EnhancedEdgeDetector, self).__init__()
        
        # 多尺度Sobel边缘检测
        self.register_buffer('sobel_x_3', torch.tensor([
            [-1, 0, 1], 
            [-2, 0, 2], 
            [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y_3', torch.tensor([
            [-1, -2, -1], 
            [ 0,  0,  0], 
            [ 1,  2,  1]
        ]).float().view(1, 1, 3, 3))
        
        # 5x5 Sobel for better edge detection
        self.register_buffer('sobel_x_5', torch.tensor([
            [-1, -2, 0, 2, 1],
            [-4, -8, 0, 8, 4],
            [-6, -12, 0, 12, 6],
            [-4, -8, 0, 8, 4],
            [-1, -2, 0, 2, 1]
        ]).float().view(1, 1, 5, 5) / 100.0)
        
        self.register_buffer('sobel_y_5', torch.tensor([
            [-1, -4, -6, -4, -1],
            [-2, -8, -12, -8, -2],
            [0, 0, 0, 0, 0],
            [2, 8, 12, 8, 2],
            [1, 4, 6, 4, 1]
        ]).float().view(1, 1, 5, 5) / 100.0)
        
        # 边缘融合网络
        self.edge_fusion = nn.Sequential(
            nn.Conv2d(2, 32, 3, 1, 1),  # 融合x和y梯度
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 1),
            nn.Sigmoid()
        )
        
        # 边缘增强权重
        self.edge_weight = nn.Parameter(torch.tensor(2.0), requires_grad=True)
        
    def forward(self, x):
        # 转换为灰度
        if x.size(1) > 1:
            gray = torch.mean(x, dim=1, keepdim=True)
        else:
            gray = x
            
        # 3x3 Sobel
        sobel_x_3 = F.conv2d(gray, self.sobel_x_3, padding=1)
        sobel_y_3 = F.conv2d(gray, self.sobel_y_3, padding=1)
        
        # 5x5 Sobel
        sobel_x_5 = F.conv2d(gray, self.sobel_x_5, padding=2)
        sobel_y_5 = F.conv2d(gray, self.sobel_y_5, padding=2)
        
        # 组合梯度
        gradient_x = sobel_x_3 + sobel_x_5
        gradient_y = sobel_y_3 + sobel_y_5
        
        # 计算边缘强度
        edge_magnitude = torch.sqrt(gradient_x**2 + gradient_y**2 + 1e-8)
        
        # 融合梯度信息
        gradient_stack = torch.cat([gradient_x, gradient_y], dim=1)
        edge_map = self.edge_fusion(gradient_stack)
        
        # 应用可学习的边缘增强权重
        enhanced_edge = edge_map * self.edge_weight
        
        return enhanced_edge, edge_magnitude


class SuperLCFI(nn.Module):
    """
    超级LCFI模块 - 基于87.95% IoU的成功经验进一步优化
    """
    def __init__(self, input_channels, dr1=1, dr2=2, dr3=3, dr4=4):
        super(SuperLCFI, self).__init__()
        self.input_channels = input_channels
        self.channels_single = input_channels // 4
        self.channels_double = input_channels // 2
        
        # 更强的通道降维
        self.channel_reduction = nn.Sequential(
            nn.Conv2d(input_channels, self.channels_single, 1),
            nn.BatchNorm2d(self.channels_single),
            nn.ReLU(inplace=True)
        )
        
        # 多尺度扩张卷积分支
        self.branch1 = self._make_branch(self.channels_single, dr1, 3)
        self.branch2 = self._make_branch(self.channels_single, dr2, 5)  
        self.branch3 = self._make_branch(self.channels_single, dr3, 7)
        self.branch4 = self._make_branch(self.channels_single, dr4, 9)
        
        # 注意力加权融合
        self.attention_weights = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(self.channels_single * 4, self.channels_single, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.channels_single, 4, 1),
            nn.Softmax(dim=1)
        )
        
        # SCSA注意力
        self.scsa = SCSA(
            dim=self.channels_single * 4,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        # 输出投影
        self.output_proj = nn.Sequential(
            nn.Conv2d(self.channels_single * 4, input_channels // 2, 1),
            nn.BatchNorm2d(input_channels // 2),
            nn.ReLU(inplace=True)
        )
        
    def _make_branch(self, channels, dilation, kernel_size):
        padding = (kernel_size // 2) * dilation
        return nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size, 1, padding, 
                     dilation=dilation, groups=channels),
            nn.Conv2d(channels, channels, 1),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        # 通道降维
        x_reduced = self.channel_reduction(x)
        
        # 多尺度特征提取
        b1 = self.branch1(x_reduced)
        b2 = self.branch2(x_reduced)
        b3 = self.branch3(x_reduced)
        b4 = self.branch4(x_reduced)
        
        # 特征堆叠
        features = torch.cat([b1, b2, b3, b4], dim=1)
        
        # 注意力加权
        weights = self.attention_weights(features)
        weighted_features = features * weights
        
        # SCSA注意力
        attended_features = self.scsa(weighted_features)
        
        # 输出投影
        output = self.output_proj(attended_features)
        
        return output


class GDNetSCSAEnhanced90(nn.Module):
    """
    基于87.95% IoU的SCSA增强版本，目标突破90% IoU
    """
    def __init__(self, backbone_path=None, crf_iter=7, trainable_crf=True):
        super(GDNetSCSAEnhanced90, self).__init__()
        
        # 骨干网络 - 使用成功的ResNeXt101
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0
        self.layer1 = resnext.layer1
        self.layer2 = resnext.layer2
        self.layer3 = resnext.layer3
        self.layer4 = resnext.layer4

        # 超级LCFI模块
        self.h5_conv = SuperLCFI(2048, 1, 2, 3, 4)  # 输出1024维
        self.h4_conv = SuperLCFI(1024, 1, 2, 3, 4)  # 输出512维
        self.h3_conv = SuperLCFI(512, 1, 2, 3, 4)   # 输出256维
        self.l2_conv = SuperLCFI(256, 1, 2, 3, 4)   # 输出128维

        # 增强边缘检测器
        self.edge_detector = EnhancedEdgeDetector(512)  # 在h3层应用
        
        # 多尺度特征融合
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        
        # 计算融合后的总维度：1024 + 512 + 256 = 1792
        fusion_dim = 1792
        
        # 增强SCSA注意力
        self.h_fusion = SCSA(
            dim=fusion_dim,
            head_num=16,  # 增加注意力头数
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=True,  # 启用bias
            attn_drop_ratio=0.05,  # 降低dropout
            channel_weight=1.5,  # 增加通道权重
            gate_layer='sigmoid'
        )
        
        # 特征融合后的卷积 - 更深的网络
        self.h_fusion_conv = nn.Sequential(
            nn.Conv2d(fusion_dim, 1024, 3, 1, 1),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 768, 3, 1, 1),
            nn.BatchNorm2d(768),
            nn.ReLU(inplace=True),
            nn.Conv2d(768, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )

        # 低层特征处理 - 增强边缘信息
        self.l2_fusion = nn.Sequential(
            nn.Conv2d(128 + 1, 96, 3, 1, 1),  # 128 + 1(edge)
            nn.BatchNorm2d(96),
            nn.ReLU(inplace=True),
            nn.Conv2d(96, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )

        # 预测头
        self.predict_h = nn.Conv2d(512, 1, 3, 1, 1)
        self.predict_l = nn.Conv2d(64, 1, 3, 1, 1)
        self.predict_edge = nn.Conv2d(1, 1, 3, 1, 1)

        # 优化的CRF - 更多迭代次数
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=12.0,     # 增加双边权重
            gaussian_weight=6.0,       # 增加高斯权重
            bilateral_spatial_sigma=45.0,  # 优化空间sigma
            bilateral_color_sigma=3.5,     # 优化颜色sigma
            gaussian_sigma=1.8,            # 优化高斯sigma
            trainable=trainable_crf
        )

        # 精细调整的自适应权重
        self.adaptive_weights = nn.Parameter(
            torch.tensor([0.5, 0.3, 0.2]), requires_grad=True
        )
        
        # 边缘增强模块 - 更强的边缘感知
        self.edge_enhance = nn.Sequential(
            nn.Conv2d(1, 32, 3, 1, 1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 3, 1, 1),
            nn.Sigmoid()
        )

        # 数值稳定性参数
        self.eps = 1e-8

        # 权重初始化
        self._init_weights()

    def _init_weights(self):
        """Kaiming初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # 提取多层特征
        layer0 = self.layer0(x)      # [B, 64, H/2, W/2]
        layer1 = self.layer1(layer0)  # [B, 256, H/4, W/4]
        layer2 = self.layer2(layer1)  # [B, 512, H/8, W/8]
        layer3 = self.layer3(layer2)  # [B, 1024, H/16, W/16]
        layer4 = self.layer4(layer3)  # [B, 2048, H/32, W/32]

        # SuperLCFI特征提取
        h5_conv = self.h5_conv(layer4)  # [B, 1024, H/32, W/32]
        h4_conv = self.h4_conv(layer3)  # [B, 512, H/16, W/16]
        h3_conv = self.h3_conv(layer2)  # [B, 256, H/8, W/8]
        l2_conv = self.l2_conv(layer1)  # [B, 128, H/4, W/4]

        # 增强边缘检测
        edge_map, edge_magnitude = self.edge_detector(layer2)  # 在h3层检测边缘
        
        # 高层特征融合
        h5_up = self.h5_up(h5_conv)     # [B, 1024, H/16, W/16]
        h3_down = self.h3_down(h3_conv) # [B, 256, H/16, W/16]
        
        # 融合高层特征
        h_fused = torch.cat([h5_up, h4_conv, h3_down], dim=1)  # [B, 1792, H/16, W/16]
        
        # 应用增强SCSA注意力
        h_attended = self.h_fusion(h_fused)
        h_final = self.h_fusion_conv(h_attended)  # [B, 512, H/16, W/16]

        # 低层特征处理 - 融合边缘信息
        edge_map_resized = F.interpolate(edge_map, size=l2_conv.size()[2:], 
                                       mode='bilinear', align_corners=True)
        l_input = torch.cat([l2_conv, edge_map_resized], dim=1)
        l_final = self.l2_fusion(l_input)  # [B, 64, H/4, W/4]

        # 生成预测
        pred_h = self.predict_h(h_final)     # [B, 1, H/16, W/16]
        pred_l = self.predict_l(l_final)     # [B, 1, H/4, W/4]
        pred_edge = self.predict_edge(edge_map)  # [B, 1, H/8, W/8]

        # 上采样到原始尺寸
        pred_h = F.interpolate(pred_h, size=x.size()[2:], mode='bilinear', align_corners=True)
        pred_l = F.interpolate(pred_l, size=x.size()[2:], mode='bilinear', align_corners=True)
        pred_edge = F.interpolate(pred_edge, size=x.size()[2:], mode='bilinear', align_corners=True)

        # 应用sigmoid - 数值稳定性
        pred_h_prob = torch.sigmoid(pred_h)
        pred_l_prob = torch.sigmoid(pred_l)
        pred_edge_prob = torch.sigmoid(pred_edge)

        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = (weights[0] * pred_h_prob + 
                        weights[1] * pred_l_prob + 
                        weights[2] * pred_edge_prob)
        
        # 边缘增强
        edge_enhanced = self.edge_enhance(ensemble_pred)
        final_pred = ensemble_pred * (1 + 0.5 * edge_enhanced)
        final_pred = torch.clamp(final_pred, min=self.eps, max=1.0 - self.eps)

        # CRF后处理
        bg_logits = torch.log(1 - final_pred + self.eps)
        fg_logits = torch.log(final_pred + self.eps)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # 归一化输入图像
        normalized_img = self._normalize_image(x)

        # 应用增强CRF
        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - final_pred, final_pred], dim=1)

        return {
            'pred_h': pred_h_prob,
            'pred_l': pred_l_prob,
            'pred_edge': pred_edge_prob,
            'ensemble_pred': final_pred,
            'refined_pred': refined_predict,
            'edge_map': F.interpolate(edge_map, size=x.size()[2:], mode='bilinear', align_corners=True),
            'edge_magnitude': F.interpolate(edge_magnitude, size=x.size()[2:], mode='bilinear', align_corners=True)
        }

    def _normalize_image(self, x):
        """图像归一化"""
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        return normalized_img


def create_scsa_enhanced_90_model(backbone_path=None, crf_iter=7, trainable_crf=True):
    """创建增强版SCSA模型，目标90% IoU"""
    return GDNetSCSAEnhanced90(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )


if __name__ == "__main__":
    # 测试模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = create_scsa_enhanced_90_model().to(device)
    
    # 测试前向传播
    with torch.no_grad():
        x = torch.randn(2, 3, 416, 416).to(device)
        outputs = model(x)
        
        print("模型输出:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
        
        # 计算参数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"\n模型参数统计:")
        print(f"  总参数量: {total_params:,} ({total_params/1e6:.1f}M)")
        print(f"  可训练参数: {trainable_params:,} ({trainable_params/1e6:.1f}M)")
        
        print(f"\n✅ Enhanced SCSA 90% IoU目标模型创建成功!")