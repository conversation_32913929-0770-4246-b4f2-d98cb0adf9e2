"""
轻量化Physics模型的损失函数 - 改进版
保留原版Physics模型的关键特性，同时简化实现
目标: 达到接近原版85%+的性能
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from .loss import IOU, EdgeSaliencyLoss


class FocalLoss(nn.Module):
    """Focal Loss - 解决类别不平衡问题"""
    def __init__(self, alpha=0.25, gamma=2.0, reduce=True):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduce = reduce

    def forward(self, inputs, targets):
        # 应用sigmoid确保输入在0-1范围内
        inputs = torch.sigmoid(inputs)
        inputs = torch.clamp(inputs, min=1e-7, max=1.0-1e-7)
        targets = torch.clamp(targets, min=0.0, max=1.0)
        
        # 计算二分类交叉熵的概率
        pt = targets * inputs + (1 - targets) * (1 - inputs)
        
        # 计算focal权重
        focal_weight = (1 - pt) ** self.gamma
        
        # 计算alpha权重
        alpha_weight = targets * self.alpha + (1 - targets) * (1 - self.alpha)
        
        # 计算BCE损失
        bce_loss = -(targets * torch.log(inputs) + (1 - targets) * torch.log(1 - inputs))
        
        # 组合成focal loss
        focal_loss = alpha_weight * focal_weight * bce_loss
        
        if self.reduce:
            return focal_loss.mean()
        else:
            return focal_loss


class ImprovedEdgeLoss(nn.Module):
    """改进的边缘损失 - 使用专业的EdgeSaliencyLoss"""
    def __init__(self, device, alpha_sal=0.7):
        super(ImprovedEdgeLoss, self).__init__()
        self.device = device
        try:
            # 使用原版的专业边缘检测损失
            self.edge_saliency_loss = EdgeSaliencyLoss(device=device, alpha_sal=alpha_sal)
            self.use_professional = True
            print("✅ 使用专业EdgeSaliencyLoss")
        except Exception as e:
            print(f"⚠️ EdgeSaliencyLoss不可用，使用简化版本: {e}")
            self.use_professional = False
            # 备用的拉普拉斯边缘检测
            self.register_buffer('laplacian_kernel', torch.tensor([
                [-1., -1., -1.], 
                [-1.,  8., -1.], 
                [-1., -1., -1.]
            ]).view(1, 1, 3, 3))

    def forward(self, inputs, targets):
        if self.use_professional:
            try:
                # 确保预测值在有效范围内
                safe_pred = torch.clamp(torch.sigmoid(inputs), min=1e-7, max=1.0-1e-7)
                edge_loss = self.edge_saliency_loss(safe_pred, targets)
                
                # 检查是否产生NaN
                if torch.isnan(edge_loss) or torch.isinf(edge_loss):
                    print("警告: EdgeSaliencyLoss产生NaN/Inf，使用备用损失")
                    return self._fallback_edge_loss(inputs, targets)
                
                return edge_loss
            except Exception as e:
                print(f"EdgeSaliencyLoss计算错误: {e}，使用备用损失")
                return self._fallback_edge_loss(inputs, targets)
        else:
            return self._fallback_edge_loss(inputs, targets)
    
    def _fallback_edge_loss(self, inputs, targets):
        """备用的边缘损失实现"""
        inputs = torch.sigmoid(inputs)
        
        # 计算预测和真值的边缘
        pred_edges = F.conv2d(inputs, self.laplacian_kernel, padding=1)
        target_edges = F.conv2d(targets, self.laplacian_kernel, padding=1)
        
        # 使用MSE计算边缘损失
        edge_loss = F.mse_loss(pred_edges, target_edges)
        return edge_loss


class LitePhysicsConsistencyLoss(nn.Module):
    """轻量化物理一致性损失 - 保留关键约束"""
    def __init__(self):
        super(LitePhysicsConsistencyLoss, self).__init__()
        
        # 用于反射检测的核
        self.register_buffer('reflection_kernel', torch.tensor([
            [0, -1, 0], 
            [-1, 4, -1], 
            [0, -1, 0]
        ]).float().view(1, 1, 3, 3))

    def forward(self, pred, target, image):
        """
        轻量化的物理一致性验证
        pred: 预测结果 [B, 1, H, W]
        target: 真值 [B, 1, H, W]
        image: 原始图像 [B, 3, H, W]
        """
        device = pred.device
        
        # 计算亮度
        if image.size(1) == 3:
            # RGB to grayscale
            luminance = 0.299 * image[:, 0:1] + 0.587 * image[:, 1:2] + 0.114 * image[:, 2:3]
        else:
            luminance = image
        
        # 反射一致性：玻璃区域应该有较高的亮度变化
        reflection_kernel = self.reflection_kernel.to(device)
        luminance_grad = F.conv2d(luminance, reflection_kernel, padding=1)
        luminance_grad = torch.abs(luminance_grad)
        
        # 预测为玻璃的区域
        pred_binary = (torch.sigmoid(pred) > 0.5).float()
        target_binary = (target > 0.5).float()
        
        # 玻璃区域的亮度变化应该更明显
        glass_regions_pred = pred_binary * luminance_grad
        glass_regions_target = target_binary * luminance_grad
        non_glass_regions = (1 - target_binary) * luminance_grad
        
        # 计算统计量
        glass_mean_pred = torch.mean(glass_regions_pred) + 1e-7
        glass_mean_target = torch.mean(glass_regions_target) + 1e-7
        non_glass_mean = torch.mean(non_glass_regions) + 1e-7
        
        # 一致性损失：预测的玻璃区域应该与真实玻璃区域有相似的亮度变化模式
        consistency_loss = F.mse_loss(glass_regions_pred, glass_regions_target)
        
        # 对比损失：玻璃区域与非玻璃区域应该有区别
        contrast_loss = torch.max(torch.tensor(0.0, device=device), 
                                non_glass_mean - glass_mean_target + 0.05)
        
        return consistency_loss + 0.1 * contrast_loss


class PhysicsLiteLossImproved(nn.Module):
    """
    改进的轻量化Physics模型损失函数
    保留原版的关键特性，性能目标：85%+ IoU
    """
    def __init__(self, 
                 device,
                 focal_weight=0.2,       # 降低focal权重
                 iou_weight=0.4,         # 降低IoU权重，给其他损失更多空间
                 edge_weight=0.3,        # 提高边缘权重
                 consistency_weight=0.05,# 保持一致性损失较小
                 physics_weight=0.15,    # 新增物理一致性损失
                 crf_weight=0.05,        # 降低CRF权重
                 alpha=0.25,             # Focal loss参数
                 gamma=2.0):             # Focal loss参数
        super(PhysicsLiteLossImproved, self).__init__()
        
        self.device = device
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight
        self.edge_weight = edge_weight
        self.consistency_weight = consistency_weight
        self.physics_weight = physics_weight
        self.crf_weight = crf_weight
        
        # 损失函数组件
        self.focal_loss = FocalLoss(alpha=alpha, gamma=gamma)
        self.iou_loss = IOU(size_average=True)  # 使用原版的IOU损失
        self.edge_loss = ImprovedEdgeLoss(device=device, alpha_sal=0.7)
        self.consistency_loss = ConsistencyLoss()
        self.physics_consistency = LitePhysicsConsistencyLoss()
        self.crf_loss = CRFLoss()
        
        print(f"改进PhysicsLite损失权重配置:")
        print(f"  Focal: {focal_weight}, IoU: {iou_weight}, Edge: {edge_weight}")
        print(f"  Physics: {physics_weight}, Consistency: {consistency_weight}, CRF: {crf_weight}")

    def forward(self, predictions, targets, input_image=None):
        """
        predictions: 模型输出字典，包含多个预测结果
        targets: [B, 1, H, W] 目标掩码
        input_image: [B, 3, H, W] 原始输入图像(用于物理一致性检查)
        """
        losses = {}
        total_loss = 0.0
        
        # 获取各个预测结果
        main_pred = predictions['main_pred']        # 主预测
        physics_pred = predictions['physics_pred']  # 物理预测
        final_pred = predictions['final_pred']      # 最终预测
        ensemble_pred = predictions['ensemble_pred'] # 集成预测
        refined_pred = predictions['refined_pred']   # CRF精炼预测
        
        # 1. 多层次Focal损失
        focal_main = self.focal_loss(main_pred, targets)
        focal_physics = self.focal_loss(physics_pred, targets)
        focal_final = self.focal_loss(final_pred, targets)
        focal_ensemble = self.focal_loss(ensemble_pred, targets)
        
        focal_total = (focal_main + focal_physics + focal_final + focal_ensemble) / 4
        losses['focal_total'] = focal_total
        total_loss += focal_total * self.focal_weight
        
        # 2. 多层次IoU损失 - 使用原版IOU
        iou_main = self.iou_loss(main_pred, targets)
        iou_physics = self.iou_loss(physics_pred, targets)
        iou_final = self.iou_loss(final_pred, targets)
        iou_ensemble = self.iou_loss(ensemble_pred, targets)
        
        # 加权组合，重点关注最终预测
        iou_total = (0.2 * iou_main + 0.2 * iou_physics + 0.3 * iou_final + 0.3 * iou_ensemble)
        losses['iou_total'] = iou_total
        total_loss += iou_total * self.iou_weight
        
        # 3. 改进的边缘损失
        edge_final = self.edge_loss(final_pred, targets)
        edge_ensemble = self.edge_loss(ensemble_pred, targets)
        edge_total = (edge_final + edge_ensemble) / 2
        losses['edge_total'] = edge_total
        total_loss += edge_total * self.edge_weight
        
        # 4. 物理一致性损失 - 关键改进
        if input_image is not None:
            physics_consistency = self.physics_consistency(ensemble_pred, targets, input_image)
            losses['physics_consistency'] = physics_consistency
            total_loss += physics_consistency * self.physics_weight
        else:
            losses['physics_consistency'] = torch.tensor(0.0, device=self.device)
        
        # 5. 预测一致性损失
        consistency_main_final = self.consistency_loss(main_pred, final_pred)
        consistency_physics_ensemble = self.consistency_loss(physics_pred, ensemble_pred)
        consistency_total = (consistency_main_final + consistency_physics_ensemble) / 2
        losses['consistency_total'] = consistency_total
        total_loss += consistency_total * self.consistency_weight
        
        # 6. CRF损失
        crf_loss_val = self.crf_loss(refined_pred, targets)
        losses['crf_loss'] = crf_loss_val
        total_loss += crf_loss_val * self.crf_weight
        
        # 记录总损失
        losses['total'] = total_loss
        
        return total_loss, losses

    def get_loss_weights(self):
        """获取当前损失权重配置"""
        return {
            'focal_weight': self.focal_weight,
            'iou_weight': self.iou_weight,
            'edge_weight': self.edge_weight,
            'physics_weight': self.physics_weight,
            'consistency_weight': self.consistency_weight,
            'crf_weight': self.crf_weight
        }

    def update_weights(self, epoch, total_epochs):
        """根据训练进度动态调整损失权重"""
        progress = epoch / total_epochs
        
        # 物理一致性损失在中期训练时权重较高
        if 0.2 < progress < 0.8:
            self.physics_weight = 0.15 * (1 + 0.5 * np.sin(np.pi * (progress - 0.2) / 0.6))
        
        # 边缘损失在早期训练时权重较高，后期保持稳定
        if progress < 0.3:
            self.edge_weight = 0.3 * (1 + 0.3 * (1 - progress / 0.3))
        
        # CRF损失在后期训练时权重略微增加
        if progress > 0.7:
            self.crf_weight = 0.05 * (1 + 0.4 * (progress - 0.7) / 0.3)


class ConsistencyLoss(nn.Module):
    """一致性损失 - 确保多个预测输出的一致性"""
    def __init__(self, weight=0.1):
        super(ConsistencyLoss, self).__init__()
        self.weight = weight

    def forward(self, pred1, pred2):
        """计算两个预测之间的一致性损失"""
        pred1_prob = torch.sigmoid(pred1)
        pred2_prob = torch.sigmoid(pred2)
        consistency_loss = F.mse_loss(pred1_prob, pred2_prob)
        return consistency_loss * self.weight


class CRFLoss(nn.Module):
    """CRF损失 - 针对CRF输出的专门损失"""
    def __init__(self, weight=0.5):
        super(CRFLoss, self).__init__()
        self.weight = weight
        self.cross_entropy = nn.CrossEntropyLoss()

    def forward(self, crf_output, targets):
        """
        crf_output: [B, 2, H, W] - CRF输出的双通道概率
        targets: [B, 1, H, W] - 单通道目标
        """
        # 将单通道目标转换为长整型类别标签
        targets_long = targets.squeeze(1).long()  # [B, H, W]
        
        # 计算交叉熵损失
        crf_loss = self.cross_entropy(crf_output, targets_long)
        return crf_loss * self.weight


def test_improved_physics_lite_loss():
    """测试改进的损失函数"""
    print("🧪 测试改进的轻量化Physics损失函数...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建损失函数
    loss_fn = PhysicsLiteLossImproved(device=device)
    
    # 模拟模型输出
    batch_size, height, width = 2, 416, 416
    
    predictions = {
        'main_pred': torch.randn(batch_size, 1, height, width, device=device),
        'physics_pred': torch.randn(batch_size, 1, height, width, device=device),
        'final_pred': torch.randn(batch_size, 1, height, width, device=device),
        'ensemble_pred': torch.randn(batch_size, 1, height, width, device=device),
        'refined_pred': torch.randn(batch_size, 2, height, width, device=device)  # CRF双通道输出
    }
    
    targets = torch.randint(0, 2, (batch_size, 1, height, width), device=device, dtype=torch.float32)
    input_image = torch.randn(batch_size, 3, height, width, device=device)
    
    # 计算损失
    total_loss, loss_dict = loss_fn(predictions, targets, input_image)
    
    print(f"✅ 改进损失函数测试成功!")
    print(f"📊 损失分解:")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.item():.6f}")
    
    print(f"🔥 总损失: {total_loss.item():.6f}")
    
    # 测试动态权重调整
    print(f"\n🔄 测试动态权重调整:")
    original_weights = loss_fn.get_loss_weights()
    print(f"初始权重: {original_weights}")
    
    loss_fn.update_weights(epoch=30, total_epochs=100)
    updated_weights = loss_fn.get_loss_weights()
    print(f"30轮后权重: {updated_weights}")


if __name__ == '__main__':
    test_improved_physics_lite_loss() 