"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_hybrid_scsa.py
 @Function: Hybrid SCSA model combining best of both worlds

结合SCSA (87.95% IoU) 和简化物理特性的混合模型
主要策略：
1. 保持SCSA的核心优势
2. 添加轻量化的物理特性增强
3. 优化特征融合策略
4. 数值稳定性优化

目标：突破90% IoU
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF
from ig_glass.attention_scsa import EnhancedSCSA


class LightweightPhysicsEnhancer(nn.Module):
    """
    轻量化物理特性增强器
    只保留最有效的物理特性，避免复杂度过高
    """
    def __init__(self, in_channels):
        super(LightweightPhysicsEnhancer, self).__init__()
        
        # 边缘检测增强 - 使用固定核避免训练不稳定
        self.register_buffer('sobel_x', torch.tensor([
            [-1, 0, 1], [-2, 0, 2], [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y', torch.tensor([
            [-1, -2, -1], [0, 0, 0], [1, 2, 1]
        ]).float().view(1, 1, 3, 3))
        
        # 轻量化边缘增强网络
        self.edge_enhancer = nn.Sequential(
            nn.Conv2d(2, 16, 3, 1, 1),  # 2通道输入（sobel_x + sobel_y）
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, 3, 1, 1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 1),
            nn.Sigmoid()
        )
        
        # 透明度感知模块 - 简化版本
        self.transparency_detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 4, 1),
            nn.BatchNorm2d(in_channels // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 4, 1, 1),
            nn.Sigmoid()
        )
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(in_channels + 2, in_channels, 3, 1, 1),  # +2 for edge and transparency
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x, rgb_input=None):
        """
        x: 特征图
        rgb_input: 原始RGB输入（用于边缘检测）
        """
        # 边缘检测增强
        if rgb_input is not None:
            # 转换为灰度
            gray = torch.mean(rgb_input, dim=1, keepdim=True)
            
            # 应用Sobel算子
            edge_x = F.conv2d(gray, self.sobel_x, padding=1)
            edge_y = F.conv2d(gray, self.sobel_y, padding=1)
            
            # 上采样到特征图尺寸
            edge_x = F.interpolate(edge_x, size=x.shape[2:], mode='bilinear', align_corners=True)
            edge_y = F.interpolate(edge_y, size=x.shape[2:], mode='bilinear', align_corners=True)
            
            # 边缘增强
            edge_features = torch.cat([edge_x, edge_y], dim=1)
            edge_weight = self.edge_enhancer(edge_features)
        else:
            edge_weight = torch.ones(x.size(0), 1, x.size(2), x.size(3), device=x.device)
        
        # 透明度检测
        transparency_weight = self.transparency_detector(x)
        
        # 特征融合
        enhanced_features = torch.cat([x, edge_weight, transparency_weight], dim=1)
        output = self.feature_fusion(enhanced_features)
        
        return output, edge_weight, transparency_weight


class OptimizedLCFI(nn.Module):
    """优化的LCFI模块，平衡性能和效率"""
    def __init__(self, in_channels, r1, r2, r3, r4):
        super(OptimizedLCFI, self).__init__()
        
        # 自适应通道数
        mid_channels = max(in_channels // 6, 64)
        
        # 多尺度空洞卷积
        self.branch1 = self._make_branch(in_channels, mid_channels, r1)
        self.branch2 = self._make_branch(in_channels, mid_channels, r2)
        self.branch3 = self._make_branch(in_channels, mid_channels, r3)
        self.branch4 = self._make_branch(in_channels, mid_channels, r4)
        
        # 特征融合和降维
        self.fusion = nn.Sequential(
            nn.Conv2d(mid_channels * 4, in_channels // 2, 1),
            nn.BatchNorm2d(in_channels // 2),
            nn.ReLU(inplace=True)
        )
        
        # 残差连接
        self.residual = nn.Conv2d(in_channels, in_channels // 2, 1)
        
    def _make_branch(self, in_channels, out_channels, dilation):
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, 1, dilation, dilation=dilation),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        # 多尺度特征
        b1 = self.branch1(x)
        b2 = self.branch2(x)
        b3 = self.branch3(x)
        b4 = self.branch4(x)
        
        # 融合
        fused = torch.cat([b1, b2, b3, b4], dim=1)
        fused = self.fusion(fused)
        
        # 残差
        residual = self.residual(x)
        
        return F.relu(fused + residual)


class GDNetHybridSCSA(nn.Module):
    """
    混合SCSA模型：结合SCSA优势和轻量化物理特性
    目标：突破90% IoU
    """
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetHybridSCSA, self).__init__()
        
        # 骨干网络
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0
        self.layer1 = resnext.layer1
        self.layer2 = resnext.layer2
        self.layer3 = resnext.layer3
        self.layer4 = resnext.layer4

        # 优化的LCFI模块
        self.h5_conv = OptimizedLCFI(2048, 1, 2, 3, 4)  # 输出1024
        self.h4_conv = OptimizedLCFI(1024, 1, 2, 3, 4)  # 输出512
        self.h3_conv = OptimizedLCFI(512, 1, 2, 3, 4)   # 输出256
        self.l2_conv = OptimizedLCFI(256, 1, 2, 3, 4)   # 输出128

        # 物理特性增强器
        self.physics_enhancer_h = LightweightPhysicsEnhancer(1024)  # 高层特征
        self.physics_enhancer_l = LightweightPhysicsEnhancer(128)   # 低层特征

        # 多尺度融合
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        
        # 融合维度：1024 + 512 + 256 = 1792
        fusion_dim = 1792
        
        # 增强SCSA注意力
        self.h_fusion = EnhancedSCSA(
            dim=fusion_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            attn_drop_ratio=0.1,
            channel_weight=1.1,
            edge_enhance=True,
            multi_scale=True
        )
        
        # 特征融合网络
        self.h_fusion_conv = nn.Sequential(
            nn.Conv2d(fusion_dim, 896, 3, 1, 1),
            nn.BatchNorm2d(896),
            nn.ReLU(inplace=True),
            nn.Conv2d(896, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )

        # 低层特征处理
        self.l_fusion_conv = nn.Sequential(
            nn.Conv2d(128, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )

        # 预测头
        self.predict_h = nn.Conv2d(512, 1, 3, 1, 1)
        self.predict_l = nn.Conv2d(64, 1, 3, 1, 1)

        # 优化的CRF
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=12.0,
            gaussian_weight=6.0,
            bilateral_spatial_sigma=45.0,
            bilateral_color_sigma=3.5,
            gaussian_sigma=1.8,
            trainable=trainable_crf
        )

        # 自适应权重
        self.adaptive_weights = nn.Parameter(torch.tensor([0.75, 0.25]), requires_grad=True)
        
        # 最终增强模块
        self.final_enhancer = nn.Sequential(
            nn.Conv2d(1, 8, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 3, 1, 1),
            nn.Sigmoid()
        )

        self._init_weights()

    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # 提取多层特征
        layer0 = self.layer0(x)
        layer1 = self.layer1(layer0)
        layer2 = self.layer2(layer1)
        layer3 = self.layer3(layer2)
        layer4 = self.layer4(layer3)

        # LCFI特征提取
        h5_conv = self.h5_conv(layer4)
        h4_conv = self.h4_conv(layer3)
        h3_conv = self.h3_conv(layer2)
        l2_conv = self.l2_conv(layer1)

        # 物理特性增强
        h5_enhanced, h_edge_weight, h_transparency_weight = self.physics_enhancer_h(h5_conv, x)
        l2_enhanced, l_edge_weight, l_transparency_weight = self.physics_enhancer_l(l2_conv, x)

        # 高层特征融合
        h5_up = self.h5_up(h5_enhanced)
        h3_down = self.h3_down(h3_conv)
        
        h_fused = torch.cat([h5_up, h4_conv, h3_down], dim=1)
        
        # 应用SCSA注意力
        h_attended = self.h_fusion(h_fused)
        h_final = self.h_fusion_conv(h_attended)

        # 低层特征处理
        l_final = self.l_fusion_conv(l2_enhanced)

        # 预测
        pred_h = self.predict_h(h_final)
        pred_l = self.predict_l(l_final)

        # 上采样
        pred_h = F.interpolate(pred_h, size=x.size()[2:], mode='bilinear', align_corners=True)
        pred_l = F.interpolate(pred_l, size=x.size()[2:], mode='bilinear', align_corners=True)

        # Sigmoid激活
        pred_h_prob = torch.sigmoid(pred_h)
        pred_l_prob = torch.sigmoid(pred_l)

        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = weights[0] * pred_h_prob + weights[1] * pred_l_prob

        # 最终增强
        enhancement = self.final_enhancer(ensemble_pred)
        final_pred = ensemble_pred * (1 + 0.2 * enhancement)  # 轻微增强
        final_pred = torch.clamp(final_pred, min=1e-7, max=1.0 - 1e-7)

        # CRF后处理
        bg_logits = torch.log(1 - final_pred + 1e-7)
        fg_logits = torch.log(final_pred + 1e-7)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        normalized_img = self._normalize_image(x)

        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - final_pred, final_pred], dim=1)

        return {
            'pred_h': pred_h_prob,
            'pred_l': pred_l_prob,
            'ensemble_pred': final_pred,
            'refined_pred': refined_predict,
            'physics_maps': {
                'h_edge': F.interpolate(h_edge_weight, size=x.size()[2:], mode='bilinear', align_corners=True),
                'h_transparency': F.interpolate(h_transparency_weight, size=x.size()[2:], mode='bilinear', align_corners=True),
                'l_edge': F.interpolate(l_edge_weight, size=x.size()[2:], mode='bilinear', align_corners=True),
                'l_transparency': F.interpolate(l_transparency_weight, size=x.size()[2:], mode='bilinear', align_corners=True)
            }
        }

    def _normalize_image(self, x):
        """图像归一化"""
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        return normalized_img


def create_hybrid_scsa_model(backbone_path=None, crf_iter=5, trainable_crf=True):
    """创建混合SCSA模型"""
    return GDNetHybridSCSA(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )
