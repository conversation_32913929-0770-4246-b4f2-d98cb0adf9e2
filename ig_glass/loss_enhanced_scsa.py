"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : loss_enhanced_scsa.py
 @Function: Enhanced loss functions for 90%+ IoU performance

基于87.95% IoU的成功经验，进一步优化损失函数：
1. 改进的IoU损失，更精确的梯度
2. 边缘感知的Focal损失
3. 自适应权重调整
4. 数值稳定性优化

目标：突破90% IoU
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class EnhancedIoULoss(nn.Module):
    """
    增强版IoU损失，针对玻璃检测优化
    主要改进：
    1. 更稳定的数值计算
    2. 自适应平滑项
    3. 边缘权重增强
    """
    def __init__(self, smooth=1e-6, edge_weight=2.0):
        super(EnhancedIoULoss, self).__init__()
        self.smooth = smooth
        self.edge_weight = edge_weight
        
    def forward(self, pred, target):
        # 确保输入在[0,1]范围内
        pred = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 计算边缘权重
        edge_mask = self._compute_edge_weight(target)
        
        # 加权的交集和并集
        intersection = torch.sum(pred * target * edge_mask, dim=(2, 3))
        union = torch.sum(pred * edge_mask, dim=(2, 3)) + torch.sum(target * edge_mask, dim=(2, 3)) - intersection
        
        # 自适应平滑项
        adaptive_smooth = self.smooth * torch.mean(target, dim=(2, 3), keepdim=True).squeeze()
        
        # IoU计算
        iou = (intersection + adaptive_smooth) / (union + adaptive_smooth)
        
        # 返回1-IoU作为损失
        return 1 - torch.mean(iou)
    
    def _compute_edge_weight(self, target):
        """计算边缘权重"""
        # 使用Sobel算子检测边缘
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], 
                              dtype=target.dtype, device=target.device).view(1, 1, 3, 3)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], 
                              dtype=target.dtype, device=target.device).view(1, 1, 3, 3)
        
        edge_x = F.conv2d(target, sobel_x, padding=1)
        edge_y = F.conv2d(target, sobel_y, padding=1)
        edge_magnitude = torch.sqrt(edge_x**2 + edge_y**2)
        
        # 归一化边缘强度
        edge_weight = 1 + self.edge_weight * torch.sigmoid(edge_magnitude)
        
        return edge_weight


class EdgeAwareFocalLoss(nn.Module):
    """
    边缘感知的Focal损失
    在玻璃边缘区域应用更高的权重
    """
    def __init__(self, alpha=0.25, gamma=2.0, edge_gamma=3.0):
        super(EdgeAwareFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.edge_gamma = edge_gamma
        
    def forward(self, pred, target):
        # 确保输入在有效范围内
        pred = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 计算边缘权重
        edge_weight = self._compute_edge_weight(target)
        
        # 计算BCE损失
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        
        # 计算pt
        pt = torch.where(target == 1, pred, 1 - pred)
        
        # 计算alpha权重
        alpha_t = torch.where(target == 1, self.alpha, 1 - self.alpha)
        
        # 边缘区域使用更高的gamma
        gamma_t = torch.where(edge_weight > 1.5, self.edge_gamma, self.gamma)
        
        # Focal权重
        focal_weight = alpha_t * (1 - pt) ** gamma_t
        
        # 最终损失
        focal_loss = focal_weight * bce_loss * edge_weight
        
        return torch.mean(focal_loss)
    
    def _compute_edge_weight(self, target):
        """计算边缘权重"""
        # 拉普拉斯算子
        laplacian_kernel = torch.tensor([[0, -1, 0], [-1, 4, -1], [0, -1, 0]], 
                                       dtype=target.dtype, device=target.device).view(1, 1, 3, 3)
        
        edge_response = F.conv2d(target, laplacian_kernel, padding=1)
        edge_magnitude = torch.abs(edge_response)
        
        # 归一化并增强边缘
        edge_weight = 1 + 2.0 * torch.sigmoid(edge_magnitude * 5)
        
        return edge_weight


class ConsistencyLoss(nn.Module):
    """
    一致性损失，确保多尺度预测的一致性
    """
    def __init__(self, weight=1.0):
        super(ConsistencyLoss, self).__init__()
        self.weight = weight
        
    def forward(self, pred_h, pred_l):
        # 将高层预测下采样到低层尺度
        pred_h_down = F.avg_pool2d(pred_h, kernel_size=4, stride=4)
        
        # 将低层预测上采样到高层尺度
        pred_l_up = F.interpolate(pred_l, size=pred_h.shape[2:], mode='bilinear', align_corners=True)
        
        # 计算L2一致性损失
        consistency_loss = F.mse_loss(pred_h, pred_l_up) + F.mse_loss(pred_h_down, pred_l)
        
        return self.weight * consistency_loss


class AdaptiveWeightLoss(nn.Module):
    """
    自适应权重损失调整器
    根据训练进度动态调整损失权重
    """
    def __init__(self, initial_weights, adaptation_rate=0.01):
        super(AdaptiveWeightLoss, self).__init__()
        self.weights = nn.Parameter(torch.tensor(initial_weights, dtype=torch.float32))
        self.adaptation_rate = adaptation_rate
        self.step_count = 0
        
    def forward(self, losses, performance_metrics=None):
        """
        losses: list of loss values
        performance_metrics: dict with 'iou', 'precision', 'recall' etc.
        """
        self.step_count += 1
        
        # 归一化权重
        normalized_weights = F.softmax(self.weights, dim=0)
        
        # 计算加权损失
        total_loss = sum(w * loss for w, loss in zip(normalized_weights, losses))
        
        # 如果提供了性能指标，进行自适应调整
        if performance_metrics is not None and self.step_count % 100 == 0:
            self._adapt_weights(performance_metrics)
        
        return total_loss, normalized_weights
    
    def _adapt_weights(self, metrics):
        """根据性能指标调整权重"""
        with torch.no_grad():
            iou = metrics.get('iou', 0.5)
            
            # 如果IoU较低，增加IoU损失权重
            if iou < 0.85:
                self.weights[1] += self.adaptation_rate  # IoU损失权重
            
            # 如果边缘质量不好，增加边缘损失权重
            edge_quality = metrics.get('edge_f1', 0.5)
            if edge_quality < 0.8:
                self.weights[2] += self.adaptation_rate  # 边缘损失权重


class EnhancedSCSALoss(nn.Module):
    """
    增强版SCSA综合损失函数
    目标：突破90% IoU
    """
    def __init__(self, 
                 focal_weight=0.2,
                 iou_weight=0.6,
                 edge_weight=0.15,
                 consistency_weight=0.05,
                 adaptive=True):
        super(EnhancedSCSALoss, self).__init__()
        
        # 损失函数组件
        self.focal_loss = EdgeAwareFocalLoss(alpha=0.25, gamma=2.0, edge_gamma=3.0)
        self.iou_loss = EnhancedIoULoss(smooth=1e-6, edge_weight=2.0)
        self.consistency_loss = ConsistencyLoss(weight=1.0)
        
        # 权重管理
        if adaptive:
            initial_weights = [focal_weight, iou_weight, edge_weight, consistency_weight]
            self.weight_manager = AdaptiveWeightLoss(initial_weights)
        else:
            self.weights = [focal_weight, iou_weight, edge_weight, consistency_weight]
            self.weight_manager = None
        
        self.adaptive = adaptive
        
    def forward(self, predictions, targets, performance_metrics=None):
        """
        predictions: dict with 'pred_h', 'pred_l', 'ensemble_pred', 'refined_pred'
        targets: ground truth masks
        performance_metrics: optional performance metrics for adaptive weighting
        """
        pred_h = predictions['pred_h']
        pred_l = predictions['pred_l']
        ensemble_pred = predictions['ensemble_pred']
        
        # 计算各项损失
        focal_loss = self.focal_loss(ensemble_pred, targets)
        iou_loss = self.iou_loss(ensemble_pred, targets)
        edge_loss = self.focal_loss(ensemble_pred, targets)  # 使用边缘感知focal作为边缘损失
        consistency_loss = self.consistency_loss(pred_h, pred_l)
        
        losses = [focal_loss, iou_loss, edge_loss, consistency_loss]
        
        # 权重管理
        if self.adaptive and self.weight_manager is not None:
            total_loss, current_weights = self.weight_manager(losses, performance_metrics)
        else:
            current_weights = self.weights
            total_loss = sum(w * loss for w, loss in zip(current_weights, losses))
        
        return {
            'total_loss': total_loss,
            'focal_loss': focal_loss,
            'iou_loss': iou_loss,
            'edge_loss': edge_loss,
            'consistency_loss': consistency_loss,
            'weights': current_weights
        }


def create_enhanced_scsa_loss(focal_weight=0.2, iou_weight=0.6, edge_weight=0.15, 
                             consistency_weight=0.05, adaptive=True):
    """创建增强版SCSA损失函数"""
    return EnhancedSCSALoss(
        focal_weight=focal_weight,
        iou_weight=iou_weight,
        edge_weight=edge_weight,
        consistency_weight=consistency_weight,
        adaptive=adaptive
    )
