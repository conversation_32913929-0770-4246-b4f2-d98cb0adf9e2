"""
简化的玻璃物理特性损失函数
用于测试网络基本稳定性，只使用经过验证的损失组件
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from .loss import IOU, FocalLoss


class SimpleGlassPhysicsLoss(nn.Module):
    """
    简化的玻璃物理特性损失函数
    只使用基础的focal loss和IoU loss，确保训练稳定性
    """
    def __init__(self, device, focal_weight=0.6, iou_weight=0.4):
        super(SimpleGlassPhysicsLoss, self).__init__()
        self.device = device
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight
        
        # 使用经过验证的损失函数
        self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0)
        self.iou_loss = IOU(size_average=True)
        
    def forward(self, predictions, target):
        """
        Args:
            predictions: GDNetGlassPhysics的输出dict
            target: 真值 [B, 1, H, W]
        """
        # 确保target维度正确
        if target.dim() == 3:
            target = target.unsqueeze(1)
        
        # 只使用基础的focal loss和IoU loss
        main_focal = self.focal_loss(predictions['main_pred'], target)
        final_focal = self.focal_loss(predictions['final_pred'], target)
        ensemble_focal = self.focal_loss(predictions['ensemble_pred'], target)
        
        # IoU损失
        main_iou = self.iou_loss(predictions['main_pred'], target)
        final_iou = self.iou_loss(predictions['final_pred'], target)
        ensemble_iou = self.iou_loss(predictions['ensemble_pred'], target)
        
        # 组合损失
        total_focal = (main_focal + final_focal + ensemble_focal) / 3
        total_iou = (main_iou + final_iou + ensemble_iou) / 3
        
        total_loss = self.focal_weight * total_focal + self.iou_weight * total_iou
        
        return {
            'total_loss': total_loss,
            'focal_loss': total_focal,
            'physics_specialization': torch.tensor(0.0, device=self.device),
            'physics_consistency': torch.tensor(0.0, device=self.device),
            'edge_loss': torch.tensor(0.0, device=self.device),
            'iou_loss': total_iou
        } 