"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : loss_vit_physics_90iou.py
 @Function: 专门针对90% IoU目标的损失函数

超高精度损失策略：
1. 精确边缘感知损失 - 亚像素级边缘精度
2. 难样本自适应挖掘 - 动态关注最难样本
3. 物理一致性约束 - 确保物理特性合理性
4. 多尺度深度监督 - 所有层级都参与优化
5. 对抗式边缘增强 - 生成器-判别器边缘优化
6. 测试时增强损失 - TTA一致性约束
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple


class PrecisionEdgeLoss(nn.Module):
    """
    精确边缘损失 - 亚像素级边缘精度
    专门针对玻璃边缘的精细检测
    """
    def __init__(self, edge_weight=3.0, gradient_weight=2.0):
        super(PrecisionEdgeLoss, self).__init__()
        self.edge_weight = edge_weight
        self.gradient_weight = gradient_weight
        
        # 多尺度边缘检测核
        self.register_buffer('sobel_x', torch.tensor([
            [-1, 0, 1], [-2, 0, 2], [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y', torch.tensor([
            [-1, -2, -1], [0, 0, 0], [1, 2, 1]
        ]).float().view(1, 1, 3, 3))
        
        # 精细边缘检测核（5x5）
        self.register_buffer('sobel_x_5', torch.tensor([
            [-1, -2, 0, 2, 1], [-4, -8, 0, 8, 4], [-6, -12, 0, 12, 6],
            [-4, -8, 0, 8, 4], [-1, -2, 0, 2, 1]
        ]).float().view(1, 1, 5, 5) / 100.0)
        
        self.register_buffer('sobel_y_5', torch.tensor([
            [-1, -4, -6, -4, -1], [-2, -8, -12, -8, -2], [0, 0, 0, 0, 0],
            [2, 8, 12, 8, 2], [1, 4, 6, 4, 1]
        ]).float().view(1, 1, 5, 5) / 100.0)
        
        # 拉普拉斯边缘增强
        self.register_buffer('laplacian', torch.tensor([
            [0, -1, 0], [-1, 4, -1], [0, -1, 0]
        ]).float().view(1, 1, 3, 3))
        
    def compute_edge_map(self, x, kernel_3x3=True, kernel_5x5=True):
        """计算多尺度边缘图"""
        edges = []
        
        if kernel_3x3:
            grad_x_3 = F.conv2d(x, self.sobel_x, padding=1)
            grad_y_3 = F.conv2d(x, self.sobel_y, padding=1)
            edge_3 = torch.sqrt(grad_x_3**2 + grad_y_3**2 + 1e-8)
            edges.append(edge_3)
        
        if kernel_5x5:
            grad_x_5 = F.conv2d(x, self.sobel_x_5, padding=2)
            grad_y_5 = F.conv2d(x, self.sobel_y_5, padding=2)
            edge_5 = torch.sqrt(grad_x_5**2 + grad_y_5**2 + 1e-8)
            edges.append(edge_5)
        
        # 拉普拉斯边缘
        lap_edge = torch.abs(F.conv2d(x, self.laplacian, padding=1))
        edges.append(lap_edge)
        
        return torch.mean(torch.stack(edges), dim=0)
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: [B, 1, H, W] 预测结果
            target: [B, 1, H, W] 真实标签
        """
        # 计算边缘图
        pred_edges = self.compute_edge_map(pred)
        target_edges = self.compute_edge_map(target)
        
        # 边缘MSE损失
        edge_mse = F.mse_loss(pred_edges, target_edges)
        
        # 边缘梯度一致性
        pred_grad_x = F.conv2d(pred, self.sobel_x, padding=1)
        pred_grad_y = F.conv2d(pred, self.sobel_y, padding=1)
        target_grad_x = F.conv2d(target, self.sobel_x, padding=1)
        target_grad_y = F.conv2d(target, self.sobel_y, padding=1)
        
        gradient_loss = (F.mse_loss(pred_grad_x, target_grad_x) + 
                        F.mse_loss(pred_grad_y, target_grad_y))
        
        return self.edge_weight * edge_mse + self.gradient_weight * gradient_loss


class HardSampleMiningLoss(nn.Module):
    """
    难样本自适应挖掘损失
    动态关注最难检测的玻璃区域
    """
    def __init__(self, hard_ratio=0.3, neg_pos_ratio=3.0):
        super(HardSampleMiningLoss, self).__init__()
        self.hard_ratio = hard_ratio
        self.neg_pos_ratio = neg_pos_ratio
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: [B, 1, H, W] 预测概率
            target: [B, 1, H, W] 真实标签
        """
        B, C, H, W = pred.shape
        
        # 计算像素级别的BCE损失
        pixel_loss = F.binary_cross_entropy(pred, target, reduction='none')  # [B, 1, H, W]
        pixel_loss = pixel_loss.view(B, -1)  # [B, H*W]
        target_flat = target.view(B, -1)  # [B, H*W]
        
        total_loss = 0
        for b in range(B):
            # 分离正负样本
            pos_mask = target_flat[b] > 0.5
            neg_mask = target_flat[b] <= 0.5
            
            pos_loss = pixel_loss[b][pos_mask]
            neg_loss = pixel_loss[b][neg_mask]
            
            # 正样本难样本挖掘
            if len(pos_loss) > 0:
                num_hard_pos = max(1, int(len(pos_loss) * self.hard_ratio))
                hard_pos_loss, _ = torch.topk(pos_loss, num_hard_pos)
                pos_contribution = hard_pos_loss.mean()
            else:
                pos_contribution = torch.tensor(0.0, device=pred.device)
            
            # 负样本难样本挖掘（保持正负样本比例）
            if len(neg_loss) > 0:
                num_hard_neg = min(len(neg_loss), max(1, int(len(pos_loss) * self.neg_pos_ratio)))
                if num_hard_neg > 0:
                    hard_neg_loss, _ = torch.topk(neg_loss, num_hard_neg)
                    neg_contribution = hard_neg_loss.mean()
                else:
                    neg_contribution = torch.tensor(0.0, device=pred.device)
            else:
                neg_contribution = torch.tensor(0.0, device=pred.device)
            
            total_loss += pos_contribution + neg_contribution
        
        return total_loss / B


class PhysicsConsistencyLoss(nn.Module):
    """
    物理一致性约束损失
    确保物理特性的合理性
    """
    def __init__(self, reflection_weight=1.0, transparency_weight=1.0):
        super(PhysicsConsistencyLoss, self).__init__()
        self.reflection_weight = reflection_weight
        self.transparency_weight = transparency_weight
        
    def forward(self, outputs: Dict, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            outputs: 模型输出字典，包含物理特性
            target: [B, 1, H, W] 真实标签
        """
        total_loss = 0
        
        # 反射一致性：玻璃区域应该有更强的反射
        if 'reflection_map' in outputs:
            reflection_map = outputs['reflection_map']
            
            # 玻璃区域的反射应该更强
            glass_mask = target > 0.5
            non_glass_mask = target <= 0.5
            
            if glass_mask.sum() > 0:
                glass_reflection = reflection_map[glass_mask].mean()
                non_glass_reflection = reflection_map[non_glass_mask].mean()
                
                # 玻璃区域反射应该大于非玻璃区域
                reflection_consistency = F.relu(non_glass_reflection - glass_reflection + 0.1)
                total_loss += self.reflection_weight * reflection_consistency
        
        # 物理置信度一致性
        if 'physics_confidence' in outputs:
            physics_confidence = outputs['physics_confidence']
            
            # 在玻璃区域，物理置信度应该更高
            if glass_mask.sum() > 0:
                # 全局池化得到每个样本的置信度
                confidence_global = F.adaptive_avg_pool2d(physics_confidence, 1)  # [B, 1, 1, 1]
                
                # 玻璃区域面积越大，置信度应该越高
                glass_ratio = glass_mask.float().mean(dim=[1, 2, 3], keepdim=True)  # [B, 1, 1, 1]
                confidence_loss = F.mse_loss(confidence_global, glass_ratio)
                total_loss += self.transparency_weight * confidence_loss
        
        return total_loss


class MultiScaleSuperivisionLoss(nn.Module):
    """
    多尺度深度监督损失
    确保所有层级都参与优化
    """
    def __init__(self, scale_weights: List[float] = [1.0, 0.8, 0.6, 0.4]):
        super(MultiScaleSuperivisionLoss, self).__init__()
        self.scale_weights = scale_weights
        self.bce_loss = nn.BCELoss()
        
    def forward(self, outputs: Dict, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            outputs: 包含多尺度预测的输出
            target: [B, 1, H, W] 真实标签
        """
        total_loss = 0
        
        # 主预测损失
        if 'main_pred' in outputs:
            main_loss = self.bce_loss(outputs['main_pred'], target)
            total_loss += main_loss
        
        # 物理预测损失
        if 'physics_pred' in outputs:
            physics_loss = self.bce_loss(outputs['physics_pred'], target)
            total_loss += 0.5 * physics_loss
        
        # 多层ViT预测损失
        if 'vit_preds' in outputs:
            vit_preds = outputs['vit_preds']
            for i, vit_pred in enumerate(vit_preds):
                if i < len(self.scale_weights):
                    vit_loss = self.bce_loss(vit_pred, target)
                    total_loss += self.scale_weights[i] * 0.3 * vit_loss
        
        return total_loss


class BoundaryRefinementLoss(nn.Module):
    """
    边界细化损失 - 专门优化边界精度
    """
    def __init__(self, boundary_width=5):
        super(BoundaryRefinementLoss, self).__init__()
        self.boundary_width = boundary_width
        
    def get_boundary_mask(self, target: torch.Tensor) -> torch.Tensor:
        """提取边界区域mask"""
        # 膨胀和腐蚀操作提取边界
        kernel = torch.ones(1, 1, self.boundary_width, self.boundary_width, device=target.device)
        
        # 膨胀
        dilated = F.conv2d(target, kernel, padding=self.boundary_width//2)
        dilated = (dilated > 0).float()
        
        # 腐蚀
        eroded = -F.conv2d(-target, kernel, padding=self.boundary_width//2)
        eroded = (eroded > 0).float()
        
        # 边界 = 膨胀 - 腐蚀
        boundary = dilated - eroded
        
        return boundary
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: [B, 1, H, W] 预测结果
            target: [B, 1, H, W] 真实标签
        """
        boundary_mask = self.get_boundary_mask(target)
        
        # 只在边界区域计算损失
        boundary_pred = pred * boundary_mask
        boundary_target = target * boundary_mask
        
        # 边界区域的BCE损失
        boundary_loss = F.binary_cross_entropy(boundary_pred + 1e-7, boundary_target + 1e-7)
        
        return boundary_loss


class ViTPhysics90IoULoss(nn.Module):
    """
    专门针对90% IoU的综合损失函数
    """
    def __init__(self, 
                 bce_weight: float = 1.0,
                 dice_weight: float = 2.0,
                 edge_weight: float = 3.0,
                 hard_mining_weight: float = 2.0,
                 physics_weight: float = 1.5,
                 multiscale_weight: float = 1.0,
                 boundary_weight: float = 2.5,
                 adaptive_weights: bool = True):
        super(ViTPhysics90IoULoss, self).__init__()
        
        # 基础损失
        self.bce_loss = nn.BCELoss()
        self.dice_loss = DiceLoss(smooth=1.0)
        
        # 高级损失组件
        self.edge_loss = PrecisionEdgeLoss()
        self.hard_mining_loss = HardSampleMiningLoss()
        self.physics_loss = PhysicsConsistencyLoss()
        self.multiscale_loss = MultiScaleSuperivisionLoss()
        self.boundary_loss = BoundaryRefinementLoss()
        
        # 权重管理
        if adaptive_weights:
            self.bce_weight = nn.Parameter(torch.tensor(bce_weight), requires_grad=True)
            self.dice_weight = nn.Parameter(torch.tensor(dice_weight), requires_grad=True)
            self.edge_weight = nn.Parameter(torch.tensor(edge_weight), requires_grad=True)
            self.hard_mining_weight = nn.Parameter(torch.tensor(hard_mining_weight), requires_grad=True)
            self.physics_weight = nn.Parameter(torch.tensor(physics_weight), requires_grad=True)
            self.multiscale_weight = nn.Parameter(torch.tensor(multiscale_weight), requires_grad=True)
            self.boundary_weight = nn.Parameter(torch.tensor(boundary_weight), requires_grad=True)
        else:
            self.bce_weight = bce_weight
            self.dice_weight = dice_weight
            self.edge_weight = edge_weight
            self.hard_mining_weight = hard_mining_weight
            self.physics_weight = physics_weight
            self.multiscale_weight = multiscale_weight
            self.boundary_weight = boundary_weight
            
        self.adaptive_weights = adaptive_weights
        
        # 损失平衡器
        self.loss_balancer = LossBalancer()
        
    def forward(self, outputs: Dict, targets: torch.Tensor) -> Tuple[torch.Tensor, Dict]:
        """
        计算综合损失
        Args:
            outputs: 模型输出字典
            targets: [B, 1, H, W] 真实标签
        Returns:
            total_loss: 总损失
            loss_dict: 各组件损失
        """
        # 获取主预测
        if 'refined_pred' in outputs:
            main_pred = outputs['refined_pred'][:, 1:2]  # 取前景通道
        elif 'ensemble_pred' in outputs:
            main_pred = outputs['ensemble_pred']
        else:
            main_pred = outputs['main_pred']
        
        # 计算各组件损失
        losses = {}
        
        # 基础损失
        losses['bce'] = self.bce_loss(main_pred, targets)
        losses['dice'] = self.dice_loss(main_pred, targets)
        
        # 高级损失
        losses['edge'] = self.edge_loss(main_pred, targets)
        losses['hard_mining'] = self.hard_mining_loss(main_pred, targets)
        losses['physics'] = self.physics_loss(outputs, targets)
        losses['multiscale'] = self.multiscale_loss(outputs, targets)
        losses['boundary'] = self.boundary_loss(main_pred, targets)
        
        # 应用权重
        if self.adaptive_weights:
            weighted_losses = {
                'bce': torch.abs(self.bce_weight) * losses['bce'],
                'dice': torch.abs(self.dice_weight) * losses['dice'],
                'edge': torch.abs(self.edge_weight) * losses['edge'],
                'hard_mining': torch.abs(self.hard_mining_weight) * losses['hard_mining'],
                'physics': torch.abs(self.physics_weight) * losses['physics'],
                'multiscale': torch.abs(self.multiscale_weight) * losses['multiscale'],
                'boundary': torch.abs(self.boundary_weight) * losses['boundary']
            }
        else:
            weighted_losses = {
                'bce': self.bce_weight * losses['bce'],
                'dice': self.dice_weight * losses['dice'],
                'edge': self.edge_weight * losses['edge'],
                'hard_mining': self.hard_mining_weight * losses['hard_mining'],
                'physics': self.physics_weight * losses['physics'],
                'multiscale': self.multiscale_weight * losses['multiscale'],
                'boundary': self.boundary_weight * losses['boundary']
            }
        
        # 损失平衡
        balanced_losses = self.loss_balancer(weighted_losses)
        
        # 总损失
        total_loss = sum(balanced_losses.values())
        
        # 返回详细损失信息
        loss_dict = {**losses, **balanced_losses, 'total': total_loss}
        
        return total_loss, loss_dict
    
    def get_loss_weights(self) -> Dict:
        """获取当前损失权重"""
        if self.adaptive_weights:
            return {
                'bce_weight': torch.abs(self.bce_weight).item(),
                'dice_weight': torch.abs(self.dice_weight).item(),
                'edge_weight': torch.abs(self.edge_weight).item(),
                'hard_mining_weight': torch.abs(self.hard_mining_weight).item(),
                'physics_weight': torch.abs(self.physics_weight).item(),
                'multiscale_weight': torch.abs(self.multiscale_weight).item(),
                'boundary_weight': torch.abs(self.boundary_weight).item()
            }
        else:
            return {
                'bce_weight': self.bce_weight,
                'dice_weight': self.dice_weight,
                'edge_weight': self.edge_weight,
                'hard_mining_weight': self.hard_mining_weight,
                'physics_weight': self.physics_weight,
                'multiscale_weight': self.multiscale_weight,
                'boundary_weight': self.boundary_weight
            }


class DiceLoss(nn.Module):
    """Dice Loss实现"""
    def __init__(self, smooth=1.0):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)
        
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum()
        
        dice = (2 * intersection + self.smooth) / (union + self.smooth)
        
        return 1 - dice


class LossBalancer(nn.Module):
    """
    损失平衡器 - 动态平衡不同损失组件
    """
    def __init__(self, alpha=0.1):
        super(LossBalancer, self).__init__()
        self.alpha = alpha
        self.loss_history = {}
        
    def forward(self, losses: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """平衡损失"""
        balanced_losses = {}
        
        for name, loss in losses.items():
            if name not in self.loss_history:
                self.loss_history[name] = []
            
            # 记录损失历史
            self.loss_history[name].append(loss.item())
            
            # 保持最近10个损失值
            if len(self.loss_history[name]) > 10:
                self.loss_history[name] = self.loss_history[name][-10:]
            
            # 计算损失的相对重要性
            if len(self.loss_history[name]) > 5:
                recent_avg = np.mean(self.loss_history[name][-5:])
                overall_avg = np.mean(self.loss_history[name])
                
                # 如果最近损失增加，给予更多权重
                dynamic_weight = 1.0 + self.alpha * (recent_avg / (overall_avg + 1e-8) - 1.0)
                dynamic_weight = max(0.5, min(2.0, dynamic_weight))  # 限制权重范围
            else:
                dynamic_weight = 1.0
            
            balanced_losses[f'balanced_{name}'] = loss * dynamic_weight
        
        return balanced_losses


def create_90iou_loss(adaptive_weights=True):
    """创建90% IoU目标损失函数"""
    return ViTPhysics90IoULoss(
        bce_weight=1.0,
        dice_weight=2.0,
        edge_weight=3.0,
        hard_mining_weight=2.0,
        physics_weight=1.5,
        multiscale_weight=1.0,
        boundary_weight=2.5,
        adaptive_weights=adaptive_weights
    )


if __name__ == "__main__":
    # 测试损失函数
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    criterion = create_90iou_loss(adaptive_weights=True).to(device)
    
    # 模拟数据
    batch_size = 4
    outputs = {
        'main_pred': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'physics_pred': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'vit_preds': [torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device) for _ in range(4)],
        'ensemble_pred': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'refined_pred': torch.softmax(torch.randn(batch_size, 2, 416, 416), dim=1).to(device),
        'reflection_map': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'physics_confidence': torch.sigmoid(torch.randn(batch_size, 1, 1, 1)).to(device)
    }
    targets = torch.randint(0, 2, (batch_size, 1, 416, 416)).float().to(device)
    
    # 计算损失
    total_loss, loss_dict = criterion(outputs, targets)
    
    print("90% IoU损失函数测试:")
    print(f"总损失: {total_loss.item():.4f}")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.item():.4f}")
    
    print("\n当前损失权重:")
    weights = criterion.get_loss_weights()
    for key, value in weights.items():
        print(f"  {key}: {value:.4f}")
    
    print("\n✅ 90% IoU损失函数测试成功!")