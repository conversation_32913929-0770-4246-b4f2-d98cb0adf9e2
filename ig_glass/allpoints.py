import os
import time
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import check_mkdir, crf_refine
from ig_glass.gdnet import GDNet
#from config import gdd_testing_root, gdd_results_root, backbone_path

# device set
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# parameter set
ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
exp_name = 'GDNet'
args = {
    'snapshot': '200',
    'scale': 416,
    'crf': True,  
    'glass_threshold': 0.98,  # 玻璃区域识别阈值
}

# 预处理
img_transform = transforms.Compose([
    transforms.Resize((args['scale'], args['scale'])),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

to_pil = transforms.ToPILImage()

# 动态亮度调整目标范围
BRIGHTNESS_TARGET_MIN = 100
BRIGHTNESS_TARGET_MAX = 120


def calculate_brightness(image_path):
    """计算图像亮度"""
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])
    return brightness


def calculate_feature_points(image_path):
    """计算图像特征点"""
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    orb = cv2.ORB_create(nfeatures=3000)
    keypoints = orb.detect(image, None)
    return keypoints, len(keypoints)


def adjust_image_brightness(image, adjustment):
    """根据调整值调整图像亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    v = np.clip(v.astype(np.int32) + int(adjustment), 0, 255).astype(np.uint8)
    hsv = cv2.merge((h, s, v))
    return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)


def adjust_image_brightness_dynamically(image, target_min, target_max):
    """根据图像亮度的分布动态调整亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    v = hsv[:, :, 2]  # 提取亮度通道

    # 计算亮度的直方图
    hist = cv2.calcHist([v], [0], None, [256], [0, 256])
    cumulative_sum = np.cumsum(hist)
    median_brightness = np.searchsorted(cumulative_sum, cumulative_sum[-1] // 2)

    # 动态目标亮度，基于图像的亮度分布
    current_mean = np.mean(v)
    current_std = np.std(v)

    # 根据当前亮度的均值和标准差，动态调整目标亮度
    if current_mean < target_min:
        adjustment = target_min - median_brightness
    elif current_mean > target_max:
        adjustment = target_max - median_brightness
    else:
        adjustment = (target_max + target_min) / 2 - median_brightness

    # 调整图像亮度
    adjusted_image = adjust_image_brightness(image, adjustment)

    return adjusted_image


def detect_glass_and_adjust_brightness(image_folder, output_folder, net, glass_threshold):
    """检测玻璃区域并调整亮度"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    brightness_list = []
    feature_points_list = []

    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)

                # Step 2: 读取原始图像，根据亮度分布动态调整亮度
        original_image = cv2.imread(image_path)
        adjusted_image = adjust_image_brightness_dynamically(original_image, BRIGHTNESS_TARGET_MIN, BRIGHTNESS_TARGET_MAX)
        temp_path = os.path.join(output_folder, 'temp.png')
        cv2.imwrite(temp_path, adjusted_image)

        # Step 1: Identify glass region and boundaries
        img_pil = Image.open(temp_path)
        if img_pil.mode != 'RGB':
            img_pil = img_pil.convert('RGB')
        w, h = img_pil.size
        img_var = Variable(img_transform(img_pil).unsqueeze(0)).cuda(device_ids[0])
        f1, f2, f3 = net(img_var)
        f3 = f3.data.squeeze(0).cpu()
        f3_resized = np.array(transforms.Resize((h, w))(to_pil(f3)))
        #glass_mask = (f3_resized > glass_threshold).astype(np.uint8) if f3_resized.max() > glass_threshold else np.zeros((h, w), dtype=np.uint8)

        # Optional: Apply CRF if activated
        if args['crf']:
            adjusted_image  = cv2.imread(temp_path)
            f3_resized = crf_refine(adjusted_image , f3_resized) 
                # 使用 glass_threshold 来调整玻璃区域识别的精度
        glass_mask = (f3_resized > glass_threshold).astype(np.uint8) if f3_resized.max() > glass_threshold else np.zeros((h, w), dtype=np.uint8)

        
        # 计算调整后的图像亮度
        adjusted_brightness = np.mean(cv2.cvtColor(adjusted_image, cv2.COLOR_BGR2HSV)[:, :, 2])


        # Calculate feature points after brightness adjustment
        keypoints, feature_points = calculate_feature_points(image_path)
        feature_points_list.append((image_file, feature_points))

        # Step 3: 绘制特征点，包括玻璃区域内的均匀分布
        if len(glass_mask) > 0:
            glass_coords = np.column_stack(np.where(glass_mask > 0))
            glass_area = len(glass_coords)  # 玻璃区域的面积

            # Dynamic feature point distribution 
            density_factor = 0.03  # 3 point per 100 pixels
            num_points = int(glass_area * density_factor)

            if len(glass_coords) > 0:
                indices = np.random.choice(len(glass_coords), size=min(num_points, len(glass_coords)), replace=False)
                selected_points_in_glass = glass_coords[indices]
                # Ensure uniform distribution within the glass region
                keypoints_in_glass = [cv2.KeyPoint(float(pt[1]), float(pt[0]), size=5) for pt in selected_points_in_glass]
            else:
                keypoints_in_glass = []

            # Calculate feature points outside glass region
            keypoints_outside_glass = [kp for kp in keypoints if not glass_mask[int(kp.pt[1]), int(kp.pt[0])]]

            # Combine keypoints from inside and outside glass region
            final_keypoints = keypoints #keypoints_in_glass + keypoints_outside_glass
        else:
            final_keypoints = keypoints

        # Step 4: 在图像上绘制特征点和亮度信息
        image_with_keypoints = cv2.drawKeypoints(adjusted_image, final_keypoints, None, color=(0, 255, 0))

        # Add notes
        #cv2.putText(image_with_keypoints, f'Brightness: {adjusted_brightness:.2f}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2) 
        #cv2.putText(image_with_keypoints, f'Feature Points: {feature_points}', (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2) 

        # Save results
        output_path = os.path.join(output_folder, image_file)
        cv2.imwrite(output_path, image_with_keypoints)

    # Delete temporary file
    if os.path.exists(temp_path):
        os.remove(temp_path)

    return brightness_list, feature_points_list


def main():
    # Step 1: Load GDNet model
    net = GDNet(backbone_path=backbone_path).cuda(device_ids[0])
    if len(args['snapshot']) > 0:
        print(f'Load snapshot {args["snapshot"]} for testing')
        net.load_state_dict(torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')))
        print(f'Load {os.path.join(ckpt_path, exp_name, args["snapshot"] + ".pth")} succeed!')

    net.eval()

    # Step 5: Input paths (Modify here to change dataset paths)
    image_folder = gdd_testing_root  # Set your dataset input folder here
    output_folder = gdd_results_root  # Set your output folder here

    # Process images, detect glass region, adjust brightness, and draw keypoints
    brightness_list, feature_points_list = detect_glass_and_adjust_brightness(image_folder, output_folder, net, args['glass_threshold'])


if __name__ == '__main__':
    main()
