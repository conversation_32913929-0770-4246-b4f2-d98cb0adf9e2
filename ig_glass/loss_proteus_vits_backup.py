"""
 @Time    : 2024
 <AUTHOR> Glennine

 @Project : IG_SLAM
 @File    : loss_proteus_vits.py
 @Function: Proteus ViT-S融合SCSA和Glass Physics优势的损失函数

融合优势：
- SCSA CompositeLoss: 87.9% IoU验证的最优权重配置
- Glass Physics一致性: 物理约束增强预测质量
- EdgeSaliencyLoss: 专业边缘检测损失
- 动态权重调整: 自适应损失平衡
"""
import torch
import torch.nn as nn
import torch.nn.functional as F


###################################################################
# ############## SCSA优势损失函数 ##############################
###################################################################
class SCSAEdgeAwareBCELoss(nn.Module):
    """
    从SCSA移植的边缘感知BCE损失函数
    87.9% IoU验证的核心组件
    """
    def __init__(self, edge_weight=1.3, body_weight=0.7):
        super(SCSAEdgeAwareBCELoss, self).__init__()
        self.edge_weight = edge_weight
        self.body_weight = body_weight
        
        # 拉普拉斯核 - 与SCSA保持一致
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1., 8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))
        
        # SCSA模块特有的通道注意力权重
        self.channel_weight = 1.1

    def forward(self, pred, target, eps=1e-7):
        # 确保输入在有效范围内
        pred = torch.clamp(pred, min=eps, max=1.0 - eps)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 数值稳定性检查
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)
            
        if torch.isnan(target).any() or torch.isinf(target).any():
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)
        
        # 确保维度正确
        if pred.dim() == 3:
            pred = pred.unsqueeze(1)
        if target.dim() == 3:
            target = target.unsqueeze(1)
            
        if pred.size(1) == 0:
            pred = torch.ones_like(target) * 0.5
        
        try:
            # 从目标提取边缘 - SCSA验证的方法
            target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel.to(target.device), padding=1)))
            
            # 创建边缘感知权重图
            edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
            
            # 计算标准BCE损失
            bce_loss = -(target * torch.log(pred + eps) + (1 - target) * torch.log(1 - pred + eps))
            
            # 应用边缘感知加权
            weighted_loss = bce_loss * edge_weight_map
            
            # SCSA通道感知权重
            weighted_loss = weighted_loss * self.channel_weight
            
            return weighted_loss.mean()
        except Exception as e:
            # 后备方案
            return F.binary_cross_entropy(pred, target, reduction='mean')


class SCSALightIOU(nn.Module):
    """
    从SCSA移植的轻量级IoU损失函数
    87.9% IoU的核心优化目标
    """
    def __init__(self, size_average=True):
        super(SCSALightIOU, self).__init__()
        self.channel_weight = 1.1  # SCSA特有权重
        self.size_average = size_average
        
    def forward(self, pred, target):
        # 确保在有效范围内
        pred = torch.clamp(pred, min=0.0, max=1.0)
        target = torch.clamp(target, min=0.0, max=1.0)

        # 数值稳定性检查
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)

        if torch.isnan(target).any() or torch.isinf(target).any():
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)

        # 计算交集和并集
        intersection = torch.sum(target * pred, dim=(1, 2, 3))
        union = torch.sum(target, dim=(1, 2, 3)) + torch.sum(pred, dim=(1, 2, 3)) - intersection
        
        # 避免除零
        union = torch.clamp(union, min=1e-7)
        
        # 计算IoU损失
        iou = (intersection / union).mean()
        iou_loss = (1.0 - iou) * self.channel_weight
        
        # 确保非负
        iou_loss = torch.clamp(iou_loss, min=0.0)
        
        if self.size_average:
            return iou_loss.mean()
        else:
            return iou_loss


###################################################################
# ############## Glass Physics优势损失函数 ######################
###################################################################
class PhysicsConsistencyLoss(nn.Module):
    """
    从Glass Physics移植的物理一致性损失
    基于玻璃物理特性的约束优化
    """
    def __init__(self, reflection_weight=0.8, edge_weight=0.6):
        super(PhysicsConsistencyLoss, self).__init__()
        self.reflection_weight = reflection_weight
        self.edge_weight = edge_weight
        
        # 反射模式检测核
        self.register_buffer('reflection_kernel', torch.tensor([
            [0, -1, 0], 
            [-1, 4, -1], 
            [0, -1, 0]
        ]).float().view(1, 1, 3, 3))
        
        # Sobel边缘检测核
        self.register_buffer('sobel_x', torch.tensor([
            [-1, 0, 1], 
            [-2, 0, 2], 
            [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y', torch.tensor([
            [-1, -2, -1], 
            [ 0,  0,  0], 
            [ 1,  2,  1]
        ]).float().view(1, 1, 3, 3))
        
    def forward(self, pred, target, image):
        """
        Args:
            pred: 预测结果 [B, 1, H, W]
            target: 真值 [B, 1, H, W]
            image: 原始图像 [B, 3, H, W]
        """
        device = pred.device
        
        # 计算亮度
        if image.size(1) == 3:
            luminance = 0.299 * image[:, 0:1] + 0.587 * image[:, 1:2] + 0.114 * image[:, 2:3]
        else:
            luminance = image
        
        # 反射一致性：玻璃区域应该有较高的亮度变化
        reflection_kernel = self.reflection_kernel.to(device)
        luminance_grad = F.conv2d(luminance, reflection_kernel, padding=1)
        luminance_grad = torch.abs(luminance_grad)
        
        # 预测为玻璃的区域
        pred_binary = (torch.sigmoid(pred) > 0.5).float()
        target_binary = (target > 0.5).float()
        
        # 玻璃区域的亮度变化应该更明显
        glass_regions_pred = pred_binary * luminance_grad
        glass_regions_target = target_binary * luminance_grad
        
        # 反射一致性损失
        reflection_consistency = F.mse_loss(glass_regions_pred, glass_regions_target)
        
        # 边缘物理一致性：玻璃边缘应该有特定的梯度模式
        gray = torch.mean(image, dim=1, keepdim=True)
        edge_x = F.conv2d(gray, self.sobel_x.to(device), padding=1)
        edge_y = F.conv2d(gray, self.sobel_y.to(device), padding=1)
        edge_magnitude = torch.sqrt(edge_x**2 + edge_y**2)
        
        # 提取预测和真值的边缘
        pred_edges = self._extract_edges(pred)
        target_edges = self._extract_edges(target)
        
        # 边缘区域的物理一致性
        edge_consistency = F.mse_loss(pred_edges * edge_magnitude, target_edges * edge_magnitude)
        
        total_loss = (self.reflection_weight * reflection_consistency +
                     self.edge_weight * edge_consistency)
        
        return total_loss
    
    def _extract_edges(self, x):
        """提取边缘"""
        sobel_x = F.conv2d(x, self.sobel_x.to(x.device), padding=1)
        sobel_y = F.conv2d(x, self.sobel_y.to(x.device), padding=1)
        edges = torch.sqrt(sobel_x**2 + sobel_y**2)
        return torch.sigmoid(edges)


class EdgeSaliencyLoss(nn.Module):
    """
    专业的边缘显著性损失函数
    来自Glass Physics的EdgeSaliencyLoss优化版本
    """
    def __init__(self, alpha_sal=0.7):
        super(EdgeSaliencyLoss, self).__init__()
        self.alpha_sal = alpha_sal
        
        # 拉普拉斯核
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1.,  8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))
        
    def forward(self, pred, target):
        """
        Args:
            pred: [B, 1, H, W]
            target: [B, 1, H, W]
        """
        # 严格的数值稳定性处理
        pred = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 检查NaN和Inf
        pred = torch.where(torch.isnan(pred) | torch.isinf(pred), torch.ones_like(pred) * 0.5, pred)
        target = torch.where(torch.isnan(target) | torch.isinf(target), torch.zeros_like(target), target)
        
        try:
            # 提取边缘 - 使用更稳定的方法
            target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel.to(target.device), padding=1)))
            pred_edges = F.relu(torch.tanh(F.conv2d(pred, self.laplacian_kernel.to(pred.device), padding=1)))
            
            # 再次检查边缘结果
            target_edges = torch.clamp(target_edges, min=0.0, max=1.0)
            pred_edges = torch.clamp(pred_edges, min=0.0, max=1.0)
            
            # 边缘显著性损失
            edge_loss = F.mse_loss(pred_edges, target_edges)
            
        except Exception as e:
            print(f"边缘检测错误: {e}")
            edge_loss = torch.tensor(0.0, device=pred.device, requires_grad=True)
        
        # 区域一致性损失 - 使用稳定的BCE
        try:
            region_loss = F.binary_cross_entropy(pred, target, reduction='mean')
        except Exception as e:
            print(f"BCE损失错误: {e}")
            region_loss = torch.tensor(0.0, device=pred.device, requires_grad=True)
        
        # 组合损失
        total_loss = self.alpha_sal * edge_loss + (1 - self.alpha_sal) * region_loss
        
        # 最终检查
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            total_loss = torch.tensor(1.0, device=pred.device, requires_grad=True)
        
        return total_loss


###################################################################
# ############## 动态权重调整 ###################################
###################################################################
class DynamicWeightAdjuster(nn.Module):
    """动态权重调整器"""
    def __init__(self, initial_weights):
        super(DynamicWeightAdjuster, self).__init__()
        self.weights = nn.Parameter(torch.tensor(initial_weights, dtype=torch.float32))
        
    def forward(self, loss_components):
        """
        Args:
            loss_components: [loss1, loss2, loss3, ...]
        Returns:
            normalized_weights
        """
        # 使用softmax确保权重和为1
        normalized_weights = F.softmax(self.weights, dim=0)
        return normalized_weights


###################################################################
# ############## 混合损失函数 ###################################
###################################################################
class ProteusViTSHybridLoss(nn.Module):
    """
    Proteus ViT-S混合损失函数
    融合SCSA和Glass Physics的最佳策略
    
    权重配置基于87.9% IoU的SCSA验证：
    - BCE: 0.1 (类别平衡)
    - IoU: 0.7 (核心优化目标)
    - Edge: 0.2 (边缘质量)
    - Physics: 0.1 (物理约束)
    """
    def __init__(self, 
                 bce_weight=0.1,
                 iou_weight=0.7, 
                 edge_weight=0.2,
                 physics_weight=0.1,
                 consistency_weight=0.05):
        super(ProteusViTSHybridLoss, self).__init__()
        
        # 损失权重 - 基于SCSA 87.9% IoU的最优配置
        self.bce_weight = bce_weight
        self.iou_weight = iou_weight
        self.edge_weight = edge_weight
        self.physics_weight = physics_weight
        self.consistency_weight = consistency_weight
        
        # SCSA优势损失函数
        self.bce_loss = SCSAEdgeAwareBCELoss(edge_weight=1.3, body_weight=0.7)
        self.iou_loss = SCSALightIOU(size_average=True)
        
        # Glass Physics优势损失函数
        self.edge_loss = EdgeSaliencyLoss(alpha_sal=0.7)
        self.physics_loss = PhysicsConsistencyLoss(reflection_weight=0.8, edge_weight=0.6)
        
        # 动态权重调整
        self.dynamic_adjuster = DynamicWeightAdjuster([0.7, 0.2, 0.1])
        
    def forward(self, predictions, target, image=None):
        """
        Args:
            predictions: dict with keys ['main_pred', 'edge_pred', 'final_pred', 'ensemble_pred', 'refined_pred']
            target: [B, 1, H, W]
            image: [B, 3, H, W] (optional, for physics consistency)
        """
        # 确保target维度正确
        if target.dim() == 3:
            target = target.unsqueeze(1)
        
        # 提取预测结果
        main_pred = predictions['main_pred']
        edge_pred = predictions['edge_pred'] 
        final_pred = predictions['final_pred']
        ensemble_pred = predictions['ensemble_pred']
        refined_pred = predictions.get('refined_pred', ensemble_pred)
        
        # 处理refined_pred的双通道输出
        if refined_pred.size(1) == 2:
            refined_pred = refined_pred[:, 1:2]  # 取前景通道
        
        # 1. SCSA BCE损失 - 87.9% IoU核心
        bce_main = self.bce_loss(main_pred, target)
        bce_edge = self.bce_loss(edge_pred, target)
        bce_final = self.bce_loss(final_pred, target)
        bce_ensemble = self.bce_loss(ensemble_pred, target)
        bce_refined = self.bce_loss(refined_pred, target)
        
        total_bce = (0.2 * bce_main + 0.2 * bce_edge + 0.2 * bce_final + 
                    0.3 * bce_ensemble + 0.3 * bce_refined)
        
        # 2. SCSA IoU损失 - 87.9% IoU的关键
        iou_main = self.iou_loss(main_pred, target)
        iou_edge = self.iou_loss(edge_pred, target)
        iou_final = self.iou_loss(final_pred, target)
        iou_ensemble = self.iou_loss(ensemble_pred, target)
        iou_refined = self.iou_loss(refined_pred, target)
        
        total_iou = (0.2 * iou_main + 0.2 * iou_edge + 0.2 * iou_final + 
                    0.3 * iou_ensemble + 0.3 * iou_refined)
        
        # 3. Glass Physics边缘损失
        edge_main = self.edge_loss(main_pred, target)
        edge_final = self.edge_loss(final_pred, target)
        edge_ensemble = self.edge_loss(ensemble_pred, target)
        edge_refined = self.edge_loss(refined_pred, target)
        
        total_edge = (0.25 * edge_main + 0.25 * edge_final + 
                     0.25 * edge_ensemble + 0.25 * edge_refined)
        
        # 4. Physics一致性损失（如果提供图像）
        if image is not None:
            physics_ensemble = self.physics_loss(ensemble_pred, target, image)
            physics_refined = self.physics_loss(refined_pred, target, image)
            total_physics = 0.5 * (physics_ensemble + physics_refined)
        else:
            total_physics = torch.tensor(0.0, device=target.device)
        
        # 5. 多预测一致性损失
        consistency_loss = (
            F.mse_loss(main_pred, ensemble_pred) +
            F.mse_loss(edge_pred, ensemble_pred) +
            F.mse_loss(final_pred, ensemble_pred) +
            F.mse_loss(refined_pred, ensemble_pred)
        ) / 4
        
        # 动态权重调整
        loss_components = torch.stack([total_iou, total_edge, total_bce])
        dynamic_weights = self.dynamic_adjuster(loss_components)
        
        # 计算总损失 - 使用SCSA验证的最优权重
        total_loss = (
            self.bce_weight * dynamic_weights[2].item() * total_bce +
            self.iou_weight * dynamic_weights[0].item() * total_iou +
            self.edge_weight * dynamic_weights[1].item() * total_edge +
            self.physics_weight * total_physics +
            self.consistency_weight * consistency_loss
        )
        
        # 返回详细的损失信息
        loss_dict = {
            'total_loss': total_loss,
            'bce_loss': total_bce,
            'iou_loss': total_iou,
            'edge_loss': total_edge,
            'physics_loss': total_physics,
            'consistency_loss': consistency_loss,
            'dynamic_weights': dynamic_weights.detach()
        }
        
        return total_loss, loss_dict


###################################################################
# ############## 工厂函数 #######################################
###################################################################
def create_proteus_vits_loss(**kwargs):
    """创建Proteus ViT-S混合损失函数"""
    return ProteusViTSHybridLoss(**kwargs)


# 测试函数
if __name__ == "__main__":
    # 测试损失函数
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    criterion = create_proteus_vits_loss()
    
    # 模拟输出和目标
    B, H, W = 2, 416, 416
    predictions = {
        'main_pred': torch.sigmoid(torch.randn(B, 1, H, W)),
        'edge_pred': torch.sigmoid(torch.randn(B, 1, H, W)),
        'final_pred': torch.sigmoid(torch.randn(B, 1, H, W)),
        'ensemble_pred': torch.sigmoid(torch.randn(B, 1, H, W)),
        'refined_pred': torch.randn(B, 2, H, W)
    }
    
    targets = torch.rand(B, 1, H, W)
    image = torch.rand(B, 3, H, W)
    
    # 计算损失
    total_loss, loss_dict = criterion(predictions, targets, image)
    
    print('✅ Proteus ViT-S混合损失函数测试成功!')
    print(f'总损失: {total_loss.item():.4f}')
    for key, value in loss_dict.items():
        if key != 'dynamic_weights':
            print(f'  {key}: {value.item():.4f}')
        else:
            print(f'  {key}: {value.tolist()}') 