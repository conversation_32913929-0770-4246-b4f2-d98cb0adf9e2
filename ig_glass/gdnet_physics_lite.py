"""
轻量化的Physics模型 - 结合SCSA的优势
目标: 从85.63%提升到87.5%+
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF
from ig_glass.attention_scsa import SCSA


###################################################################
# ############## 轻量化物理特征提取 ##########################
###################################################################

class LiteEdgeDetector(nn.Modu<PERSON>):
    """轻量化边缘检测器 - 只保留最有效的边缘特征"""
    def __init__(self, in_channels):
        super(LiteEdgeDetector, self).__init__()
        
        # 简化的边缘检测
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, in_channels//8, 3, 1, 1),
            nn.BatchNorm2d(in_channels//8),
            nn.ReLU(inplace=True)
        )
        
        # 固定的拉普拉斯核 - 不参与训练
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1.,  8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))
        
    def forward(self, x):
        # 学习的边缘特征
        edge_feat = self.edge_conv(x)
        
        # 固定的拉普拉斯边缘检测
        gray = torch.mean(x, dim=1, keepdim=True)
        laplacian_edges = F.conv2d(gray, self.laplacian_kernel, padding=1)
        laplacian_edges = torch.sigmoid(laplacian_edges)
        
        # 扩展拉普拉斯边缘到与edge_feat相同的通道数
        laplacian_expanded = laplacian_edges.repeat(1, edge_feat.size(1), 1, 1)
        
        # 简单融合
        fused_edges = edge_feat * (1 + laplacian_expanded)
        
        return fused_edges


class LiteReflectionDetector(nn.Module):
    """轻量化反射检测器 - 检测亮度异常"""
    def __init__(self, in_channels):
        super(LiteReflectionDetector, self).__init__()
        
        # 亮度异常检测
        self.brightness_detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, in_channels//8, 3, 1, 1),
            nn.BatchNorm2d(in_channels//8),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        brightness_feat = self.brightness_detector(x)
        return brightness_feat


###################################################################
# ############## 主网络架构 ################################
###################################################################

class GDNetPhysicsLite(nn.Module):
    """
    轻量化Physics模型 - 结合SCSA优势
    只保留最有效的物理特征，使用SCSA进行特征融合
    """
    def __init__(self, backbone_path=None, crf_iter=3, trainable_crf=True):
        super(GDNetPhysicsLite, self).__init__()
        
        # 骨干网络
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0
        self.layer1 = resnext.layer1
        self.layer2 = resnext.layer2
        self.layer3 = resnext.layer3
        self.layer4 = resnext.layer4

        # 轻量化物理特征提取
        self.edge_detector = LiteEdgeDetector(512)  # layer2: 512
        self.reflection_detector = LiteReflectionDetector(1024)  # layer3: 1024

        # 特征通道调整
        self.h5_conv = nn.Sequential(
            nn.Conv2d(2048, 512, 1, 1, 0),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )
        self.h4_conv = nn.Sequential(
            nn.Conv2d(1024, 256, 1, 1, 0),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        self.h3_conv = nn.Sequential(
            nn.Conv2d(512, 128, 1, 1, 0),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True)
        )
        
        # 物理特征通道调整
        edge_dim = 512 // 8  # 64
        reflection_dim = 1024 // 8  # 128
        physics_dim = edge_dim + reflection_dim  # 192
        
        self.physics_conv = nn.Sequential(
            nn.Conv2d(physics_dim, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )

        # 使用SCSA进行主特征融合
        main_fusion_dim = 512 + 256 + 128  # 896
        self.main_scsa = SCSA(
            dim=main_fusion_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.main_fusion_conv = nn.Sequential(
            nn.Conv2d(main_fusion_dim, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )

        # 使用SCSA进行最终融合
        final_fusion_dim = 512 + 64  # 576
        self.final_scsa = SCSA(
            dim=final_fusion_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.final_fusion_conv = nn.Sequential(
            nn.Conv2d(final_fusion_dim, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )

        # 预测头
        self.main_predict = nn.Conv2d(512, 1, 3, 1, 1)
        self.physics_predict = nn.Conv2d(64, 1, 3, 1, 1)
        self.final_predict = nn.Conv2d(256, 1, 3, 1, 1)

        # CRF后处理 - 使用与SCSA相同的参数
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,  # 与SCSA一致
            gaussian_weight=5.0,    # 与SCSA一致
            bilateral_spatial_sigma=40.0,  # 与SCSA一致
            bilateral_color_sigma=3.0,     # 与SCSA一致
            gaussian_sigma=1.5,            # 与SCSA一致
            trainable=trainable_crf
        )

        # 自适应权重
        self.adaptive_weights = nn.Parameter(torch.tensor([0.4, 0.2, 0.4]), requires_grad=True)

        # 上采样模块
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)

        for m in self.modules():
            if isinstance(m, nn.ReLU):
                m.inplace = True

    def forward(self, x):
        # 提取骨干特征
        layer0 = self.layer0(x)  # [B, 64, H/2, W/2]
        layer1 = self.layer1(layer0)  # [B, 256, H/4, W/4]
        layer2 = self.layer2(layer1)  # [B, 512, H/8, W/8]
        layer3 = self.layer3(layer2)  # [B, 1024, H/16, W/16]
        layer4 = self.layer4(layer3)  # [B, 2048, H/32, W/32]

        # 轻量化物理特征提取
        edge_feat = self.edge_detector(layer2)  # [B, 64, H/8, W/8]
        reflection_feat = self.reflection_detector(layer3)  # [B, 128, H/16, W/16]

        # 统一物理特征尺度到layer3的尺度
        target_size = layer3.shape[2:]
        edge_feat_up = F.interpolate(edge_feat, size=target_size, mode='bilinear', align_corners=True)
        
        # 融合物理特征
        physics_combined = torch.cat([edge_feat_up, reflection_feat], dim=1)
        physics_features = self.physics_conv(physics_combined)

        # 主特征融合
        h5_conv = self.h5_conv(layer4)
        h4_conv = self.h4_conv(layer3)
        h3_conv = self.h3_conv(layer2)
        
        # 统一尺度到layer3
        h5_up = F.interpolate(h5_conv, size=target_size, mode='bilinear', align_corners=True)
        h3_down = F.adaptive_avg_pool2d(h3_conv, target_size)
        
        # 主特征SCSA融合
        main_combined = torch.cat([h5_up, h4_conv, h3_down], dim=1)
        main_attended = self.main_scsa(main_combined)
        main_features = self.main_fusion_conv(main_attended)

        # 最终SCSA融合
        final_combined = torch.cat([main_features, physics_features], dim=1)
        final_attended = self.final_scsa(final_combined)
        final_features = self.final_fusion_conv(final_attended)

        # 预测
        main_pred = self.main_predict(main_features)
        physics_pred = self.physics_predict(physics_features)
        final_pred = self.final_predict(final_features)

        # 上采样到原始尺寸
        main_pred = F.interpolate(main_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        physics_pred = F.interpolate(physics_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        final_pred = F.interpolate(final_pred, size=x.size()[2:], mode='bilinear', align_corners=True)

        # 应用sigmoid
        main_pred_prob = torch.sigmoid(main_pred)
        physics_pred_prob = torch.sigmoid(physics_pred)
        final_pred_prob = torch.sigmoid(final_pred)

        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = (weights[0] * main_pred_prob + 
                        weights[1] * physics_pred_prob + 
                        weights[2] * final_pred_prob)
        
        ensemble_pred = torch.clamp(ensemble_pred, min=1e-7, max=1.0 - 1e-7)

        # CRF后处理
        bg_logits = torch.log(1 - ensemble_pred)
        fg_logits = torch.log(ensemble_pred)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # 归一化输入图像
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)

        # 应用CRF
        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - ensemble_pred, ensemble_pred], dim=1)

        return {
            'main_pred': main_pred_prob,
            'physics_pred': physics_pred_prob,
            'final_pred': final_pred_prob,
            'ensemble_pred': ensemble_pred,
            'refined_pred': refined_predict
        }


def create_physics_lite_model(backbone_path=None, crf_iter=3, trainable_crf=True):
    """创建轻量化Physics模型"""
    return GDNetPhysicsLite(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )


# 使用说明
"""
这个轻量化Physics模型的改进点：

1. 🔄 简化物理模块：只保留边缘检测和反射检测
2. ⚡ 使用SCSA替换复杂的物理融合
3. 🎯 统一CRF参数与SCSA模型保持一致
4. 🔧 减少参数量，防止过拟合
5. 📈 预期IoU从85.63%提升到87.5%+

训练脚本：
python train_physics_lite.py --backbone_path resnext101_32x8.pth

测试脚本：
python test_physics_lite.py --model_path best_model.pth
""" 