"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : loss_scsa_enhanced_90.py
 @Function: 基于87.95% SCSA成功经验的增强损失函数，目标90% IoU

基于SCSA成功经验的损失优化：
1. 保留SCSA的核心损失结构
2. 增强边缘感知损失 - 针对玻璃边缘优化
3. 超级难样本挖掘 - 提升最后3% IoU
4. 多尺度深度监督 - SCSA多层预测优化
5. 自适应CRF损失 - 配合7次迭代CRF
6. 数值稳定性增强 - 避免训练崩溃
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple


class SuperEdgeAwareLoss(nn.Module):
    """
    超级边缘感知损失 - 基于SCSA成功经验优化
    针对玻璃边缘的亚像素级精度
    """
    def __init__(self, edge_weight=4.0, gradient_weight=3.0, multi_scale_weight=2.0):
        super(SuperEdgeAwareLoss, self).__init__()
        self.edge_weight = edge_weight
        self.gradient_weight = gradient_weight
        self.multi_scale_weight = multi_scale_weight
        
        # 多尺度Sobel核 - 匹配gdnet_scsa_enhanced_90.py的设计
        self.register_buffer('sobel_x_3', torch.tensor([
            [-1, 0, 1], [-2, 0, 2], [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y_3', torch.tensor([
            [-1, -2, -1], [0, 0, 0], [1, 2, 1]
        ]).float().view(1, 1, 3, 3))
        
        # 5x5 Sobel - 更精细的边缘检测
        self.register_buffer('sobel_x_5', torch.tensor([
            [-1, -2, 0, 2, 1], [-4, -8, 0, 8, 4], [-6, -12, 0, 12, 6],
            [-4, -8, 0, 8, 4], [-1, -2, 0, 2, 1]
        ]).float().view(1, 1, 5, 5) / 100.0)
        
        self.register_buffer('sobel_y_5', torch.tensor([
            [-1, -4, -6, -4, -1], [-2, -8, -12, -8, -2], [0, 0, 0, 0, 0],
            [2, 8, 12, 8, 2], [1, 4, 6, 4, 1]
        ]).float().view(1, 1, 5, 5) / 100.0)
        
        # 玻璃专用边缘核
        self.register_buffer('glass_edge_kernel', torch.tensor([
            [0, -1, 0], [-1, 5, -1], [0, -1, 0]
        ]).float().view(1, 1, 3, 3))
        
    def compute_multi_scale_edges(self, x):
        """计算多尺度边缘 - 匹配SCSA Enhanced的边缘检测"""
        # 3x3 Sobel
        grad_x_3 = F.conv2d(x, self.sobel_x_3, padding=1)
        grad_y_3 = F.conv2d(x, self.sobel_y_3, padding=1)
        edge_3 = torch.sqrt(grad_x_3**2 + grad_y_3**2 + 1e-8)
        
        # 5x5 Sobel
        grad_x_5 = F.conv2d(x, self.sobel_x_5, padding=2)
        grad_y_5 = F.conv2d(x, self.sobel_y_5, padding=2)
        edge_5 = torch.sqrt(grad_x_5**2 + grad_y_5**2 + 1e-8)
        
        # 玻璃专用边缘
        glass_edge = torch.abs(F.conv2d(x, self.glass_edge_kernel, padding=1))
        
        return edge_3, edge_5, glass_edge, (grad_x_3, grad_y_3), (grad_x_5, grad_y_5)
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: [B, 1, H, W] 预测结果
            target: [B, 1, H, W] 真实标签
        """
        # 多尺度边缘计算
        pred_edge_3, pred_edge_5, pred_glass, pred_grads_3, pred_grads_5 = self.compute_multi_scale_edges(pred)
        target_edge_3, target_edge_5, target_glass, target_grads_3, target_grads_5 = self.compute_multi_scale_edges(target)
        
        # 边缘MSE损失
        edge_loss_3 = F.mse_loss(pred_edge_3, target_edge_3)
        edge_loss_5 = F.mse_loss(pred_edge_5, target_edge_5)
        glass_edge_loss = F.mse_loss(pred_glass, target_glass)
        
        # 梯度一致性损失
        gradient_loss = (F.mse_loss(pred_grads_3[0], target_grads_3[0]) + 
                        F.mse_loss(pred_grads_3[1], target_grads_3[1]) +
                        F.mse_loss(pred_grads_5[0], target_grads_5[0]) + 
                        F.mse_loss(pred_grads_5[1], target_grads_5[1]))
        
        # 多尺度边缘融合损失
        multi_scale_loss = edge_loss_3 + edge_loss_5 + 2.0 * glass_edge_loss
        
        return (self.edge_weight * multi_scale_loss + 
                self.gradient_weight * gradient_loss)


class UltraHardSampleMining(nn.Module):
    """
    超级难样本挖掘 - 基于SCSA经验优化
    专门针对最后3% IoU的难样本
    """
    def __init__(self, hard_ratio=0.25, ultra_hard_ratio=0.1, neg_pos_ratio=3.0, 
                 focal_alpha=0.75, focal_gamma=2.5):
        super(UltraHardSampleMining, self).__init__()
        self.hard_ratio = hard_ratio
        self.ultra_hard_ratio = ultra_hard_ratio
        self.neg_pos_ratio = neg_pos_ratio
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """超级难样本挖掘"""
        B, C, H, W = pred.shape
        
        # 计算Focal Loss作为难度指标
        pt = torch.where(target == 1, pred, 1 - pred)
        focal_weight = self.focal_alpha * (1 - pt) ** self.focal_gamma
        pixel_loss = F.binary_cross_entropy(pred, target, reduction='none') * focal_weight
        pixel_loss = pixel_loss.view(B, -1)
        target_flat = target.view(B, -1)
        
        total_loss = 0
        for b in range(B):
            pos_mask = target_flat[b] > 0.5
            neg_mask = target_flat[b] <= 0.5
            
            pos_loss = pixel_loss[b][pos_mask]
            neg_loss = pixel_loss[b][neg_mask]
            
            if len(pos_loss) > 0:
                # 常规难样本
                num_hard_pos = max(1, int(len(pos_loss) * self.hard_ratio))
                hard_pos_loss, _ = torch.topk(pos_loss, num_hard_pos)
                
                # 超级难样本 - 最难的10%
                if len(pos_loss) > 10:
                    num_ultra_hard = max(1, int(len(pos_loss) * self.ultra_hard_ratio))
                    ultra_hard_loss, _ = torch.topk(pos_loss, num_ultra_hard)
                    pos_contribution = hard_pos_loss.mean() + 2.0 * ultra_hard_loss.mean()
                else:
                    pos_contribution = hard_pos_loss.mean()
            else:
                pos_contribution = torch.tensor(0.0, device=pred.device)
            
            # 负样本难样本挖掘
            if len(neg_loss) > 0:
                num_hard_neg = min(len(neg_loss), max(1, int(len(pos_loss) * self.neg_pos_ratio)))
                if num_hard_neg > 0:
                    hard_neg_loss, _ = torch.topk(neg_loss, num_hard_neg)
                    neg_contribution = hard_neg_loss.mean()
                else:
                    neg_contribution = torch.tensor(0.0, device=pred.device)
            else:
                neg_contribution = torch.tensor(0.0, device=pred.device)
            
            total_loss += pos_contribution + neg_contribution
        
        return total_loss / B


class SCSAMultiScaleLoss(nn.Module):
    """
    SCSA多尺度损失 - 针对Enhanced版本的多层预测
    """
    def __init__(self, pred_weights=[1.0, 0.8, 0.6], edge_weights=[0.8, 0.6, 0.4]):
        super(SCSAMultiScaleLoss, self).__init__()
        self.pred_weights = pred_weights
        self.edge_weights = edge_weights
        self.bce_loss = nn.BCELoss()
        
    def forward(self, outputs: Dict, target: torch.Tensor) -> torch.Tensor:
        """
        针对SCSA Enhanced的多尺度预测
        """
        total_loss = 0
        
        # 主要预测损失
        main_preds = ['pred_h', 'pred_l', 'pred_edge']
        for i, pred_name in enumerate(main_preds):
            if pred_name in outputs and i < len(self.pred_weights):
                pred_loss = self.bce_loss(outputs[pred_name], target)
                total_loss += self.pred_weights[i] * pred_loss
        
        # 边缘相关预测
        edge_preds = ['edge_map', 'edge_magnitude']
        for i, edge_name in enumerate(edge_preds):
            if edge_name in outputs and i < len(self.edge_weights):
                # 计算目标边缘
                target_edge = self._compute_edge(target)
                edge_loss = F.mse_loss(outputs[edge_name], target_edge)
                total_loss += self.edge_weights[i] * edge_loss
        
        return total_loss
    
    def _compute_edge(self, x: torch.Tensor) -> torch.Tensor:
        """计算边缘图"""
        kernel = torch.tensor([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]], 
                             device=x.device, dtype=x.dtype).view(1, 1, 3, 3)
        edge = F.conv2d(x, kernel, padding=1)
        return torch.sigmoid(edge)


class AdaptiveCRFLoss(nn.Module):
    """
    自适应CRF损失 - 配合7次迭代CRF优化
    """
    def __init__(self, crf_weight=1.5, consistency_weight=1.0):
        super(AdaptiveCRFLoss, self).__init__()
        self.crf_weight = crf_weight
        self.consistency_weight = consistency_weight
        
    def forward(self, outputs: Dict, target: torch.Tensor) -> torch.Tensor:
        """
        CRF相关损失
        """
        total_loss = 0
        
        # CRF输出与目标的BCE损失
        if 'refined_pred' in outputs:
            refined_pred = outputs['refined_pred']
            if refined_pred.size(1) == 2:  # [B, 2, H, W] format
                refined_prob = refined_pred[:, 1:2]  # 取前景通道
            else:
                refined_prob = refined_pred
                
            crf_loss = F.binary_cross_entropy(refined_prob, target)
            total_loss += self.crf_weight * crf_loss
        
        # 集成预测一致性
        if 'ensemble_pred' in outputs and 'refined_pred' in outputs:
            ensemble_pred = outputs['ensemble_pred']
            refined_prob = outputs['refined_pred'][:, 1:2] if outputs['refined_pred'].size(1) == 2 else outputs['refined_pred']
            
            consistency_loss = F.mse_loss(ensemble_pred, refined_prob)
            total_loss += self.consistency_weight * consistency_loss
        
        return total_loss


class EnhancedDiceLoss(nn.Module):
    """
    增强Dice损失 - 针对玻璃检测优化
    """
    def __init__(self, smooth=1.0, edge_focus=True, edge_weight=2.0):
        super(EnhancedDiceLoss, self).__init__()
        self.smooth = smooth
        self.edge_focus = edge_focus
        self.edge_weight = edge_weight
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算增强Dice损失
        """
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)
        
        # 基础Dice
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum()
        dice = (2 * intersection + self.smooth) / (union + self.smooth)
        dice_loss = 1 - dice
        
        # 边缘聚焦Dice
        if self.edge_focus:
            # 计算边缘mask
            edge_kernel = torch.tensor([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]], 
                                     device=target.device, dtype=target.dtype).view(1, 1, 3, 3)
            target_edge = F.conv2d(target, edge_kernel, padding=1)
            edge_mask = (torch.abs(target_edge) > 0.1).float()
            
            # 在边缘区域计算Dice
            pred_edge_flat = (pred * edge_mask).view(-1)
            target_edge_flat = (target * edge_mask).view(-1)
            
            edge_intersection = (pred_edge_flat * target_edge_flat).sum()
            edge_union = pred_edge_flat.sum() + target_edge_flat.sum()
            
            if edge_union > 0:
                edge_dice = (2 * edge_intersection + self.smooth) / (edge_union + self.smooth)
                edge_dice_loss = 1 - edge_dice
                dice_loss = dice_loss + self.edge_weight * edge_dice_loss
        
        return dice_loss


class SCSAEnhanced90Loss(nn.Module):
    """
    SCSA Enhanced 90% IoU损失函数
    基于87.95%成功经验的增强版本
    """
    def __init__(self, 
                 bce_weight: float = 1.0,
                 dice_weight: float = 2.0,
                 edge_weight: float = 4.0,
                 hard_mining_weight: float = 3.0,
                 multiscale_weight: float = 1.5,
                 crf_weight: float = 1.5,
                 adaptive_weights: bool = True):
        super(SCSAEnhanced90Loss, self).__init__()
        
        # 基础损失
        self.bce_loss = nn.BCELoss()
        self.dice_loss = EnhancedDiceLoss(smooth=1.0, edge_focus=True)
        
        # SCSA Enhanced专用损失
        self.edge_loss = SuperEdgeAwareLoss()
        self.hard_mining_loss = UltraHardSampleMining()
        self.multiscale_loss = SCSAMultiScaleLoss()
        self.crf_loss = AdaptiveCRFLoss()
        
        # 权重管理
        if adaptive_weights:
            self.bce_weight = nn.Parameter(torch.tensor(bce_weight), requires_grad=True)
            self.dice_weight = nn.Parameter(torch.tensor(dice_weight), requires_grad=True)
            self.edge_weight = nn.Parameter(torch.tensor(edge_weight), requires_grad=True)
            self.hard_mining_weight = nn.Parameter(torch.tensor(hard_mining_weight), requires_grad=True)
            self.multiscale_weight = nn.Parameter(torch.tensor(multiscale_weight), requires_grad=True)
            self.crf_weight = nn.Parameter(torch.tensor(crf_weight), requires_grad=True)
        else:
            self.bce_weight = bce_weight
            self.dice_weight = dice_weight
            self.edge_weight = edge_weight
            self.hard_mining_weight = hard_mining_weight
            self.multiscale_weight = multiscale_weight
            self.crf_weight = crf_weight
            
        self.adaptive_weights = adaptive_weights
        
        # 损失历史记录
        self.loss_history = {}
        
    def forward(self, outputs: Dict, targets: torch.Tensor) -> Tuple[torch.Tensor, Dict]:
        """
        计算SCSA Enhanced总损失
        """
        # 获取主预测
        if 'refined_pred' in outputs:
            main_pred = outputs['refined_pred']
            if main_pred.size(1) == 2:
                main_pred = main_pred[:, 1:2]  # 取前景通道
        elif 'ensemble_pred' in outputs:
            main_pred = outputs['ensemble_pred']
        elif 'final_pred' in outputs:
            main_pred = outputs['final_pred']
        else:
            main_pred = outputs['pred_h']
        
        # 计算各组件损失
        losses = {}
        
        # 基础损失
        losses['bce'] = self.bce_loss(main_pred, targets)
        losses['dice'] = self.dice_loss(main_pred, targets)
        
        # SCSA Enhanced专用损失
        losses['edge'] = self.edge_loss(main_pred, targets)
        losses['hard_mining'] = self.hard_mining_loss(main_pred, targets)
        losses['multiscale'] = self.multiscale_loss(outputs, targets)
        losses['crf'] = self.crf_loss(outputs, targets)
        
        # 应用权重
        if self.adaptive_weights:
            weighted_losses = {
                'bce': torch.abs(self.bce_weight) * losses['bce'],
                'dice': torch.abs(self.dice_weight) * losses['dice'],
                'edge': torch.abs(self.edge_weight) * losses['edge'],
                'hard_mining': torch.abs(self.hard_mining_weight) * losses['hard_mining'],
                'multiscale': torch.abs(self.multiscale_weight) * losses['multiscale'],
                'crf': torch.abs(self.crf_weight) * losses['crf']
            }
        else:
            weighted_losses = {
                'bce': self.bce_weight * losses['bce'],
                'dice': self.dice_weight * losses['dice'],
                'edge': self.edge_weight * losses['edge'],
                'hard_mining': self.hard_mining_weight * losses['hard_mining'],
                'multiscale': self.multiscale_weight * losses['multiscale'],
                'crf': self.crf_weight * losses['crf']
            }
        
        # 总损失
        total_loss = sum(weighted_losses.values())
        
        # 数值稳定性检查
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("警告: 损失出现NaN/Inf，使用安全值")
            total_loss = torch.tensor(1.0, device=targets.device, requires_grad=True)
        
        # 记录损失历史
        for name, loss in losses.items():
            if name not in self.loss_history:
                self.loss_history[name] = []
            self.loss_history[name].append(loss.item())
            
            # 保持最近50个值
            if len(self.loss_history[name]) > 50:
                self.loss_history[name] = self.loss_history[name][-50:]
        
        # 返回详细损失信息
        loss_dict = {**losses, **weighted_losses, 'total': total_loss}
        
        return total_loss, loss_dict
    
    def get_loss_weights(self) -> Dict:
        """获取当前损失权重"""
        if self.adaptive_weights:
            return {
                'bce_weight': torch.abs(self.bce_weight).item(),
                'dice_weight': torch.abs(self.dice_weight).item(),
                'edge_weight': torch.abs(self.edge_weight).item(),
                'hard_mining_weight': torch.abs(self.hard_mining_weight).item(),
                'multiscale_weight': torch.abs(self.multiscale_weight).item(),
                'crf_weight': torch.abs(self.crf_weight).item()
            }
        else:
            return {
                'bce_weight': self.bce_weight,
                'dice_weight': self.dice_weight,
                'edge_weight': self.edge_weight,
                'hard_mining_weight': self.hard_mining_weight,
                'multiscale_weight': self.multiscale_weight,
                'crf_weight': self.crf_weight
            }
    
    def get_loss_trends(self) -> Dict:
        """获取损失趋势"""
        trends = {}
        for name, history in self.loss_history.items():
            if len(history) >= 10:
                recent_avg = np.mean(history[-10:])
                overall_avg = np.mean(history)
                trend = "increasing" if recent_avg > overall_avg * 1.1 else "decreasing" if recent_avg < overall_avg * 0.9 else "stable"
                trends[name] = {
                    'trend': trend,
                    'recent_avg': recent_avg,
                    'overall_avg': overall_avg
                }
        return trends


def create_scsa_enhanced_90_loss(adaptive_weights=True):
    """创建SCSA Enhanced 90% IoU损失函数"""
    return SCSAEnhanced90Loss(
        bce_weight=1.0,
        dice_weight=2.0,
        edge_weight=4.0,
        hard_mining_weight=3.0,
        multiscale_weight=1.5,
        crf_weight=1.5,
        adaptive_weights=adaptive_weights
    )


if __name__ == "__main__":
    # 测试损失函数
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    criterion = create_scsa_enhanced_90_loss(adaptive_weights=True).to(device)
    
    # 模拟SCSA Enhanced的输出
    batch_size = 4
    outputs = {
        'pred_h': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'pred_l': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'pred_edge': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'ensemble_pred': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'refined_pred': torch.softmax(torch.randn(batch_size, 2, 416, 416), dim=1).to(device),
        'edge_map': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'edge_magnitude': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device)
    }
    targets = torch.randint(0, 2, (batch_size, 1, 416, 416)).float().to(device)
    
    # 计算损失
    total_loss, loss_dict = criterion(outputs, targets)
    
    print("SCSA Enhanced 90% IoU损失函数测试:")
    print(f"总损失: {total_loss.item():.4f}")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.item():.4f}")
    
    print("\n当前损失权重:")
    weights = criterion.get_loss_weights()
    for key, value in weights.items():
        print(f"  {key}: {value:.4f}")
    
    print("\n✅ SCSA Enhanced 90% IoU损失函数测试成功!")