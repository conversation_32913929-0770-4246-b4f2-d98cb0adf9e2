from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

import torch
import torch.nn as nn
import torch.nn.functional as F

import os
os.environ['CUDA_LAUNCH_BLOCKING'] = "1"

def _iou(pred, target, size_average = True):
    # Ensure pred and target are in valid range
    pred = torch.clamp(pred, min=0.0, max=1.0)
    target = torch.clamp(target, min=0.0, max=1.0)

    # Check for NaN or Inf values
    if torch.isnan(pred).any() or torch.isinf(pred).any():
        print("Warning: NaN or Inf values detected in predictions in IOU loss")
        pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)

    if torch.isnan(target).any() or torch.isinf(target).any():
        print("Warning: NaN or Inf values detected in targets in IOU loss")
        target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)

    b = pred.shape[0]
    IoU = 0.0
    for i in range(0,b):
        #compute the IoU of the foreground
        Iand1 = torch.sum(target[i,:,:,:]*pred[i,:,:,:])
        Ior1 = torch.sum(target[i,:,:,:]) + torch.sum(pred[i,:,:,:])-Iand1

        # Avoid division by zero
        if Ior1 < 1e-7:
            IoU1 = torch.tensor(1.0, device=pred.device)  # If both pred and target are empty, IoU=1
        else:
            IoU1 = Iand1/Ior1

        #IoU loss is (1-IoU1)
        IoU = IoU + (1-IoU1)

    return IoU/b

class IOU(torch.nn.Module):
    def __init__(self, size_average = True):
        super(IOU, self).__init__()
        self.size_average = size_average

    def forward(self, pred, target):

        return _iou(pred, target, self.size_average)

# Focal Loss
class FocalLoss(nn.Module):
    def __init__(self, alpha=0.25, gamma=2, balance_weights=None):
        """
        Focal Loss for binary segmentation

        Args:
            alpha: Weighting factor for the rare class (glass)
            gamma: Focusing parameter that reduces the loss contribution from easy examples
            balance_weights: Optional tensor of shape [B, 1, H, W] with pixel-wise weights
        """
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.balance_weights = balance_weights

    def forward(self, pred, target):
        """
        Args:
            pred: Predicted masks, shape [B, 1, H, W]
            target: Ground truth masks, shape [B, 1, H, W]
        """
        # Ensure pred is in [0,1] range
        pred = torch.clamp(pred, min=1e-7, max=1.0 - 1e-7)

        # Ensure target is in [0,1] range
        target = torch.clamp(target, min=0.0, max=1.0)

        # Check for NaN or Inf values
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            print("Warning: NaN or Inf values detected in predictions")
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)

        if torch.isnan(target).any() or torch.isinf(target).any():
            print("Warning: NaN or Inf values detected in targets")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)

        # Compute standard BCE loss
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')

        # Compute probabilities and focusing weight
        pt = torch.where(target > 0.5, pred, 1 - pred)  # Probability of the correct class
        focal_weight = (1 - pt) ** self.gamma

        # Apply alpha weighting based on the class
        alpha_weight = torch.where(target > 0.5, self.alpha, 1 - self.alpha)

        # Combine weights
        loss = alpha_weight * focal_weight * bce_loss

        # Apply optional balance weights if provided
        if self.balance_weights is not None:
            loss = loss * self.balance_weights

        return loss.mean()

# class IOU:
#     def __init__(self):
#         pass

#     def compute_iou(self, predict_mask, gt_mask):
#         """
#         计算预测掩码和真实掩码之间的 IoU（交并比）。

#         参数:
#         predict_mask (torch.Tensor): 预测的掩码张量。
#         gt_mask (torch.Tensor): 真实的掩码张量。

#         返回:
#         torch.Tensor: 计算得到的 IoU 值。
#         """
#         # 检查两个张量的形状是否一致
#         assert predict_mask.shape == gt_mask.shape, "预测掩码和真实掩码的形状必须一致"

#         # 处理预测掩码或真实掩码全为 0 的情况
#         if torch.sum(predict_mask) == 0 or torch.sum(gt_mask) == 0:
#             return torch.tensor(0.0, device=predict_mask.device)

#         # 计算交集
#         intersection = torch.sum(torch.logical_and(predict_mask, gt_mask))
#         # 计算真实掩码的元素总和
#         gt_sum = torch.sum(gt_mask)
#         # 计算预测掩码的元素总和
#         predict_sum = torch.sum(predict_mask)
#         # 计算并集
#         union = gt_sum + predict_sum - intersection

#         # 计算 IoU
#         iou = intersection / union

#         return iou
#     def compute_iou_loss(self, predict_mask, gt_mask):
#         iou = self.compute_iou(predict_mask, gt_mask)
#         iou_loss = 1-iou
#         return iou_loss

#     def __call__(self, predict_mask, gt_mask):
#         return self.compute_iou_loss(predict_mask, gt_mask)

class EdgeSaliencyLoss(nn.Module):
    def __init__(self, device, alpha_sal=0.7):
        super(EdgeSaliencyLoss, self).__init__()

        self.alpha_sal = alpha_sal

        self.laplacian_kernel = torch.tensor([[-1., -1., -1.], [-1., 8., -1.], [-1., -1., -1.]], dtype=torch.float, requires_grad=False)
        self.laplacian_kernel = self.laplacian_kernel.view((1, 1, 3, 3))  # Shape format of weight for convolution
        self.laplacian_kernel = self.laplacian_kernel.to(device)

    @staticmethod
    def weighted_bce(input_, target, weight_0=1.0, weight_1=1.0, eps=1e-15):
        wbce_loss = -weight_1 * target * torch.log(input_ + eps) - weight_0 * (1 - target) * torch.log(
            1 - input_ + eps)
        return torch.mean(wbce_loss)

    def forward(self, y_pred, y_gt):
        # Ensure inputs are in valid range
        y_pred = torch.clamp(y_pred, min=1e-7, max=1.0 - 1e-7)
        y_gt = torch.clamp(y_gt, min=0.0, max=1.0)

        # Check for NaN or Inf values
        if torch.isnan(y_pred).any() or torch.isinf(y_pred).any():
            print("Warning: NaN or Inf values detected in predictions in EdgeSaliencyLoss")
            y_pred = torch.nan_to_num(y_pred, nan=0.5, posinf=1.0, neginf=0.0)

        if torch.isnan(y_gt).any() or torch.isinf(y_gt).any():
            print("Warning: NaN or Inf values detected in targets in EdgeSaliencyLoss")
            y_gt = torch.nan_to_num(y_gt, nan=0.5, posinf=1.0, neginf=0.0)

        # Generate edge maps
        y_gt_edges = F.relu(torch.tanh(F.conv2d(y_gt, self.laplacian_kernel, padding=(1, 1))))
        y_pred_edges = F.relu(torch.tanh(F.conv2d(y_pred, self.laplacian_kernel, padding=(1, 1))))

        # 处理 nan 值
        y_gt_edges = torch.nan_to_num(y_gt_edges, nan=0.0)
        y_pred_edges = torch.nan_to_num(y_pred_edges, nan=0.0)

        # Ensure edge maps are in valid range for BCE
        y_pred_edges = torch.clamp(y_pred_edges, min=1e-7, max=1.0 - 1e-7)
        y_gt_edges = torch.clamp(y_gt_edges, min=0.0, max=1.0)

        # Compute losses
        sal_loss = self.weighted_bce(input_=y_pred, target=y_gt, weight_0=1.0, weight_1=1.12)
        edge_loss = F.binary_cross_entropy(input=y_pred_edges, target=y_gt_edges)

        total_loss = self.alpha_sal * sal_loss + (1 - self.alpha_sal) * edge_loss
        return total_loss

class BCE(nn.Module):
    def __init__(self, weight_0=1.0, weight_1=1.0, eps=1e-15):
        super(BCE, self).__init__()
        self.weight_0 = weight_0
        self.weight_1 = weight_1
        self.eps = eps

    def forward(self, input_, target):
        # Ensure inputs are in valid range
        input_ = torch.clamp(input_, min=self.eps, max=1.0 - self.eps)
        target = torch.clamp(target, min=0.0, max=1.0)

        # Check for NaN or Inf values
        if torch.isnan(input_).any() or torch.isinf(input_).any():
            print("Warning: NaN or Inf values detected in predictions in BCE loss")
            input_ = torch.nan_to_num(input_, nan=0.5, posinf=1.0, neginf=0.0)

        if torch.isnan(target).any() or torch.isinf(target).any():
            print("Warning: NaN or Inf values detected in targets in BCE loss")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)

        wbce_loss = -self.weight_1 * target * torch.log(input_ + self.eps) - self.weight_0 * (1 - target) * torch.log(
            1 - input_ + self.eps)
        return torch.mean(wbce_loss)





if __name__ == '__main__':
    if torch.cuda.is_available():
        device = torch.device(device='cuda')
    else:
        device = torch.device(device='cpu')

    dummy_input = torch.autograd.Variable(torch.sigmoid(torch.randn(2, 1, 8, 16)), requires_grad=True).to(device)
    dummy_gt = torch.autograd.Variable(torch.ones_like(dummy_input)).to(device)
    print('Input Size :', dummy_input.size())

    criteria = EdgeSaliencyLoss(device=device)
    loss = criteria(dummy_input, dummy_gt)
    print('Loss Value :', loss)