"""
 @Time    : 2024
 <AUTHOR> <PERSON><PERSON>

 @Project : IG_SLAM
 @File    : diff_crf_fixed.py
 @Function: 修复了NaN问题的Differentiable CRF implementation

"""
import torch
import torch.nn as nn
import torch.nn.functional as F

class SimplifiedDiffCRF(nn.Module):
    """
    修复了NaN问题的简化可微分CRF层
    """
    def __init__(self, n_iter=5, bilateral_weight=5.0, gaussian_weight=3.0,
                 bilateral_spatial_sigma=49.0, bilateral_color_sigma=5.0,
                 gaussian_sigma=3.0, trainable=True):
        """
        Initialize the simplified differentiable CRF layer

        Args:
            n_iter: Number of mean-field iterations
            bilateral_weight: Weight for the bilateral filter
            gaussian_weight: Weight for the Gaussian filter
            bilateral_spatial_sigma: Spatial sigma for the bilateral filter
            bilateral_color_sigma: Color sigma for the bilateral filter
            gaussian_sigma: Sigma for the Gaussian filter
            trainable: Whether to make the parameters trainable
        """
        super(SimplifiedDiffCRF, self).__init__()

        self.n_iter = n_iter

        # Initialize parameters
        if trainable:
            self.bilateral_weight = nn.Parameter(torch.tensor(bilateral_weight))
            self.gaussian_weight = nn.Parameter(torch.tensor(gaussian_weight))
            self.bilateral_spatial_sigma = nn.Parameter(torch.tensor(bilateral_spatial_sigma))
            self.bilateral_color_sigma = nn.Parameter(torch.tensor(bilateral_color_sigma))
            self.gaussian_sigma = nn.Parameter(torch.tensor(gaussian_sigma))
        else:
            self.register_buffer('bilateral_weight', torch.tensor(bilateral_weight))
            self.register_buffer('gaussian_weight', torch.tensor(gaussian_weight))
            self.register_buffer('bilateral_spatial_sigma', torch.tensor(bilateral_spatial_sigma))
            self.register_buffer('bilateral_color_sigma', torch.tensor(bilateral_color_sigma))
            self.register_buffer('gaussian_sigma', torch.tensor(gaussian_sigma))

    def _gaussian_filter(self, x, sigma):
        """
        Apply Gaussian filter with NaN protection

        Args:
            x: Input tensor (B, C, H, W)
            sigma: Standard deviation of the Gaussian filter

        Returns:
            filtered: Filtered tensor
        """
        try:
            # 数值稳定性检查 - 修复NaN转换为int的问题
            sigma_val = sigma.item()
            if torch.isnan(torch.tensor(sigma_val)) or torch.isinf(torch.tensor(sigma_val)):
                print(f"警告: sigma值异常 ({sigma_val})，使用默认值1.0")
                sigma_val = 1.0
            
            # 限制sigma值在合理范围内
            sigma_val = max(0.1, min(sigma_val, 10.0))  # 限制在0.1到10之间
            
            # Determine kernel size based on sigma (2*sigma + 1 is a good rule of thumb)
            kernel_size = int(2 * sigma_val + 1)
            if kernel_size % 2 == 0:
                kernel_size += 1  # Ensure odd kernel size
            
            # 限制kernel_size在合理范围内
            kernel_size = max(3, min(kernel_size, 15))  # 限制在3到15之间

            # Create Gaussian kernel
            padding = kernel_size // 2

            # Create a 1D Gaussian kernel - 使用修正的sigma值
            grid = torch.arange(-padding, padding + 1, dtype=torch.float32, device=x.device)
            sigma_tensor = torch.tensor(sigma_val, device=x.device, dtype=torch.float32)
            gaussian_1d = torch.exp(-0.5 * (grid / sigma_tensor).pow(2))
            
            # 检查gaussian_1d的数值稳定性
            if torch.isnan(gaussian_1d).any() or torch.isinf(gaussian_1d).any():
                print("警告: 1D高斯核包含NaN或Inf值，使用默认核")
                gaussian_1d = torch.ones_like(grid)
            
            gaussian_1d_sum = gaussian_1d.sum()
            if gaussian_1d_sum == 0 or torch.isnan(gaussian_1d_sum) or torch.isinf(gaussian_1d_sum):
                gaussian_1d = torch.ones_like(grid)
                gaussian_1d_sum = gaussian_1d.sum()
            
            gaussian_1d = gaussian_1d / gaussian_1d_sum

            # Create 2D Gaussian kernel
            gaussian_2d = gaussian_1d.view(1, 1, -1, 1) * gaussian_1d.view(1, 1, 1, -1)
            gaussian_2d = gaussian_2d.expand(x.size(1), 1, kernel_size, kernel_size)
            
            # 检查2D高斯核的数值稳定性
            if torch.isnan(gaussian_2d).any() or torch.isinf(gaussian_2d).any():
                print("警告: 2D高斯核包含NaN或Inf值，使用单位核")
                gaussian_2d = torch.zeros_like(gaussian_2d)
                center = kernel_size // 2
                gaussian_2d[:, :, center, center] = 1.0

            # Apply Gaussian filter
            x_padded = F.pad(x, (padding, padding, padding, padding), mode='reflect')
            result = F.conv2d(x_padded, gaussian_2d.to(x.device), groups=x.size(1))
            
            # 最终检查结果
            if torch.isnan(result).any() or torch.isinf(result).any():
                print("警告: 高斯滤波结果包含NaN或Inf值，返回原始输入")
                return x
            
            return result
            
        except Exception as e:
            print(f"高斯滤波出错: {e}，返回原始输入")
            return x

    def _bilateral_filter(self, x, guide, spatial_sigma, color_sigma):
        """
        Simplified bilateral filter - just use Gaussian filter to avoid NaN issues

        Args:
            x: Input tensor (B, C, H, W)
            guide: Guidance image (B, 3, H, W)
            spatial_sigma: Spatial sigma for the bilateral filter
            color_sigma: Color sigma for the bilateral filter (unused in simplified version)

        Returns:
            filtered: Filtered tensor
        """
        # 简化为纯高斯滤波，避免颜色权重计算中的数值问题
        return self._gaussian_filter(x, spatial_sigma)

    def forward(self, logits, img):
        """
        Forward pass of the simplified CRF layer with comprehensive NaN protection

        Args:
            logits: Logits from the network (B, C, H, W)
            img: Input image (B, 3, H, W)

        Returns:
            refined: Refined segmentation after CRF (B, C, H, W)
        """
        try:
            # 数值稳定性检查 - 限制logits范围
            logits = torch.clamp(logits, min=-10.0, max=10.0)
            
            # 检查并修复NaN/Inf值
            if torch.isnan(logits).any() or torch.isinf(logits).any():
                print("警告: CRF输入logits包含NaN或Inf值，已修复")
                logits = torch.nan_to_num(logits, nan=0.0, posinf=10.0, neginf=-10.0)
            
            # 检查并修复图像NaN/Inf值
            if torch.isnan(img).any() or torch.isinf(img).any():
                print("警告: CRF输入图像包含NaN或Inf值，已修复")
                img = torch.nan_to_num(img, nan=0.5, posinf=1.0, neginf=0.0)
                img = torch.clamp(img, min=0.0, max=1.0)

            # 确保图像在[0,1]范围内
            img = torch.clamp(img, min=0.0, max=1.0)

            # Initialize with softmax of logits - 数值稳定的softmax
            Q = F.softmax(logits, dim=1)
            
            # 检查初始Q的数值稳定性
            if torch.isnan(Q).any() or torch.isinf(Q).any():
                print("警告: 初始Q包含NaN或Inf值，重新初始化")
                # 重新初始化为均匀分布
                Q = torch.ones_like(logits) / logits.size(1)

            # Mean-field iterations
            for iteration in range(self.n_iter):
                try:
                    # Apply Gaussian filter (appearance kernel)
                    gaussian_term = self._gaussian_filter(Q, self.gaussian_sigma)
                    
                    # 检查gaussian_term的数值稳定性
                    if torch.isnan(gaussian_term).any() or torch.isinf(gaussian_term).any():
                        print(f"警告: 第{iteration}轮高斯滤波结果包含NaN或Inf值")
                        gaussian_term = torch.nan_to_num(gaussian_term, nan=0.0, posinf=1.0, neginf=0.0)

                    # Apply bilateral filter (smoothness kernel)
                    bilateral_term = self._bilateral_filter(Q, img, self.bilateral_spatial_sigma, self.bilateral_color_sigma)
                    
                    # 检查bilateral_term的数值稳定性
                    if torch.isnan(bilateral_term).any() or torch.isinf(bilateral_term).any():
                        print(f"警告: 第{iteration}轮双边滤波结果包含NaN或Inf值")
                        bilateral_term = torch.nan_to_num(bilateral_term, nan=0.0, posinf=1.0, neginf=0.0)

                    # Combine filtered results
                    message = self.gaussian_weight * gaussian_term + self.bilateral_weight * bilateral_term
                    
                    # 检查message的数值稳定性
                    if torch.isnan(message).any() or torch.isinf(message).any():
                        print(f"警告: 第{iteration}轮消息传递结果包含NaN或Inf值")
                        message = torch.nan_to_num(message, nan=0.0, posinf=1.0, neginf=0.0)

                    # Update Q - 数值稳定的softmax
                    updated_logits = logits + message
                    updated_logits = torch.clamp(updated_logits, min=-10.0, max=10.0)
                    Q = F.softmax(updated_logits, dim=1)
                    
                    # 检查更新后Q的数值稳定性
                    if torch.isnan(Q).any() or torch.isinf(Q).any():
                        print(f"警告: 第{iteration}轮更新后Q包含NaN或Inf值，跳出循环")
                        Q = torch.nan_to_num(Q, nan=0.0, posinf=1.0, neginf=0.0)
                        # 重新归一化
                        Q = Q / Q.sum(dim=1, keepdim=True).clamp(min=1e-7)
                        break
                        
                except Exception as e:
                    print(f"CRF第{iteration}轮迭代出错: {e}")
                    break

            # For binary segmentation, return the probability of foreground and background
            if Q.size(1) == 2:
                # 确保概率和为1
                Q = Q / Q.sum(dim=1, keepdim=True).clamp(min=1e-7)
                return Q  # Return both channels (bg, fg)
            else:
                # If not 2 channels, create a 2-channel output (bg, fg)
                # Use the first channel as foreground if available, otherwise use a default value
                if Q.size(1) >= 1:
                    fg_prob = Q[:, 0:1, :, :]
                    bg_prob = 1.0 - fg_prob
                else:
                    # Create default probabilities
                    fg_prob = torch.ones_like(logits[:, 0:1, :, :]) * 0.5
                    bg_prob = torch.ones_like(logits[:, 0:1, :, :]) * 0.5

                # 确保概率在有效范围内
                fg_prob = torch.clamp(fg_prob, min=1e-7, max=1.0-1e-7)
                bg_prob = torch.clamp(bg_prob, min=1e-7, max=1.0-1e-7)
                
                # Ensure probabilities sum to 1
                total = fg_prob + bg_prob
                total = total.clamp(min=1e-7)  # 避免除零
                fg_prob = fg_prob / total
                bg_prob = bg_prob / total

                return torch.cat([bg_prob, fg_prob], dim=1)  # Return (bg, fg)
        
        except Exception as e:
            print(f"CRF整体出错: {e}，返回默认预测")
            # 返回默认的2通道预测
            batch_size, _, height, width = logits.shape
            default_pred = torch.ones(batch_size, 2, height, width, device=logits.device) * 0.5
            return default_pred 