import os
import time
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import check_mkdir, crf_refine_use,compute_iou, compute_fmeasure,compute_ber,compute_acc,compute_mae,compute_precision_recall
from ig_glass.gdnet import GDNet
#from config import gdd_testing_root, gdd_results_root, backbone_path

# device set
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# parameter set
ckpt_path = '/home/<USER>/ws/IGSLAM/ig_glass/ckpt'
exp_name = 'IG-SLAM'
args = {
    'snapshot': 're90',
    'scale': 416,
    'crf': True,
    'glass_threshold': 0.5,  # 玻璃区域识别阈值
}

# 预处理
img_transform = transforms.Compose([
    transforms.Resize((args['scale'], args['scale'])),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

to_pil = transforms.ToPILImage()

# 动态亮度调整目标范围
BRIGHTNESS_TARGET_MIN = 100
BRIGHTNESS_TARGET_MAX = 120


def calculate_brightness(image_path):
    """计算图像亮度"""
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])
    return brightness


def calculate_feature_points(image_path):
    """计算图像特征点"""
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    orb = cv2.ORB_create(nfeatures=10000)
    keypoints = orb.detect(image, None)
    return keypoints, len(keypoints)


def adjust_image_brightness(image, adjustment):
    """根据调整值调整图像亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    v = np.clip(v.astype(np.int32) + int(adjustment), 0, 255).astype(np.uint8)
    hsv = cv2.merge((h, s, v))
    return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)


def adjust_image_brightness_dynamically(image, target_min, target_max):
    """根据图像亮度的分布动态调整亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    v = hsv[:, :, 2]  # 提取亮度通道

    # 计算亮度的直方图
    hist = cv2.calcHist([v], [0], None, [256], [0, 256])
    cumulative_sum = np.cumsum(hist)
    median_brightness = np.searchsorted(cumulative_sum, cumulative_sum[-1] // 2)

    # 动态目标亮度，基于图像的亮度分布
    current_mean = np.mean(v)
    current_std = np.std(v)

    # 根据当前亮度的均值和标准差，动态调整目标亮度
    if current_mean < target_min:
        adjustment = target_min - median_brightness
    elif current_mean > target_max:
        adjustment = target_max - median_brightness
    else:
        adjustment = (target_max + target_min) / 2 - median_brightness

    # 调整图像亮度
    adjusted_image = adjust_image_brightness(image, adjustment)

    return adjusted_image


def detect_glass_and_adjust_brightness(image_folder, output_folder, net, glass_threshold):
    """检测玻璃区域并调整亮度"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    brightness_list = []
    feature_points_list = []

    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)
                # Step 2: 读取原始图像
        original_image = cv2.imread(image_path)
        adjusted_image = adjust_image_brightness_dynamically(original_image, BRIGHTNESS_TARGET_MIN, BRIGHTNESS_TARGET_MAX)
        temp_path = os.path.join(output_folder, 'temp.png')
        cv2.imwrite(temp_path, adjusted_image)
        # Step 1: Identify glass region and boundaries
        img_pil = Image.open(temp_path)
        if img_pil.mode != 'RGB':
            img_pil = img_pil.convert('RGB')
        w, h = img_pil.size
        img_var = Variable(img_transform(img_pil).unsqueeze(0)).cuda(device_ids[0])
        f1, f2, f3 = net(img_var)
        f3 = f3.data.squeeze(0).cpu()
        f3_resized = np.array(transforms.Resize((h, w))(to_pil(f3)))

        # 使用 glass_threshold 来调整玻璃区域识别的精度
        #glass_mask = (f3_resized > glass_threshold).astype(np.uint8) if f3_resized.max() > glass_threshold else np.zeros((h, w), dtype=np.uint8)

        # Optional: Apply CRF if activated
        if args['crf']:
            #original_image = cv2.imread(image_path)
            adjusted_image  = cv2.imread(temp_path)
            f3_resized = crf_refine_use(adjusted_image, f3_resized)
        
        glass_mask = (f3_resized > glass_threshold).astype(np.uint8) if f3_resized.max() > glass_threshold else np.zeros((h, w), dtype=np.uint8)
        no_glass = np.zeros((h, w), dtype=np.uint8)
            
        #将glass——mask直接图形化
        image_mask = glass_mask * 255
        # #如果玻璃领域超过一定比例，则该部分没有mask
        # if np.sum(glass_mask) / (h * w) > 0.7:
        #     image_mask = no_glass
        # else:
        #     image_mask = glass_mask * 255


        # # 计算玻璃区域的边缘轮廓
        # contours, _ = cv2.findContours(glass_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # # 绘制玻璃区域边框
        # image_with_contours = adjusted_image.copy()
        # cv2.drawContours(image_with_contours, contours, -1, (0, 0, 255), thickness=3)

        # # Step 3: 计算特征点并清除玻璃区域内的特征点
        # #keypoints, feature_points = calculate_feature_points(temp_path)
        # #keypoints_outside_glass = [kp for kp in keypoints if not glass_mask[int(kp.pt[1]), int(kp.pt[0])]]
        # keypoints_outside_glass = []

        # # 在图像上绘制保留下来的特征点
        # image_with_keypoints = cv2.drawKeypoints(image_with_contours, keypoints_outside_glass, None, color=(0, 255, 0))

        # # 保存结果
        # output_path = os.path.join(output_folder, image_file)
        glass_mask_path = os.path.join(output_folder, image_file)
        #cv2.imwrite(output_path, image_with_keypoints)
        cv2.imwrite(glass_mask_path, image_mask)

    return brightness_list, feature_points_list

