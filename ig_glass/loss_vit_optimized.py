"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : loss_vit_optimized.py
 @Function: 优化ViT玻璃检测的损失函数

专门针对ViT特点设计的损失函数：
1. 多尺度感知损失 - 适应ViT的patch-based特性
2. 边缘对比损失 - 增强ViT对边缘的感知
3. 特征一致性损失 - 确保ViT-CNN融合的稳定性
4. 自适应权重调节 - 动态平衡不同损失分量
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class EdgeAwareLoss(nn.Module):
    """
    边缘感知损失 - 专门针对ViT的patch特性设计
    """
    def __init__(self, patch_size=16):
        super(EdgeAwareLoss, self).__init__()
        self.patch_size = patch_size
        
        # Sobel边缘检测核
        self.register_buffer('sobel_x', torch.tensor([
            [-1, 0, 1],
            [-2, 0, 2], 
            [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y', torch.tensor([
            [-1, -2, -1],
            [0, 0, 0],
            [1, 2, 1]
        ]).float().view(1, 1, 3, 3))
        
    def forward(self, pred, target):
        """
        计算边缘感知损失
        Args:
            pred: 预测结果 [B, 1, H, W]
            target: 真实标签 [B, 1, H, W]
        """
        # 计算边缘
        pred_edge_x = F.conv2d(pred, self.sobel_x, padding=1)
        pred_edge_y = F.conv2d(pred, self.sobel_y, padding=1)
        pred_edge = torch.sqrt(pred_edge_x**2 + pred_edge_y**2 + 1e-8)
        
        target_edge_x = F.conv2d(target, self.sobel_x, padding=1)
        target_edge_y = F.conv2d(target, self.sobel_y, padding=1)
        target_edge = torch.sqrt(target_edge_x**2 + target_edge_y**2 + 1e-8)
        
        # 边缘损失
        edge_loss = F.mse_loss(pred_edge, target_edge)
        
        # Patch级别的边缘损失 - 适配ViT的patch特性
        B, C, H, W = pred.shape
        patch_h, patch_w = H // self.patch_size, W // self.patch_size
        
        if patch_h > 0 and patch_w > 0:
            # 重塑为patch格式
            pred_patches = pred.unfold(2, self.patch_size, self.patch_size).unfold(3, self.patch_size, self.patch_size)
            target_patches = target.unfold(2, self.patch_size, self.patch_size).unfold(3, self.patch_size, self.patch_size)
            
            # 计算每个patch的边缘密度
            pred_patch_edges = pred_patches.mean(dim=(-2, -1))  # [B, 1, patch_h, patch_w]
            target_patch_edges = target_patches.mean(dim=(-2, -1))
            
            patch_edge_loss = F.mse_loss(pred_patch_edges, target_patch_edges)
        else:
            patch_edge_loss = torch.tensor(0.0, device=pred.device)
        
        return edge_loss + 0.5 * patch_edge_loss


class MultiScaleConsistencyLoss(nn.Module):
    """
    多尺度一致性损失 - 确保不同尺度预测的一致性
    """
    def __init__(self, scales=[0.5, 0.75, 1.0, 1.25]):
        super(MultiScaleConsistencyLoss, self).__init__()
        self.scales = scales
        
    def forward(self, pred, target):
        """
        Args:
            pred: 预测结果 [B, 1, H, W]
            target: 真实标签 [B, 1, H, W]
        """
        total_loss = 0
        base_size = pred.shape[2:]
        
        for scale in self.scales:
            if scale == 1.0:
                continue
                
            # 缩放尺寸
            scaled_size = (int(base_size[0] * scale), int(base_size[1] * scale))
            
            # 缩放预测和目标
            pred_scaled = F.interpolate(pred, size=scaled_size, mode='bilinear', align_corners=True)
            target_scaled = F.interpolate(target, size=scaled_size, mode='bilinear', align_corners=True)
            
            # 缩放回原尺寸
            pred_rescaled = F.interpolate(pred_scaled, size=base_size, mode='bilinear', align_corners=True)
            target_rescaled = F.interpolate(target_scaled, size=base_size, mode='bilinear', align_corners=True)
            
            # 计算一致性损失
            consistency_loss = F.mse_loss(pred, pred_rescaled) + F.mse_loss(target, target_rescaled)
            total_loss += consistency_loss
            
        return total_loss / (len(self.scales) - 1)


class FeatureContrastiveLoss(nn.Module):
    """
    特征对比损失 - 增强正负样本的区分度
    """
    def __init__(self, temperature=0.1, margin=0.5):
        super(FeatureContrastiveLoss, self).__init__()
        self.temperature = temperature
        self.margin = margin
        
    def forward(self, pred, target):
        """
        Args:
            pred: 预测结果 [B, 1, H, W]
            target: 真实标签 [B, 1, H, W]
        """
        # 展平
        pred_flat = pred.view(pred.size(0), -1)  # [B, H*W]
        target_flat = target.view(target.size(0), -1)  # [B, H*W]
        
        # 正样本：目标为1的位置
        pos_mask = (target_flat > 0.5).float()
        neg_mask = (target_flat <= 0.5).float()
        
        # 计算正负样本的预测值
        pos_pred = pred_flat * pos_mask
        neg_pred = pred_flat * neg_mask
        
        # 对比损失：正样本预测值应该高，负样本预测值应该低
        pos_loss = torch.mean((1 - pos_pred) * pos_mask)  # 正样本接近1
        neg_loss = torch.mean(neg_pred * neg_mask)        # 负样本接近0
        
        # 边际损失：正负样本之间应该有足够的间隔
        pos_mean = torch.sum(pos_pred) / (torch.sum(pos_mask) + 1e-8)
        neg_mean = torch.sum(neg_pred) / (torch.sum(neg_mask) + 1e-8)
        margin_loss = F.relu(self.margin - (pos_mean - neg_mean))
        
        return pos_loss + neg_loss + margin_loss


class FocalLoss(nn.Module):
    """
    Focal Loss - 处理类别不平衡
    """
    def __init__(self, alpha=1, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, pred, target):
        """
        Args:
            pred: 预测概率 [B, 1, H, W]
            target: 真实标签 [B, 1, H, W]
        """
        # 计算BCE
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        
        # 计算pt
        pt = torch.where(target == 1, pred, 1 - pred)
        
        # 计算focal weight
        focal_weight = self.alpha * (1 - pt) ** self.gamma
        
        # 计算focal loss
        focal_loss = focal_weight * bce_loss
        
        return focal_loss.mean()


class DiceLoss(nn.Module):
    """
    Dice Loss - 处理分割任务
    """
    def __init__(self, smooth=1):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
        
    def forward(self, pred, target):
        """
        Args:
            pred: 预测概率 [B, 1, H, W]
            target: 真实标签 [B, 1, H, W]
        """
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)
        
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum()
        
        dice = (2 * intersection + self.smooth) / (union + self.smooth)
        
        return 1 - dice


class ViTOptimizedLoss(nn.Module):
    """
    ViT优化损失函数 - 综合多种损失
    """
    def __init__(self, 
                 bce_weight=1.0,
                 dice_weight=1.0, 
                 focal_weight=1.0,
                 edge_weight=2.0,
                 consistency_weight=0.5,
                 contrastive_weight=0.3,
                 adaptive_weights=True):
        super(ViTOptimizedLoss, self).__init__()
        
        # 基础损失
        self.bce_loss = nn.BCELoss()
        self.dice_loss = DiceLoss()
        self.focal_loss = FocalLoss(alpha=1, gamma=2)
        
        # ViT特化损失
        self.edge_loss = EdgeAwareLoss(patch_size=16)
        self.consistency_loss = MultiScaleConsistencyLoss()
        self.contrastive_loss = FeatureContrastiveLoss()
        
        # 权重
        if adaptive_weights:
            self.bce_weight = nn.Parameter(torch.tensor(bce_weight), requires_grad=True)
            self.dice_weight = nn.Parameter(torch.tensor(dice_weight), requires_grad=True)
            self.focal_weight = nn.Parameter(torch.tensor(focal_weight), requires_grad=True)
            self.edge_weight = nn.Parameter(torch.tensor(edge_weight), requires_grad=True)
            self.consistency_weight = nn.Parameter(torch.tensor(consistency_weight), requires_grad=True)
            self.contrastive_weight = nn.Parameter(torch.tensor(contrastive_weight), requires_grad=True)
        else:
            self.bce_weight = bce_weight
            self.dice_weight = dice_weight
            self.focal_weight = focal_weight
            self.edge_weight = edge_weight
            self.consistency_weight = consistency_weight
            self.contrastive_weight = contrastive_weight
            
        self.adaptive_weights = adaptive_weights
        
    def forward(self, outputs, targets):
        """
        计算总损失
        Args:
            outputs: 模型输出字典
            targets: 真实标签 [B, 1, H, W]
        """
        losses = {}
        total_loss = 0
        
        # 获取主要预测
        if 'refined_pred' in outputs:
            main_pred = outputs['refined_pred'][:, 1:2]  # 取前景通道
        elif 'ensemble_pred' in outputs:
            main_pred = outputs['ensemble_pred']
        else:
            main_pred = outputs['pred_high']
            
        # 基础损失
        losses['bce'] = self.bce_loss(main_pred, targets)
        losses['dice'] = self.dice_loss(main_pred, targets)
        losses['focal'] = self.focal_loss(main_pred, targets)
        
        # ViT特化损失
        losses['edge'] = self.edge_loss(main_pred, targets)
        losses['consistency'] = self.consistency_loss(main_pred, targets)
        losses['contrastive'] = self.contrastive_loss(main_pred, targets)
        
        # 多尺度损失
        if 'pred_high' in outputs and 'pred_edge' in outputs:
            losses['pred_high_bce'] = self.bce_loss(outputs['pred_high'], targets) * 0.5
            losses['pred_edge_bce'] = self.bce_loss(outputs['pred_edge'], targets) * 0.3
        
        # 计算加权总损失
        if self.adaptive_weights:
            # 使用可学习权重
            total_loss = (torch.abs(self.bce_weight) * losses['bce'] +
                         torch.abs(self.dice_weight) * losses['dice'] +
                         torch.abs(self.focal_weight) * losses['focal'] +
                         torch.abs(self.edge_weight) * losses['edge'] +
                         torch.abs(self.consistency_weight) * losses['consistency'] +
                         torch.abs(self.contrastive_weight) * losses['contrastive'])
        else:
            # 使用固定权重
            total_loss = (self.bce_weight * losses['bce'] +
                         self.dice_weight * losses['dice'] +
                         self.focal_weight * losses['focal'] +
                         self.edge_weight * losses['edge'] +
                         self.consistency_weight * losses['consistency'] +
                         self.contrastive_weight * losses['contrastive'])
        
        # 添加多尺度损失
        if 'pred_high_bce' in losses:
            total_loss += losses['pred_high_bce']
        if 'pred_edge_bce' in losses:
            total_loss += losses['pred_edge_bce']
        
        losses['total'] = total_loss
        
        return total_loss, losses
    
    def get_loss_weights(self):
        """获取当前损失权重"""
        if self.adaptive_weights:
            return {
                'bce_weight': torch.abs(self.bce_weight).item(),
                'dice_weight': torch.abs(self.dice_weight).item(),
                'focal_weight': torch.abs(self.focal_weight).item(),
                'edge_weight': torch.abs(self.edge_weight).item(),
                'consistency_weight': torch.abs(self.consistency_weight).item(),
                'contrastive_weight': torch.abs(self.contrastive_weight).item()
            }
        else:
            return {
                'bce_weight': self.bce_weight,
                'dice_weight': self.dice_weight,
                'focal_weight': self.focal_weight,
                'edge_weight': self.edge_weight,
                'consistency_weight': self.consistency_weight,
                'contrastive_weight': self.contrastive_weight
            }


def create_vit_optimized_loss(adaptive_weights=True):
    """创建ViT优化损失函数"""
    return ViTOptimizedLoss(
        bce_weight=1.0,
        dice_weight=1.0,
        focal_weight=1.0,
        edge_weight=2.0,
        consistency_weight=0.5,
        contrastive_weight=0.3,
        adaptive_weights=adaptive_weights
    )


if __name__ == "__main__":
    # 测试损失函数
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建损失函数
    criterion = create_vit_optimized_loss(adaptive_weights=True).to(device)
    
    # 模拟数据
    batch_size = 4
    outputs = {
        'pred_high': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'pred_edge': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'ensemble_pred': torch.sigmoid(torch.randn(batch_size, 1, 416, 416)).to(device),
        'refined_pred': torch.softmax(torch.randn(batch_size, 2, 416, 416), dim=1).to(device)
    }
    targets = torch.randint(0, 2, (batch_size, 1, 416, 416)).float().to(device)
    
    # 计算损失
    total_loss, losses = criterion(outputs, targets)
    
    print("损失函数测试:")
    print(f"总损失: {total_loss.item():.4f}")
    for key, value in losses.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.item():.4f}")
    
    print("\n当前损失权重:")
    weights = criterion.get_loss_weights()
    for key, value in weights.items():
        print(f"  {key}: {value:.4f}")
    
    print("\n✅ ViT优化损失函数测试成功!")