"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_optimized_vit.py
 @Function: Optimized ViT for Glass Detection - 解决ViT效果不佳的问题

主要优化：
1. 使用更小的patch_size (8x8) 保留细节
2. 支持416x416输入分辨率，避免信息损失
3. 多尺度ViT特征提取
4. 改进的ViT-CNN特征融合策略
5. 针对玻璃检测的专门优化

目标：让ViT在玻璃检测中达到90%+ IoU
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import sys
import os

# 添加路径
sys.path.append('/home/<USER>/ws/IG_SLAM/ig_glass')
from attention_scsa import EnhancedSCSA
from diff_crf import SimplifiedDiffCRF


class OptimizedViTBackbone(nn.Module):
    """
    优化的ViT骨干网络，专门针对玻璃检测
    主要改进：
    1. 更小的patch_size (8x8) 保留边缘细节
    2. 支持416x416输入，避免分辨率损失
    3. 多尺度特征提取
    """
    def __init__(self, 
                 img_size=416,
                 patch_size=8,  # 从14改为8，保留更多细节
                 embed_dim=384,
                 depth=12,
                 num_heads=6,
                 mlp_ratio=4.0,
                 drop_rate=0.1):
        super(OptimizedViTBackbone, self).__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        self.num_patches = (img_size // patch_size) ** 2  # 416//8 = 52, 52*52 = 2704
        
        print(f"🔧 优化ViT配置:")
        print(f"   输入尺寸: {img_size}x{img_size}")
        print(f"   Patch尺寸: {patch_size}x{patch_size}")
        print(f"   Patch数量: {self.num_patches}")
        print(f"   嵌入维度: {embed_dim}")
        
        # Patch嵌入
        self.patch_embed = nn.Conv2d(3, embed_dim, kernel_size=patch_size, stride=patch_size)
        
        # 位置编码
        self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches + 1, embed_dim))
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        
        # Transformer块
        self.blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, mlp_ratio, drop_rate)
            for _ in range(depth)
        ])
        
        # 层归一化
        self.norm = nn.LayerNorm(embed_dim)
        
        # 多尺度特征提取点
        self.feature_layers = [3, 6, 9, 11]  # 在这些层提取特征
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """权重初始化"""
        torch.nn.init.trunc_normal_(self.pos_embed, std=0.02)
        torch.nn.init.trunc_normal_(self.cls_token, std=0.02)
        
        for m in self.modules():
            if isinstance(m, nn.Linear):
                torch.nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x):
        """
        Args:
            x: [B, 3, 416, 416]
        Returns:
            multi_scale_features: 多尺度特征列表
        """
        B = x.shape[0]
        
        # Patch嵌入 [B, 3, 416, 416] -> [B, 384, 52, 52] -> [B, 384, 2704] -> [B, 2704, 384]
        x = self.patch_embed(x).flatten(2).transpose(1, 2)
        
        # 添加CLS token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)  # [B, 2705, 384]
        
        # 添加位置编码
        x = x + self.pos_embed
        
        # 多尺度特征提取
        multi_scale_features = []
        
        for i, block in enumerate(self.blocks):
            x = block(x)
            
            # 在指定层提取特征
            if i in self.feature_layers:
                # 移除CLS token，只保留patch tokens
                patch_tokens = x[:, 1:]  # [B, 2704, 384]
                
                # 重塑为特征图格式
                patch_h = patch_w = int(math.sqrt(patch_tokens.size(1)))  # 52
                feature_map = patch_tokens.transpose(1, 2).reshape(B, self.embed_dim, patch_h, patch_w)
                multi_scale_features.append(feature_map)
        
        # 最终特征
        x = self.norm(x)
        final_patch_tokens = x[:, 1:]
        patch_h = patch_w = int(math.sqrt(final_patch_tokens.size(1)))
        final_feature_map = final_patch_tokens.transpose(1, 2).reshape(B, self.embed_dim, patch_h, patch_w)
        multi_scale_features.append(final_feature_map)
        
        return multi_scale_features


class TransformerBlock(nn.Module):
    """Transformer块"""
    def __init__(self, embed_dim, num_heads, mlp_ratio=4.0, drop_rate=0.1):
        super(TransformerBlock, self).__init__()
        
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = nn.MultiheadAttention(embed_dim, num_heads, dropout=drop_rate, batch_first=True)
        
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop_rate),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(drop_rate)
        )
    
    def forward(self, x):
        # 自注意力
        x_norm = self.norm1(x)
        attn_out, _ = self.attn(x_norm, x_norm, x_norm)
        x = x + attn_out
        
        # MLP
        x = x + self.mlp(self.norm2(x))
        
        return x


class ViTCNNFusionModule(nn.Module):
    """
    ViT-CNN特征融合模块
    解决ViT和CNN特征不匹配的问题
    """
    def __init__(self, vit_dim=384, cnn_dims=[64, 128, 256, 512]):
        super(ViTCNNFusionModule, self).__init__()
        
        self.vit_dim = vit_dim
        self.cnn_dims = cnn_dims
        
        # ViT特征适配器
        self.vit_adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(vit_dim, cnn_dim, 1),
                nn.BatchNorm2d(cnn_dim),
                nn.ReLU(inplace=True)
            ) for cnn_dim in cnn_dims
        ])
        
        # 融合模块
        self.fusion_modules = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(cnn_dim * 2, cnn_dim, 3, 1, 1),  # ViT + CNN
                nn.BatchNorm2d(cnn_dim),
                nn.ReLU(inplace=True),
                nn.Conv2d(cnn_dim, cnn_dim, 3, 1, 1),
                nn.BatchNorm2d(cnn_dim),
                nn.ReLU(inplace=True)
            ) for cnn_dim in cnn_dims
        ])
        
        # 注意力增强
        self.attention_modules = nn.ModuleList([
            EnhancedSCSA(
                dim=cnn_dim,
                head_num=max(1, cnn_dim // 64),
                window_size=7,
                edge_enhance=True,
                multi_scale=True
            ) for cnn_dim in cnn_dims
        ])
    
    def forward(self, vit_features, cnn_features):
        """
        Args:
            vit_features: list of ViT features at different scales
            cnn_features: list of CNN features at different scales
        Returns:
            fused_features: list of fused features
        """
        fused_features = []
        
        for i, (vit_feat, cnn_feat) in enumerate(zip(vit_features, cnn_features)):
            # 调整ViT特征尺寸到CNN特征尺寸
            if vit_feat.shape[2:] != cnn_feat.shape[2:]:
                vit_feat = F.interpolate(vit_feat, size=cnn_feat.shape[2:], 
                                       mode='bilinear', align_corners=True)
            
            # ViT特征适配
            adapted_vit = self.vit_adapters[i](vit_feat)
            
            # 特征融合
            fused = torch.cat([adapted_vit, cnn_feat], dim=1)
            fused = self.fusion_modules[i](fused)
            
            # 注意力增强
            enhanced = self.attention_modules[i](fused)
            
            fused_features.append(enhanced)
        
        return fused_features


class GDNetOptimizedViT(nn.Module):
    """
    优化的ViT玻璃检测网络
    解决ViT效果不佳的核心问题
    """
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetOptimizedViT, self).__init__()
        
        # 优化的ViT骨干网络
        self.vit_backbone = OptimizedViTBackbone(
            img_size=416,
            patch_size=8,  # 关键改进：更小的patch size
            embed_dim=384,
            depth=12,
            num_heads=6
        )
        
        # CNN辅助网络（用于多尺度特征）
        self.cnn_backbone = self._build_cnn_backbone()
        
        # ViT-CNN特征融合
        self.fusion_module = ViTCNNFusionModule(
            vit_dim=384,
            cnn_dims=[64, 128, 256, 512]
        )
        
        # 最终特征处理
        self.final_conv = nn.Sequential(
            nn.Conv2d(512, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True)
        )
        
        # 预测头
        self.predict = nn.Conv2d(128, 1, 3, 1, 1)
        
        # CRF后处理
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=8.0,
            gaussian_weight=4.0,
            bilateral_spatial_sigma=35.0,
            bilateral_color_sigma=2.5,
            gaussian_sigma=1.2,
            trainable=trainable_crf
        )
        
        print(f"✅ 优化ViT玻璃检测网络创建成功!")
        print(f"   参数量: {sum(p.numel() for p in self.parameters())/1e6:.1f}M")
    
    def _build_cnn_backbone(self):
        """构建CNN辅助网络"""
        layers = nn.ModuleList([
            # Layer 1: 416 -> 208
            nn.Sequential(
                nn.Conv2d(3, 64, 7, 2, 3),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(3, 2, 1)  # 208 -> 104
            ),
            # Layer 2: 104 -> 52
            nn.Sequential(
                nn.Conv2d(64, 128, 3, 2, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True)
            ),
            # Layer 3: 52 -> 26
            nn.Sequential(
                nn.Conv2d(128, 256, 3, 2, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            ),
            # Layer 4: 26 -> 13
            nn.Sequential(
                nn.Conv2d(256, 512, 3, 2, 1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True)
            )
        ])
        return layers
    
    def forward(self, x):
        """
        Args:
            x: [B, 3, 416, 416]
        Returns:
            dict with predictions
        """
        # ViT多尺度特征提取
        vit_features = self.vit_backbone(x)  # 4个不同尺度的特征
        
        # CNN特征提取
        cnn_features = []
        cnn_x = x
        for layer in self.cnn_backbone:
            cnn_x = layer(cnn_x)
            cnn_features.append(cnn_x)
        
        # ViT-CNN特征融合
        fused_features = self.fusion_module(vit_features, cnn_features)
        
        # 使用最高分辨率的融合特征
        final_features = fused_features[-1]  # [B, 512, 13, 13]
        
        # 最终处理
        final_features = self.final_conv(final_features)  # [B, 128, 13, 13]
        
        # 预测
        pred = self.predict(final_features)  # [B, 1, 13, 13]
        
        # 上采样到原始尺寸
        pred = F.interpolate(pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        
        # Sigmoid激活
        pred_prob = torch.sigmoid(pred)
        
        # CRF后处理
        bg_logits = torch.log(1 - pred_prob + 1e-7)
        fg_logits = torch.log(pred_prob + 1e-7)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
        
        try:
            refined_pred = self.crf(combined_logits, x)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_pred = torch.cat([1 - pred_prob, pred_prob], dim=1)
        
        return {
            'pred': pred_prob,
            'refined_pred': refined_pred,
            'vit_features': vit_features,
            'cnn_features': cnn_features,
            'fused_features': fused_features
        }


def create_optimized_vit_model(backbone_path=None, crf_iter=5, trainable_crf=True):
    """创建优化的ViT模型"""
    return GDNetOptimizedViT(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )
