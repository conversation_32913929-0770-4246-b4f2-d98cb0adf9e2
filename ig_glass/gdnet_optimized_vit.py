"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_optimized_vit.py
 @Function: Optimized Proteus ViT-B for Glass Detection - 解决ViT效果不佳的问题

主要优化：
1. 正确使用Proteus ViT-B预训练模型
2. 优化patch_size策略，平衡细节和计算效率
3. 支持416x416输入分辨率，避免信息损失
4. 多尺度ViT特征提取
5. 改进的ViT-CNN特征融合策略
6. 针对玻璃检测的专门优化

目标：让ViT在玻璃检测中达到90%+ IoU
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import sys
import os
from functools import partial

# 添加Proteus路径到系统路径
proteus_path = '/home/<USER>/ws/IG_SLAM/Proteus-pytorch/pretrain'
if proteus_path not in sys.path:
    sys.path.append(proteus_path)

# 添加ig_glass路径
ig_glass_path = '/home/<USER>/ws/IG_SLAM/ig_glass'
if ig_glass_path not in sys.path:
    sys.path.append(ig_glass_path)

# 使用Proteus框架的DINOv2模型
try:
    from models_dinov2 import DinoVisionTransformer, Attention, Block, vit_base
    PROTEUS_AVAILABLE = True
    print("✅ Proteus框架加载成功")
except ImportError as e:
    print(f"⚠️ Proteus框架导入失败: {e}")
    PROTEUS_AVAILABLE = False

from attention_scsa import EnhancedSCSA
from diff_crf import SimplifiedDiffCRF


class ProteusViTBFeatureExtractor(nn.Module):
    """
    基于Proteus ViT-B的优化特征提取器
    主要改进：
    1. 正确使用Proteus ViT-B预训练权重
    2. 优化patch_size策略 (使用14，但增加多尺度处理)
    3. 支持416x416输入，避免分辨率损失
    4. 多尺度特征提取
    """
    def __init__(self, pretrained_path=None, img_size=416):
        super(ProteusViTBFeatureExtractor, self).__init__()

        self.img_size = img_size
        self.patch_size = 14  # 保持与Proteus预训练权重一致
        self.embed_dim = 768  # ViT-B的嵌入维度

        print(f"🔧 Proteus ViT-B优化配置:")
        print(f"   输入尺寸: {img_size}x{img_size}")
        print(f"   Patch尺寸: {self.patch_size}x{self.patch_size}")
        print(f"   嵌入维度: {self.embed_dim}")

        if PROTEUS_AVAILABLE:
            # 使用Proteus框架的ViT-B
            if pretrained_path and os.path.exists(pretrained_path):
                print(f"📦 加载Proteus ViT-B预训练权重: {pretrained_path}")

                # 创建ViT-B模型，强制使用标准Attention避免xformers问题
                self.vit_model = DinoVisionTransformer(
                    patch_size=14,
                    embed_dim=768,
                    depth=12,
                    num_heads=12,
                    mlp_ratio=4,
                    block_fn=partial(Block, attn_class=Attention),
                    num_register_tokens=0
                )

                # 加载预训练权重
                try:
                    checkpoint = torch.load(pretrained_path, map_location='cpu')
                    if 'model' in checkpoint:
                        model_weights = checkpoint['model']
                        # 提取student.backbone的权重
                        student_weights = {}
                        for key, value in model_weights.items():
                            if key.startswith('student.backbone.'):
                                new_key = key.replace('student.backbone.', '')
                                student_weights[new_key] = value

                        print(f"   提取Student权重: {len(student_weights)}个参数")
                        missing_keys, unexpected_keys = self.vit_model.load_state_dict(student_weights, strict=False)
                        if missing_keys:
                            print(f"   缺失键: {len(missing_keys)}个")
                        if unexpected_keys:
                            print(f"   意外键: {len(unexpected_keys)}个")
                    else:
                        self.vit_model.load_state_dict(checkpoint, strict=False)

                    print(f"✅ Proteus ViT-B权重加载成功")
                except Exception as e:
                    print(f"⚠️ 权重加载失败: {e}")
                    print("   使用随机初始化")
            else:
                print(f"⚠️ 预训练权重路径无效，使用随机初始化")
                self.vit_model = DinoVisionTransformer(
                    patch_size=14,
                    embed_dim=768,
                    depth=12,
                    num_heads=12,
                    mlp_ratio=4,
                    block_fn=partial(Block, attn_class=Attention),
                    num_register_tokens=0
                )
        else:
            # Fallback: 简单的ViT实现
            print("⚠️ Proteus不可用，使用简化ViT实现")
            self.vit_model = self._create_simple_vit()

        # 多尺度特征适配器
        self.feature_adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(768, 256, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            ),
            nn.Sequential(
                nn.Conv2d(768, 512, 1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True)
            ),
            nn.Sequential(
                nn.Conv2d(768, 768, 1),
                nn.BatchNorm2d(768),
                nn.ReLU(inplace=True)
            )
        ])

        # 多尺度处理模块
        self.multi_scale_processor = nn.ModuleList([
            # 1/2尺度处理
            nn.Sequential(
                nn.Conv2d(3, 64, 7, 2, 3),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(3, 2, 1)
            ),
            # 1/4尺度处理
            nn.Sequential(
                nn.Conv2d(64, 128, 3, 2, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True)
            ),
            # 1/8尺度处理
            nn.Sequential(
                nn.Conv2d(128, 256, 3, 2, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            )
        ])

    def _create_simple_vit(self):
        """创建简化的ViT实现作为fallback"""
        return nn.Sequential(
            nn.Conv2d(3, 768, 14, 14),
            nn.AdaptiveAvgPool2d((16, 16)),
            nn.ReLU()
        )

    def forward(self, x):
        """
        Args:
            x: [B, 3, 416, 416]
        Returns:
            multi_scale_features: 多尺度特征列表
        """
        B, C, H, W = x.shape
        multi_scale_features = []

        if PROTEUS_AVAILABLE and hasattr(self.vit_model, 'forward_features'):
            # 使用Proteus ViT-B提取特征
            # 为了保持细节，我们使用多个尺度的输入
            scales = [224, 336, 448]  # 多尺度输入

            for i, scale in enumerate(scales):
                # 调整输入尺寸
                x_scaled = F.interpolate(x, size=(scale, scale), mode='bilinear', align_corners=True)

                # 通过ViT提取特征
                vit_output = self.vit_model.forward_features(x_scaled)
                patch_tokens = vit_output["x_norm_patchtokens"]

                # 计算特征图尺寸
                num_patches_h = scale // self.patch_size
                num_patches_w = scale // self.patch_size

                # 重塑为特征图格式
                vit_features = patch_tokens.transpose(1, 2).reshape(B, self.embed_dim, num_patches_h, num_patches_w)

                # 特征适配
                adapted_features = self.feature_adapters[i](vit_features)

                # 调整到统一尺寸 (H//14, W//14)
                target_h, target_w = H // 14, W // 14
                if adapted_features.shape[2:] != (target_h, target_w):
                    adapted_features = F.interpolate(adapted_features, size=(target_h, target_w),
                                                   mode='bilinear', align_corners=True)

                multi_scale_features.append(adapted_features)
        else:
            # Fallback处理
            for i, processor in enumerate(self.multi_scale_processor):
                if i == 0:
                    feat = processor(x)
                else:
                    feat = processor(feat)

                # 调整到目标尺寸
                target_h, target_w = H // (14 * (2**i)), W // (14 * (2**i))
                if feat.shape[2:] != (target_h, target_w):
                    feat = F.interpolate(feat, size=(target_h, target_w), mode='bilinear', align_corners=True)

                multi_scale_features.append(feat)

        return multi_scale_features


class TransformerBlock(nn.Module):
    """Transformer块"""
    def __init__(self, embed_dim, num_heads, mlp_ratio=4.0, drop_rate=0.1):
        super(TransformerBlock, self).__init__()
        
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = nn.MultiheadAttention(embed_dim, num_heads, dropout=drop_rate, batch_first=True)
        
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop_rate),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(drop_rate)
        )
    
    def forward(self, x):
        # 自注意力
        x_norm = self.norm1(x)
        attn_out, _ = self.attn(x_norm, x_norm, x_norm)
        x = x + attn_out
        
        # MLP
        x = x + self.mlp(self.norm2(x))
        
        return x


class ViTCNNFusionModule(nn.Module):
    """
    ViT-CNN特征融合模块
    解决ViT和CNN特征不匹配的问题
    """
    def __init__(self, vit_dim=384, cnn_dims=[64, 128, 256, 512]):
        super(ViTCNNFusionModule, self).__init__()
        
        self.vit_dim = vit_dim
        self.cnn_dims = cnn_dims
        
        # ViT特征适配器
        self.vit_adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(vit_dim, cnn_dim, 1),
                nn.BatchNorm2d(cnn_dim),
                nn.ReLU(inplace=True)
            ) for cnn_dim in cnn_dims
        ])
        
        # 融合模块
        self.fusion_modules = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(cnn_dim * 2, cnn_dim, 3, 1, 1),  # ViT + CNN
                nn.BatchNorm2d(cnn_dim),
                nn.ReLU(inplace=True),
                nn.Conv2d(cnn_dim, cnn_dim, 3, 1, 1),
                nn.BatchNorm2d(cnn_dim),
                nn.ReLU(inplace=True)
            ) for cnn_dim in cnn_dims
        ])
        
        # 注意力增强
        self.attention_modules = nn.ModuleList([
            EnhancedSCSA(
                dim=cnn_dim,
                head_num=max(1, cnn_dim // 64),
                window_size=7,
                edge_enhance=True,
                multi_scale=True
            ) for cnn_dim in cnn_dims
        ])
    
    def forward(self, vit_features, cnn_features):
        """
        Args:
            vit_features: list of ViT features at different scales
            cnn_features: list of CNN features at different scales
        Returns:
            fused_features: list of fused features
        """
        fused_features = []
        
        for i, (vit_feat, cnn_feat) in enumerate(zip(vit_features, cnn_features)):
            # 调整ViT特征尺寸到CNN特征尺寸
            if vit_feat.shape[2:] != cnn_feat.shape[2:]:
                vit_feat = F.interpolate(vit_feat, size=cnn_feat.shape[2:], 
                                       mode='bilinear', align_corners=True)
            
            # ViT特征适配
            adapted_vit = self.vit_adapters[i](vit_feat)
            
            # 特征融合
            fused = torch.cat([adapted_vit, cnn_feat], dim=1)
            fused = self.fusion_modules[i](fused)
            
            # 注意力增强
            enhanced = self.attention_modules[i](fused)
            
            fused_features.append(enhanced)
        
        return fused_features


class GDNetOptimizedViT(nn.Module):
    """
    优化的ViT玻璃检测网络
    解决ViT效果不佳的核心问题
    """
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetOptimizedViT, self).__init__()
        
        # Proteus ViT-B特征提取器
        self.vit_backbone = ProteusViTBFeatureExtractor(
            pretrained_path=backbone_path,
            img_size=416
        )

        # CNN辅助网络（用于多尺度特征）
        self.cnn_backbone = self._build_cnn_backbone()

        # ViT-CNN特征融合 - 适配多尺度ViT特征
        self.fusion_module = ViTCNNFusionModule(
            vit_dim=768,  # ViT-B的输出维度
            cnn_dims=[64, 128, 256, 512]
        )
        
        # 最终特征处理
        self.final_conv = nn.Sequential(
            nn.Conv2d(512, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True)
        )
        
        # 预测头
        self.predict = nn.Conv2d(128, 1, 3, 1, 1)
        
        # CRF后处理
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=8.0,
            gaussian_weight=4.0,
            bilateral_spatial_sigma=35.0,
            bilateral_color_sigma=2.5,
            gaussian_sigma=1.2,
            trainable=trainable_crf
        )
        
        print(f"✅ 优化ViT玻璃检测网络创建成功!")
        print(f"   参数量: {sum(p.numel() for p in self.parameters())/1e6:.1f}M")
    
    def _build_cnn_backbone(self):
        """构建CNN辅助网络"""
        layers = nn.ModuleList([
            # Layer 1: 416 -> 208
            nn.Sequential(
                nn.Conv2d(3, 64, 7, 2, 3),
                nn.BatchNorm2d(64),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(3, 2, 1)  # 208 -> 104
            ),
            # Layer 2: 104 -> 52
            nn.Sequential(
                nn.Conv2d(64, 128, 3, 2, 1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True)
            ),
            # Layer 3: 52 -> 26
            nn.Sequential(
                nn.Conv2d(128, 256, 3, 2, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            ),
            # Layer 4: 26 -> 13
            nn.Sequential(
                nn.Conv2d(256, 512, 3, 2, 1),
                nn.BatchNorm2d(512),
                nn.ReLU(inplace=True)
            )
        ])
        return layers
    
    def forward(self, x):
        """
        Args:
            x: [B, 3, 416, 416]
        Returns:
            dict with predictions
        """
        # ViT多尺度特征提取
        vit_features = self.vit_backbone(x)  # 4个不同尺度的特征
        
        # CNN特征提取
        cnn_features = []
        cnn_x = x
        for layer in self.cnn_backbone:
            cnn_x = layer(cnn_x)
            cnn_features.append(cnn_x)
        
        # ViT-CNN特征融合
        fused_features = self.fusion_module(vit_features, cnn_features)
        
        # 使用最高分辨率的融合特征
        final_features = fused_features[-1]  # [B, 512, 13, 13]
        
        # 最终处理
        final_features = self.final_conv(final_features)  # [B, 128, 13, 13]
        
        # 预测
        pred = self.predict(final_features)  # [B, 1, 13, 13]
        
        # 上采样到原始尺寸
        pred = F.interpolate(pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        
        # Sigmoid激活
        pred_prob = torch.sigmoid(pred)
        
        # CRF后处理
        bg_logits = torch.log(1 - pred_prob + 1e-7)
        fg_logits = torch.log(pred_prob + 1e-7)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
        
        try:
            refined_pred = self.crf(combined_logits, x)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_pred = torch.cat([1 - pred_prob, pred_prob], dim=1)
        
        return {
            'pred': pred_prob,
            'refined_pred': refined_pred,
            'vit_features': vit_features,
            'cnn_features': cnn_features,
            'fused_features': fused_features
        }


def create_optimized_vit_model(backbone_path=None, crf_iter=5, trainable_crf=True):
    """创建优化的ViT模型"""
    return GDNetOptimizedViT(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )
