"""
 @Time    : 2024
 <AUTHOR> <PERSON><PERSON>

 @Project : IG_SLAM
 @File    : gdnet_crf.py
 @Function: GDNet with end-to-end CRF

"""
import torch
import torch.nn as nn
import torch.nn.functional as F

from ig_glass.gdnet import GDNet, CBAM, LCFI
from ig_glass.diff_crf import SimplifiedDiffCRF


class GDNetCRF(nn.Module):
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetCRF, self).__init__()
        # Base GDNet model
        self.gdnet = GDNet(backbone_path=backbone_path)

        # CRF layer with parameters from grid search [1.5, 40, 3, 5, 10]
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,  # From compat=10
            gaussian_weight=5.0,     # From compat=5
            bilateral_spatial_sigma=40.0,  # From sxy=40
            bilateral_color_sigma=3.0,     # From srgb=3
            gaussian_sigma=1.5,            # From sxy=1.5
            trainable=trainable_crf
        )

        # Convert single-channel output to 2-channel logits for CRF
        self.to_logits = nn.Conv2d(1, 2, kernel_size=1)

    def forward(self, x):
        # Get predictions from base GDNet
        h_predict, l_predict, final_predict = self.gdnet(x)

        # Convert final prediction to logits for CRF
        # Ensure predictions are in valid range
        final_predict = torch.clamp(final_predict, min=1e-7, max=1.0 - 1e-7)

        # Check for NaN or Inf values
        if torch.isnan(final_predict).any() or torch.isinf(final_predict).any():
            print("Warning: NaN or Inf values detected in final predictions")
            final_predict = torch.nan_to_num(final_predict, nan=0.5, posinf=1.0, neginf=0.0)

        # First, we need to convert sigmoid output to logits
        final_logits = torch.log(final_predict / (1 - final_predict))

        # Expand to 2 channels (background and foreground)
        bg_logits = -final_logits  # Background logits
        fg_logits = final_logits    # Foreground logits
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # Check for NaN or Inf values in logits
        if torch.isnan(combined_logits).any() or torch.isinf(combined_logits).any():
            print("Warning: NaN or Inf values detected in combined logits")
            combined_logits = torch.nan_to_num(combined_logits, nan=0.0, posinf=10.0, neginf=-10.0)

        # Normalize input image for CRF
        # The input image should be in [0,1] range for the bilateral filter
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            # If the image is not in [0,1] range, normalize it
            # Assuming the image is in the range used by the ImageNet normalization
            # Undo the normalization: x = (x * std) + mean
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean

            # Ensure the image is in [0,1] range
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)

        # Apply CRF refinement
        refined_predict = self.crf(combined_logits, normalized_img)

        # Return all predictions for training
        return h_predict, l_predict, final_predict, refined_predict


class GDNetCRFv2(nn.Module):
    """
    Enhanced version of GDNet with integrated CRF layer
    This version rebuilds the model architecture to better integrate with CRF
    """
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetCRFv2, self).__init__()
        # params

        # backbone - reuse from GDNet
        gdnet = GDNet(backbone_path=backbone_path)
        self.layer0 = gdnet.layer0
        self.layer1 = gdnet.layer1
        self.layer2 = gdnet.layer2
        self.layer3 = gdnet.layer3
        self.layer4 = gdnet.layer4

        self.h5_conv = gdnet.h5_conv
        self.h4_conv = gdnet.h4_conv
        self.h3_conv = gdnet.h3_conv
        self.l2_conv = gdnet.l2_conv

        # h fusion
        self.h5_up = gdnet.h5_up
        self.h3_down = gdnet.h3_down
        self.h_fusion = gdnet.h_fusion
        self.h_fusion_conv = gdnet.h_fusion_conv

        # l fusion
        self.l_fusion_conv = gdnet.l_fusion_conv
        self.h2l = gdnet.h2l

        # final fusion
        self.h_up_for_final_fusion = gdnet.h_up_for_final_fusion
        self.final_fusion = gdnet.final_fusion
        self.final_fusion_conv = gdnet.final_fusion_conv

        # predict conv
        self.h_predict = gdnet.h_predict
        self.l_predict = gdnet.l_predict
        self.final_predict = gdnet.final_predict

        # CRF layer with parameters from grid search [1.5, 40, 3, 5, 10]
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,  # From compat=10
            gaussian_weight=5.0,     # From compat=5
            bilateral_spatial_sigma=40.0,  # From sxy=40
            bilateral_color_sigma=3.0,     # From srgb=3
            gaussian_sigma=1.5,            # From sxy=1.5
            trainable=trainable_crf
        )

        # Convert single-channel output to 2-channel logits for CRF
        self.to_logits = nn.Conv2d(1, 2, kernel_size=1)

        # Class balancing weights for handling imbalanced data
        self.class_weights = nn.Parameter(torch.tensor([1.0, 1.5]), requires_grad=True)

        for m in self.modules():
            if isinstance(m, nn.ReLU):
                m.inplace = True

    def forward(self, x):
        # x: [batch_size, channel=3, h, w]
        layer0 = self.layer0(x)  # [-1, 64, h/2, w/2]
        layer1 = self.layer1(layer0)  # [-1, 256, h/4, w/4]
        layer2 = self.layer2(layer1)  # [-1, 512, h/8, w/8]
        layer3 = self.layer3(layer2)  # [-1, 1024, h/16, w/16]
        layer4 = self.layer4(layer3)  # [-1, 2048, h/32, w/32]

        h5_conv = self.h5_conv(layer4)
        h4_conv = self.h4_conv(layer3)
        h3_conv = self.h3_conv(layer2)
        l2_conv = self.l2_conv(layer1)

        # h fusion
        h5_up = self.h5_up(h5_conv)
        h3_down = self.h3_down(h3_conv)
        h_fusion = self.h_fusion(torch.cat((h5_up, h4_conv, h3_down), 1))
        h_fusion = self.h_fusion_conv(h_fusion)

        # l fusion
        l_fusion = self.l_fusion_conv(l2_conv)
        h2l = self.h2l(h_fusion)
        l_fusion = F.sigmoid(h2l) * l_fusion

        # final fusion
        h_up_for_final_fusion = self.h_up_for_final_fusion(h_fusion)
        final_fusion = self.final_fusion(torch.cat((h_up_for_final_fusion, l_fusion), 1))
        final_fusion = self.final_fusion_conv(final_fusion)

        # h predict
        h_predict = self.h_predict(h_fusion)

        # l predict
        l_predict = self.l_predict(l_fusion)

        # final predict
        final_predict = self.final_predict(final_fusion)

        # rescale to original size
        h_predict = F.interpolate(h_predict, size=x.size()[2:], mode='bilinear', align_corners=True)
        l_predict = F.interpolate(l_predict, size=x.size()[2:], mode='bilinear', align_corners=True)
        final_predict = F.interpolate(final_predict, size=x.size()[2:], mode='bilinear', align_corners=True)

        # Apply sigmoid to get probabilities
        h_predict_prob = torch.sigmoid(h_predict)
        l_predict_prob = torch.sigmoid(l_predict)
        final_predict_prob = torch.sigmoid(final_predict)

        # Convert final prediction to 2-channel logits for CRF
        # Ensure predictions are in valid range
        final_predict_prob = torch.clamp(final_predict_prob, min=1e-7, max=1.0 - 1e-7)

        # Check for NaN or Inf values
        if torch.isnan(final_predict_prob).any() or torch.isinf(final_predict_prob).any():
            print("Warning: NaN or Inf values detected in final predictions")
            final_predict_prob = torch.nan_to_num(final_predict_prob, nan=0.5, posinf=1.0, neginf=0.0)

        # We need to create logits for background and foreground
        bg_logits = torch.log((1 - final_predict_prob) * self.class_weights[0])
        fg_logits = torch.log(final_predict_prob * self.class_weights[1])
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # Check for NaN or Inf values in logits
        if torch.isnan(combined_logits).any() or torch.isinf(combined_logits).any():
            print("Warning: NaN or Inf values detected in combined logits")
            combined_logits = torch.nan_to_num(combined_logits, nan=0.0, posinf=10.0, neginf=-10.0)

        # Normalize input image for CRF
        # The input image should be in [0,1] range for the bilateral filter
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            # If the image is not in [0,1] range, normalize it
            # Assuming the image is in the range used by the ImageNet normalization
            # Undo the normalization: x = (x * std) + mean
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean

            # Ensure the image is in [0,1] range
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)

        # Apply CRF refinement
        refined_predict = self.crf(combined_logits, normalized_img)

        return h_predict_prob, l_predict_prob, final_predict_prob, refined_predict
