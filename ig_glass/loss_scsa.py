"""
 @Time    : 2024
 <AUTHOR> Glennine

 @Project : IG_SLAM
 @File    : loss_scsa.py
 @Function: 针对SCSA注意力机制的损失函数

"""
import torch
import torch.nn as nn
import torch.nn.functional as F

import os
os.environ['CUDA_LAUNCH_BLOCKING'] = "1"


class SCSAEdgeAwareBCELoss(nn.Module):
    """
    适用于SCSA注意力机制的边缘感知BCE损失函数，
    为前景和背景设置平衡权重，针对实时应用的轻量级实现。
    """
    def __init__(self, edge_weight=1.3, body_weight=0.7):
        super(SCSAEdgeAwareBCELoss, self).__init__()
        self.edge_weight = edge_weight
        self.body_weight = body_weight
        
        # Laplacian kernel for edge detection
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1., 8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))
        
        # SCSA模块特有的通道注意力权重
        self.channel_weight = 1.1

    def forward(self, pred, target, eps=1e-7):
        # 确保输入在有效范围内
        pred = torch.clamp(pred, min=eps, max=1.0 - eps)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 检查NaN或Inf值
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            print("警告: 在SCSAEdgeAwareBCELoss中的预测中检测到NaN或Inf值")
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)
            
        if torch.isnan(target).any() or torch.isinf(target).any():
            print("警告: 在SCSAEdgeAwareBCELoss中的目标中检测到NaN或Inf值")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)
        
        # 确保pred和target至少有一个通道
        if pred.dim() == 3:
            pred = pred.unsqueeze(1)
        if target.dim() == 3:
            target = target.unsqueeze(1)
            
        # 检查pred是否有0个通道
        if pred.size(1) == 0:
            print("错误: pred有0个通道。创建一个虚拟预测。")
            pred = torch.ones_like(target) * 0.5
        
        try:
            # 从目标和预测中提取边缘
            target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel.to(target.device), padding=1)))
            
            # 创建边缘感知权重图
            edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
            
            # 计算标准BCE损失
            bce_loss = -(target * torch.log(pred + eps) + (1 - target) * torch.log(1 - pred + eps))
            
            # 应用边缘感知加权
            weighted_loss = bce_loss * edge_weight_map
            
            # 针对SCSA增加通道感知权重
            weighted_loss = weighted_loss * self.channel_weight
            
            return weighted_loss.mean()
        except Exception as e:
            print(f"SCSAEdgeAwareBCELoss错误: {e}")
            # 作为后备返回简单的BCE损失
            return F.binary_cross_entropy(pred, target, reduction='mean')


class SCSAEdgeAwareFocalLoss(nn.Module):
    """
    适用于SCSA注意力机制的边缘感知Focal损失函数，
    为前景和背景设置平衡权重，针对实时应用的轻量级实现。
    """
    def __init__(self, alpha=0.25, gamma=2.0, edge_weight=1.3, body_weight=0.7):
        super(SCSAEdgeAwareFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.edge_weight = edge_weight
        self.body_weight = body_weight
        
        # Laplacian kernel for edge detection
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1., 8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))
        
        # SCSA模块特有的通道注意力权重
        self.channel_weight = 1.1

    def forward(self, pred, target, eps=1e-7):
        # 确保输入在有效范围内
        pred = torch.clamp(pred, min=eps, max=1.0 - eps)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 检查NaN或Inf值
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            print("警告: 在SCSAEdgeAwareFocalLoss中的预测中检测到NaN或Inf值")
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)
            
        if torch.isnan(target).any() or torch.isinf(target).any():
            print("警告: 在SCSAEdgeAwareFocalLoss中的目标中检测到NaN或Inf值")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)
        
        # 确保pred和target至少有一个通道
        if pred.dim() == 3:
            pred = pred.unsqueeze(1)
        if target.dim() == 3:
            target = target.unsqueeze(1)
            
        # 检查pred是否有0个通道
        if pred.size(1) == 0:
            print("错误: pred有0个通道。创建一个虚拟预测。")
            pred = torch.ones_like(target) * 0.5
        
        try:
            # 从目标中提取边缘
            target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel, padding=1)))
            
            # 创建边缘感知权重图
            edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
            
            # 计算focal loss组件
            pt = target * pred + (1 - target) * (1 - pred)  # 正确分类的概率
            focal_weight = (1 - pt) ** self.gamma
            alpha_weight = target * self.alpha + (1 - target) * (1 - self.alpha)
            
            # 计算Focal loss
            focal_loss = -alpha_weight * focal_weight * torch.log(pt + eps)
            
            # 应用边缘感知加权
            weighted_loss = focal_loss * edge_weight_map
            
            # 针对SCSA增加通道感知权重
            weighted_loss = weighted_loss * self.channel_weight
            
            return weighted_loss.mean()
        except Exception as e:
            print(f"SCSAEdgeAwareFocalLoss错误: {e}")
            # 作为后备返回简单的Focal loss
            pt = target * pred + (1 - target) * (1 - pred)
            focal_weight = (1 - pt) ** self.gamma
            alpha_weight = target * self.alpha + (1 - target) * (1 - self.alpha)
            return (-alpha_weight * focal_weight * torch.log(pt + eps)).mean()


class SCSALightIOU(nn.Module):
    """
    适用于SCSA模块的轻量级IoU损失函数实现，针对实时应用
    """
    def __init__(self, size_average=True):
        super(SCSALightIOU, self).__init__()
        # SCSA模块特有的通道注意力权重
        self.channel_weight = 1.1
        self.size_average = size_average
        
    def forward(self, pred, target):
        # 确保pred和target在有效范围内
        pred = torch.clamp(pred, min=0.0, max=1.0)
        target = torch.clamp(target, min=0.0, max=1.0)

        # 检查NaN或Inf值
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            print("警告: 在SCSA IOU损失中的预测中检测到NaN或Inf值")
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)

        if torch.isnan(target).any() or torch.isinf(target).any():
            print("警告: 在SCSA IOU损失中的目标中检测到NaN或Inf值")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)

        # 计算交集和并集
        intersection = torch.sum(target * pred, dim=(1, 2, 3))
        union = torch.sum(target, dim=(1, 2, 3)) + torch.sum(pred, dim=(1, 2, 3)) - intersection
        
        # 添加小的epsilon值以避免除以零
        union = torch.clamp(union, min=1e-7)
        
        # 计算IoU损失 (1 - IoU)
        iou = (intersection / union).mean()
        iou_loss = (1.0 - iou) * self.channel_weight
        
        # 确保loss非负
        iou_loss = torch.clamp(iou_loss, min=0.0)
        
        if self.size_average:
            return iou_loss.mean()
        else:
            return iou_loss


class SCSABoundaryAwareLoss(nn.Module):
    def __init__(self, alpha=0.5, beta=0.5):
        super(SCSABoundaryAwareLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
        self.eps = 1e-6

    def forward(self, pred, target, boundary_mask):
        # 确保所有输入在有效范围内
        pred = torch.clamp(pred, min=self.eps, max=1.0 - self.eps)
        target = torch.clamp(target, min=0.0, max=1.0)
        boundary_mask = torch.clamp(boundary_mask, min=0.0, max=1.0)

        # 检查NaN和Inf
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            print("警告: 在边界损失中的预测中检测到NaN或Inf值")
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0-self.eps, neginf=self.eps)
        
        if torch.isnan(target).any() or torch.isinf(target).any():
            print("警告: 在边界损失中的目标中检测到NaN或Inf值")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)

        if torch.isnan(boundary_mask).any() or torch.isinf(boundary_mask).any():
            print("警告: 在边界掩码中检测到NaN或Inf值")
            boundary_mask = torch.nan_to_num(boundary_mask, nan=0.5, posinf=1.0, neginf=0.0)

        # 计算边界区域的损失
        boundary_pred = pred * boundary_mask
        boundary_target = target * boundary_mask
        boundary_loss = F.binary_cross_entropy(boundary_pred, boundary_target, reduction='mean')

        # 计算非边界区域的损失
        non_boundary_mask = 1 - boundary_mask
        non_boundary_pred = pred * non_boundary_mask
        non_boundary_target = target * non_boundary_mask
        non_boundary_loss = F.binary_cross_entropy(non_boundary_pred, non_boundary_target, reduction='mean')

        # 组合损失
        total_loss = self.alpha * boundary_loss + self.beta * non_boundary_loss
        return total_loss


class SCSADynamicWeightLoss(nn.Module):
    def __init__(self, initial_weights=None):
        super(SCSADynamicWeightLoss, self).__init__()
        if initial_weights is None:
            initial_weights = [1.0, 1.0, 1.0]  # 初始权重
        self.weights = nn.Parameter(torch.tensor(initial_weights))
        self.softmax = nn.Softmax(dim=0)

    def forward(self, losses):
        # 确保losses是张量并且形状正确
        if not isinstance(losses, torch.Tensor):
            losses = torch.stack([torch.tensor(l, device=self.weights.device) for l in losses])
        elif losses.device != self.weights.device:
            losses = losses.to(self.weights.device)
            
        # 应用softmax确保权重和为1
        normalized_weights = self.softmax(self.weights)
        
        # 确保权重是一维张量
        normalized_weights = normalized_weights.view(-1)
        
        return normalized_weights


class SCSACompositeLoss(nn.Module):
    """
    综合损失函数，结合边缘感知Focal和IoU损失
    针对SCSA注意力机制进行优化，并为CRF预测添加权重
    """
    def __init__(self, bce_weight=0.1, iou_weight=0.7, edge_weight=0.2):
        super(SCSACompositeLoss, self).__init__()
        # 损失权重
        self.iou_weight = iou_weight  # IoU损失权重
        self.edge_weight = edge_weight  # 边界损失权重
        self.bce_weight = bce_weight  # BCE损失权重
        
        # 使用改进的IoU损失
        self.iou_loss = SCSALightIOU(size_average=True)
        # 使用改进的边界损失
        self.edge_loss = SCSABoundaryAwareLoss(alpha=0.5, beta=0.5)
        # 使用带权重的BCE损失
        self.bce_loss = SCSAEdgeAwareBCELoss(edge_weight=1.3, body_weight=0.7)
        
        # 动态权重调整器
        self.dynamic_weight = SCSADynamicWeightLoss(initial_weights=[0.7, 0.2, 0.1])
        
    def forward(self, preds, target):
        """
        Args:
            preds: tuple of (h_pred, l_pred, final_pred, refined_pred)
                  每个pred的shape为 [B, 1, H, W]，refined_pred的shape为 [B, 2, H, W]
            target: [B, 1, H, W]
        """
        h_pred, l_pred, final_pred, refined_pred = preds
        batch_size = target.size(0)
        
        # 确保输入在有效范围内
        h_pred = torch.clamp(h_pred, min=1e-7, max=1.0-1e-7)
        l_pred = torch.clamp(l_pred, min=1e-7, max=1.0-1e-7)
        final_pred = torch.clamp(final_pred, min=1e-7, max=1.0-1e-7)
        
        # 计算IoU损失
        iou_loss_h = self.iou_loss(h_pred, target)
        iou_loss_l = self.iou_loss(l_pred, target)
        iou_loss_final = self.iou_loss(final_pred, target)
        iou_loss_refined = self.iou_loss(refined_pred[:, 1:], target)
        
        # 计算边界损失
        edge_loss_h = self.edge_loss(h_pred, target, boundary_mask=torch.ones_like(h_pred))
        edge_loss_l = self.edge_loss(l_pred, target, boundary_mask=torch.ones_like(l_pred))
        edge_loss_final = self.edge_loss(final_pred, target, boundary_mask=torch.ones_like(final_pred))
        edge_loss_refined = self.edge_loss(refined_pred[:, 1:], target, boundary_mask=torch.ones_like(refined_pred[:, 1:]))
        
        # 计算BCE损失
        bce_loss_h = self.bce_loss(h_pred, target)
        bce_loss_l = self.bce_loss(l_pred, target)
        bce_loss_final = self.bce_loss(final_pred, target)
        bce_loss_refined = self.bce_loss(refined_pred[:, 1:], target)
        
        # 动态调整权重
        loss_components = torch.stack([
            iou_loss_final.mean(),
            edge_loss_final.mean(),
            bce_loss_final.mean()
        ])
        
        dynamic_weights = self.dynamic_weight(loss_components)
        
        # 计算总损失
        total_loss = (
            # IoU损失（权重提高）
            self.iou_weight * dynamic_weights[0].item() * (
                0.3 * iou_loss_h +
                0.3 * iou_loss_l +
                0.4 * iou_loss_final +
                1.0 * iou_loss_refined  # 增加refined预测的权重
            ) +
            # 边界损失（权重降低）
            self.edge_weight * dynamic_weights[1].item() * (
                0.3 * edge_loss_h +
                0.3 * edge_loss_l +
                0.4 * edge_loss_final +
                1.0 * edge_loss_refined
            ) +
            # BCE损失（权重降低）
            self.bce_weight * dynamic_weights[2].item() * (
                0.3 * bce_loss_h +
                0.3 * bce_loss_l +
                0.4 * bce_loss_final +
                1.0 * bce_loss_refined
            )
        )
        
        return total_loss 