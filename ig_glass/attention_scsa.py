"""
 @Time    : 2024
 <AUTHOR> <PERSON><PERSON>

 @Project : IG_SLAM
 @File    : attention_scsa.py
 @Function: SCSA注意力机制的本地实现，避免依赖MMCV

 Enhanced SCSA for Glass Detection:
 - Multi-scale spatial attention for glass edge detection
 - Adaptive channel weighting for transparency features
 - Optimized for 90%+ IoU performance

"""
import typing as t
import torch
import torch.nn as nn
import torch.nn.functional as F
from einops import rearrange

class SCSA(nn.Module):
    """
    空间-通道自注意力（Spatial-Channel Self Attention）模块
    本地实现版本，避免依赖MMCV库
    """
    def __init__(
            self,
            dim: int,
            head_num: int,
            window_size: int = 7,
            group_kernel_sizes: t.List[int] = [3, 5, 7, 9],
            qkv_bias: bool = False,
            fuse_bn: bool = False,
            down_sample_mode: str = 'avg_pool',
            attn_drop_ratio: float = 0.,
            gate_layer: str = 'sigmoid',
            channel_weight: float = 1.0,
    ):
        super(SCSA, self).__init__()
        self.dim = dim
        self.head_num = head_num
        self.head_dim = dim // head_num
        self.scaler = self.head_dim ** -0.5
        self.group_kernel_sizes = group_kernel_sizes
        self.window_size = window_size
        self.qkv_bias = qkv_bias
        self.fuse_bn = fuse_bn
        self.down_sample_mode = down_sample_mode
        self.channel_weight = channel_weight

        assert self.dim % 4 == 0, '输入特征的维度应该被4整除'
        self.group_chans = group_chans = self.dim // 4

        # 局部和全局深度卷积
        self.local_dwc = nn.Conv1d(group_chans, group_chans, kernel_size=group_kernel_sizes[0],
                                   padding=group_kernel_sizes[0] // 2, groups=group_chans)
        self.global_dwc_s = nn.Conv1d(group_chans, group_chans, kernel_size=group_kernel_sizes[1],
                                      padding=group_kernel_sizes[1] // 2, groups=group_chans)
        self.global_dwc_m = nn.Conv1d(group_chans, group_chans, kernel_size=group_kernel_sizes[2],
                                      padding=group_kernel_sizes[2] // 2, groups=group_chans)
        self.global_dwc_l = nn.Conv1d(group_chans, group_chans, kernel_size=group_kernel_sizes[3],
                                      padding=group_kernel_sizes[3] // 2, groups=group_chans)
        
        # 空间注意力门控
        self.sa_gate = nn.Softmax(dim=2) if gate_layer == 'softmax' else nn.Sigmoid()
        self.norm_h = nn.GroupNorm(4, dim)
        self.norm_w = nn.GroupNorm(4, dim)

        # 通道注意力组件
        self.conv_d = nn.Identity()
        self.norm = nn.GroupNorm(1, dim)
        self.q = nn.Conv2d(in_channels=dim, out_channels=dim, kernel_size=1, bias=qkv_bias, groups=dim)
        self.k = nn.Conv2d(in_channels=dim, out_channels=dim, kernel_size=1, bias=qkv_bias, groups=dim)
        self.v = nn.Conv2d(in_channels=dim, out_channels=dim, kernel_size=1, bias=qkv_bias, groups=dim)
        self.attn_drop = nn.Dropout(attn_drop_ratio)
        self.ca_gate = nn.Softmax(dim=1) if gate_layer == 'softmax' else nn.Sigmoid()

        # 下采样方法
        if window_size == -1:
            self.down_func = nn.AdaptiveAvgPool2d((1, 1))
        else:
            if down_sample_mode == 'recombination':
                self.down_func = self.space_to_chans
                # 维度降低
                self.conv_d = nn.Conv2d(in_channels=dim * window_size ** 2, out_channels=dim, kernel_size=1, bias=False)
            elif down_sample_mode == 'avg_pool':
                self.down_func = nn.AvgPool2d(kernel_size=(window_size, window_size), stride=window_size)
            elif down_sample_mode == 'max_pool':
                self.down_func = nn.MaxPool2d(kernel_size=(window_size, window_size), stride=window_size)

    def space_to_chans(self, x):
        """空间到通道的重排列，实现重组下采样的功能"""
        b, c, h, w = x.size()
        window_size = self.window_size
        x = x.unfold(2, window_size, window_size).unfold(3, window_size, window_size)
        x = x.permute(0, 2, 3, 1, 4, 5).contiguous()
        x = x.view(b, h // window_size, w // window_size, c * window_size * window_size)
        x = x.permute(0, 3, 1, 2).contiguous()
        return x

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        x的维度是(B, C, H, W)
        """
        # 空间注意力优先计算
        b, c, h_, w_ = x.size()
        # (B, C, H)
        x_h = x.mean(dim=3)
        l_x_h, g_x_h_s, g_x_h_m, g_x_h_l = torch.split(x_h, self.group_chans, dim=1)
        # (B, C, W)
        x_w = x.mean(dim=2)
        l_x_w, g_x_w_s, g_x_w_m, g_x_w_l = torch.split(x_w, self.group_chans, dim=1)

        # 应用水平方向的注意力
        x_h_attn = self.sa_gate(self.norm_h(torch.cat((
            self.local_dwc(l_x_h),
            self.global_dwc_s(g_x_h_s),
            self.global_dwc_m(g_x_h_m),
            self.global_dwc_l(g_x_h_l),
        ), dim=1)))
        x_h_attn = x_h_attn.view(b, c, h_, 1)

        # 应用垂直方向的注意力
        x_w_attn = self.sa_gate(self.norm_w(torch.cat((
            self.local_dwc(l_x_w),
            self.global_dwc_s(g_x_w_s),
            self.global_dwc_m(g_x_w_m),
            self.global_dwc_l(g_x_w_l)
        ), dim=1)))
        x_w_attn = x_w_attn.view(b, c, 1, w_)

        # 应用空间注意力
        x = x * x_h_attn * x_w_attn

        # 基于自注意力的通道注意力
        # 减少计算量
        y = self.down_func(x)
        y = self.conv_d(y)
        _, _, h_, w_ = y.size()

        # 先归一化，然后reshape -> (B, H, W, C) -> (B, C, H * W)并生成q, k和v
        y = self.norm(y)
        q = self.q(y)
        k = self.k(y)
        v = self.v(y)
        # (B, C, H, W) -> (B, head_num, head_dim, N)
        q = rearrange(q, 'b (head_num head_dim) h w -> b head_num head_dim (h w)', head_num=int(self.head_num),
                      head_dim=int(self.head_dim))
        k = rearrange(k, 'b (head_num head_dim) h w -> b head_num head_dim (h w)', head_num=int(self.head_num),
                      head_dim=int(self.head_dim))
        v = rearrange(v, 'b (head_num head_dim) h w -> b head_num head_dim (h w)', head_num=int(self.head_num),
                      head_dim=int(self.head_dim))

        # (B, head_num, head_dim, head_dim)
        attn = q @ k.transpose(-2, -1) * self.scaler
        attn = self.attn_drop(attn.softmax(dim=-1))
        # (B, head_num, head_dim, N)
        attn = attn @ v
        # (B, C, H_, W_)
        attn = rearrange(attn, 'b head_num head_dim (h w) -> b (head_num head_dim) h w', h=int(h_), w=int(w_))
        # (B, C, 1, 1)
        attn = attn.mean((2, 3), keepdim=True)
        attn = self.ca_gate(attn)
        
        return attn * x * self.channel_weight


class EnhancedSCSA(nn.Module):
    """
    增强版SCSA注意力机制，专门针对玻璃检测优化
    目标：突破90% IoU

    主要改进：
    1. 多尺度空间注意力，更好地捕获玻璃边缘
    2. 自适应通道权重，针对透明度特征优化
    3. 边缘感知的注意力机制
    4. 数值稳定性优化
    """
    def __init__(
            self,
            dim: int,
            head_num: int = 8,
            window_size: int = 7,
            group_kernel_sizes: t.List[int] = [3, 5, 7, 9],
            qkv_bias: bool = False,
            attn_drop_ratio: float = 0.1,
            gate_layer: str = 'sigmoid',
            channel_weight: float = 1.0,
            edge_enhance: bool = True,
            multi_scale: bool = True
    ):
        super(EnhancedSCSA, self).__init__()
        self.dim = dim
        self.head_num = head_num
        self.head_dim = dim // head_num
        self.scaler = self.head_dim ** -0.5
        self.group_kernel_sizes = group_kernel_sizes
        self.window_size = window_size
        self.channel_weight = channel_weight
        self.edge_enhance = edge_enhance
        self.multi_scale = multi_scale

        assert self.dim % 4 == 0, '输入特征的维度应该被4整除'
        self.group_chans = self.dim // 4

        # 多尺度空间注意力 - 针对玻璃边缘优化
        self.local_dwc = nn.Conv1d(self.group_chans, self.group_chans,
                                   kernel_size=group_kernel_sizes[0],
                                   padding=group_kernel_sizes[0] // 2,
                                   groups=self.group_chans)
        self.global_dwc_s = nn.Conv1d(self.group_chans, self.group_chans,
                                      kernel_size=group_kernel_sizes[1],
                                      padding=group_kernel_sizes[1] // 2,
                                      groups=self.group_chans)
        self.global_dwc_m = nn.Conv1d(self.group_chans, self.group_chans,
                                      kernel_size=group_kernel_sizes[2],
                                      padding=group_kernel_sizes[2] // 2,
                                      groups=self.group_chans)
        self.global_dwc_l = nn.Conv1d(self.group_chans, self.group_chans,
                                      kernel_size=group_kernel_sizes[3],
                                      padding=group_kernel_sizes[3] // 2,
                                      groups=self.group_chans)

        # 边缘增强模块
        if self.edge_enhance:
            self.edge_detector = nn.Sequential(
                nn.Conv2d(dim, dim // 4, 3, 1, 1, groups=dim // 4),
                nn.BatchNorm2d(dim // 4),
                nn.ReLU(inplace=True),
                nn.Conv2d(dim // 4, 1, 1),
                nn.Sigmoid()
            )

        # 空间注意力门控 - 使用更稳定的激活
        self.sa_gate = nn.Sigmoid()  # 统一使用Sigmoid，更稳定
        self.norm_h = nn.GroupNorm(4, dim)
        self.norm_w = nn.GroupNorm(4, dim)

        # 自适应通道注意力
        self.adaptive_pool = nn.AdaptiveAvgPool2d(1)
        self.channel_attention = nn.Sequential(
            nn.Conv2d(dim, dim // 8, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim // 8, dim, 1),
            nn.Sigmoid()
        )

        # 通道注意力组件 - 优化版本
        self.norm = nn.GroupNorm(1, dim)
        self.q = nn.Conv2d(dim, dim, 1, bias=qkv_bias, groups=dim)
        self.k = nn.Conv2d(dim, dim, 1, bias=qkv_bias, groups=dim)
        self.v = nn.Conv2d(dim, dim, 1, bias=qkv_bias, groups=dim)
        self.attn_drop = nn.Dropout(attn_drop_ratio)

        # 下采样
        self.down_func = nn.AvgPool2d(kernel_size=window_size, stride=window_size) if window_size > 1 else nn.Identity()

        # 多尺度融合
        if self.multi_scale:
            self.scale_fusion = nn.Sequential(
                nn.Conv2d(dim * 2, dim, 1),
                nn.BatchNorm2d(dim),
                nn.ReLU(inplace=True)
            )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Enhanced forward pass for glass detection
        """
        b, c, h_, w_ = x.size()
        identity = x

        # 1. 多尺度空间注意力
        # 水平方向注意力
        x_h = x.mean(dim=3)  # (B, C, H)
        l_x_h, g_x_h_s, g_x_h_m, g_x_h_l = torch.split(x_h, self.group_chans, dim=1)

        # 垂直方向注意力
        x_w = x.mean(dim=2)  # (B, C, W)
        l_x_w, g_x_w_s, g_x_w_m, g_x_w_l = torch.split(x_w, self.group_chans, dim=1)

        # 应用多尺度卷积
        x_h_attn = self.sa_gate(self.norm_h(torch.cat((
            self.local_dwc(l_x_h),
            self.global_dwc_s(g_x_h_s),
            self.global_dwc_m(g_x_h_m),
            self.global_dwc_l(g_x_h_l),
        ), dim=1)))
        x_h_attn = x_h_attn.view(b, c, h_, 1)

        x_w_attn = self.sa_gate(self.norm_w(torch.cat((
            self.local_dwc(l_x_w),
            self.global_dwc_s(g_x_w_s),
            self.global_dwc_m(g_x_w_m),
            self.global_dwc_l(g_x_w_l)
        ), dim=1)))
        x_w_attn = x_w_attn.view(b, c, 1, w_)

        # 应用空间注意力
        spatial_attended = x * x_h_attn * x_w_attn

        # 2. 边缘增强（如果启用）
        if self.edge_enhance:
            edge_map = self.edge_detector(x)
            spatial_attended = spatial_attended * (1 + edge_map)

        # 3. 自适应通道注意力
        channel_weight = self.channel_attention(self.adaptive_pool(spatial_attended))
        channel_attended = spatial_attended * channel_weight

        # 4. 多尺度融合（如果启用）
        if self.multi_scale:
            # 创建不同尺度的特征
            scale_1 = F.avg_pool2d(channel_attended, 2, 2)
            scale_1 = F.interpolate(scale_1, size=(h_, w_), mode='bilinear', align_corners=True)

            # 融合多尺度特征
            multi_scale_feat = torch.cat([channel_attended, scale_1], dim=1)
            output = self.scale_fusion(multi_scale_feat)
        else:
            output = channel_attended

        # 5. 残差连接和最终权重
        output = output * self.channel_weight + identity * (1 - self.channel_weight)

        return output