import cv2
import numpy as np
import os

# 计算图像亮度（使用HSV色彩空间的亮度通道）
def calculate_brightness(image):
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])  # V通道代表亮度
    return brightness

# 处理图像，提取ORB特征点，并标记亮度和特征点数
def process_image(image_path, output_folder):
    # 读取输入图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return

    # 计算图像亮度
    brightness = calculate_brightness(image)

    # 使用 ORB 检测特征点
    orb = cv2.ORB_create(nfeatures=15000)  # 
    keypoints, descriptors = orb.detectAndCompute(image, None)

    # 在图像上绘制特征点
    image_with_keypoints = cv2.drawKeypoints(image, keypoints, None, color=(0, 255, 0), flags=0)

    # 显示亮度和特征点数
    cv2.putText(image_with_keypoints, f'Brightness: {brightness:.2f}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(image_with_keypoints, f'Feature Points: {len(keypoints)}', (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

    # 保存结果图像
    output_path = os.path.join(output_folder, os.path.basename(image_path))
    cv2.imwrite(output_path, image_with_keypoints)
    print(f"处理完成: {output_path}")

# 主函数，用于处理输入文件夹中的所有图像
def process_image_folder(input_folder, output_folder):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # 读取输入文件夹中的所有图像文件
    image_files = [f for f in os.listdir(input_folder) if f.endswith(('.png', '.jpg', '.jpeg'))]

    for image_file in image_files:
        image_path = os.path.join(input_folder, image_file)
        process_image(image_path, output_folder)

if __name__ == "__main__":
    # 在这里填写你的输入和输出文件夹路径
    input_folder = "/home/<USER>/orb_slam3_ws/src/SLAM/Task/jpg"  # 替换为你的输入文件夹路径
    output_folder = "/home/<USER>/orb_slam3_ws/src/SLAM/Task/origin/outfull"  # 替换为你的输出文件夹路径

    # 开始处理图像
    process_image_folder(input_folder, output_folder)
