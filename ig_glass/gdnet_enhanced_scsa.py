"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_enhanced_scsa.py
 @Function: Enhanced GDNet with optimized SCSA for 90%+ IoU performance

基于87.95% IoU的SCSA成功经验，进一步优化：
1. 增强的SCSA注意力机制
2. 优化的特征融合策略
3. 改进的损失函数权重
4. 数值稳定性优化

目标：突破90% IoU
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF
from ig_glass.attention_scsa import EnhancedSCSA


class LightLCFI(nn.Module):
    """轻量化的LCFI模块，针对玻璃检测优化"""
    def __init__(self, in_channels, r1, r2, r3, r4):
        super(LightLCFI, self).__init__()
        
        # 减少通道数以提高效率
        mid_channels = max(in_channels // 8, 32)
        
        # 多尺度卷积分支
        self.branch1 = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, 1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, mid_channels, 3, 1, r1, dilation=r1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True)
        )
        
        self.branch2 = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, 1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, mid_channels, 3, 1, r2, dilation=r2),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True)
        )
        
        self.branch3 = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, 1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, mid_channels, 3, 1, r3, dilation=r3),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True)
        )
        
        self.branch4 = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, 1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, mid_channels, 3, 1, r4, dilation=r4),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True)
        )
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Conv2d(mid_channels * 4, in_channels // 2, 1),
            nn.BatchNorm2d(in_channels // 2),
            nn.ReLU(inplace=True)
        )
        
        # 残差连接
        self.residual = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 2, 1),
            nn.BatchNorm2d(in_channels // 2)
        )

    def forward(self, x):
        # 多尺度特征提取
        b1 = self.branch1(x)
        b2 = self.branch2(x)
        b3 = self.branch3(x)
        b4 = self.branch4(x)
        
        # 特征融合
        fused = torch.cat([b1, b2, b3, b4], dim=1)
        fused = self.fusion(fused)
        
        # 残差连接
        residual = self.residual(x)
        
        return F.relu(fused + residual)


class GDNetEnhancedSCSA(nn.Module):
    """
    Enhanced GDNet with optimized SCSA attention
    目标：突破90% IoU性能
    """
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetEnhancedSCSA, self).__init__()
        
        # 骨干网络
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0
        self.layer1 = resnext.layer1
        self.layer2 = resnext.layer2
        self.layer3 = resnext.layer3
        self.layer4 = resnext.layer4

        # 轻量化LCFI模块
        self.h5_conv = LightLCFI(2048, 1, 2, 3, 4)  # 输出1024维
        self.h4_conv = LightLCFI(1024, 1, 2, 3, 4)  # 输出512维
        self.h3_conv = LightLCFI(512, 1, 2, 3, 4)   # 输出256维
        self.l2_conv = LightLCFI(256, 1, 2, 3, 4)   # 输出128维

        # 多尺度特征融合
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        
        # 计算融合后的总维度：1024 + 512 + 256 = 1792
        fusion_dim = 1792
        
        # 使用增强版SCSA注意力
        self.h_fusion = EnhancedSCSA(
            dim=fusion_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            attn_drop_ratio=0.1,
            channel_weight=1.2,  # 增加通道权重
            edge_enhance=True,   # 启用边缘增强
            multi_scale=True     # 启用多尺度
        )
        
        # 特征融合后的卷积
        self.h_fusion_conv = nn.Sequential(
            nn.Conv2d(fusion_dim, 896, 3, 1, 1),
            nn.BatchNorm2d(896),
            nn.ReLU(inplace=True),
            nn.Conv2d(896, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )

        # 低层特征处理
        self.l2_fusion = nn.Sequential(
            nn.Conv2d(128, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )

        # 最终预测头
        self.predict_h = nn.Conv2d(512, 1, 3, 1, 1)
        self.predict_l = nn.Conv2d(64, 1, 3, 1, 1)

        # CRF后处理 - 优化参数
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,    # 增加双边权重
            gaussian_weight=5.0,      # 增加高斯权重
            bilateral_spatial_sigma=40.0,  # 优化空间sigma
            bilateral_color_sigma=3.0,     # 优化颜色sigma
            gaussian_sigma=1.5,            # 优化高斯sigma
            trainable=trainable_crf
        )

        # 自适应权重融合
        self.adaptive_weights = nn.Parameter(torch.tensor([0.7, 0.3]), requires_grad=True)
        
        # 边缘增强模块
        self.edge_enhance = nn.Sequential(
            nn.Conv2d(1, 16, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 3, 1, 1),
            nn.Sigmoid()
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # 提取多层特征
        layer0 = self.layer0(x)      # [B, 64, H/2, W/2]
        layer1 = self.layer1(layer0)  # [B, 256, H/4, W/4]
        layer2 = self.layer2(layer1)  # [B, 512, H/8, W/8]
        layer3 = self.layer3(layer2)  # [B, 1024, H/16, W/16]
        layer4 = self.layer4(layer3)  # [B, 2048, H/32, W/32]

        # LCFI特征提取
        h5_conv = self.h5_conv(layer4)  # [B, 1024, H/32, W/32]
        h4_conv = self.h4_conv(layer3)  # [B, 512, H/16, W/16]
        h3_conv = self.h3_conv(layer2)  # [B, 256, H/8, W/8]
        l2_conv = self.l2_conv(layer1)  # [B, 128, H/4, W/4]

        # 高层特征融合
        h5_up = self.h5_up(h5_conv)     # [B, 1024, H/16, W/16]
        h3_down = self.h3_down(h3_conv) # [B, 256, H/16, W/16]
        
        # 融合高层特征
        h_fused = torch.cat([h5_up, h4_conv, h3_down], dim=1)  # [B, 1792, H/16, W/16]
        
        # 应用增强SCSA注意力
        h_attended = self.h_fusion(h_fused)
        h_final = self.h_fusion_conv(h_attended)  # [B, 512, H/16, W/16]

        # 低层特征处理
        l_final = self.l2_fusion(l2_conv)  # [B, 64, H/4, W/4]

        # 生成预测
        pred_h = self.predict_h(h_final)  # [B, 1, H/16, W/16]
        pred_l = self.predict_l(l_final)  # [B, 1, H/4, W/4]

        # 上采样到原始尺寸
        pred_h = F.interpolate(pred_h, size=x.size()[2:], mode='bilinear', align_corners=True)
        pred_l = F.interpolate(pred_l, size=x.size()[2:], mode='bilinear', align_corners=True)

        # 应用sigmoid
        pred_h_prob = torch.sigmoid(pred_h)
        pred_l_prob = torch.sigmoid(pred_l)

        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = weights[0] * pred_h_prob + weights[1] * pred_l_prob
        
        # 边缘增强
        edge_enhanced = self.edge_enhance(ensemble_pred)
        final_pred = ensemble_pred * (1 + edge_enhanced)
        final_pred = torch.clamp(final_pred, min=1e-7, max=1.0 - 1e-7)

        # CRF后处理
        bg_logits = torch.log(1 - final_pred + 1e-7)
        fg_logits = torch.log(final_pred + 1e-7)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # 归一化输入图像
        normalized_img = self._normalize_image(x)

        # 应用CRF
        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - final_pred, final_pred], dim=1)

        return {
            'pred_h': pred_h_prob,
            'pred_l': pred_l_prob,
            'ensemble_pred': final_pred,
            'refined_pred': refined_predict
        }

    def _normalize_image(self, x):
        """图像归一化"""
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        return normalized_img


def create_enhanced_scsa_model(backbone_path=None, crf_iter=5, trainable_crf=True):
    """创建增强版SCSA模型"""
    return GDNetEnhancedSCSA(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )
