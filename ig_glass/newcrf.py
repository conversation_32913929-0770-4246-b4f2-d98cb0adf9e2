"""
 @Project : IG_SLAM
 @File    : newcrf.py
 @Function: 新型玻璃检测网络，融合GDNet、GDNetLightCRF和EBLNet的优点
"""
import torch
import torch.nn as nn
import torch.nn.functional as F

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF


###################################################################
# ########################## CBAM #################################
###################################################################
class BasicConv(nn.Module):
    def __init__(self, in_planes, out_planes, kernel_size, stride=1, padding=0, dilation=1, groups=1, relu=True,
                 bn=True, bias=False):
        super(BasicConv, self).__init__()
        self.out_channels = out_planes
        self.conv = nn.Conv2d(in_planes, out_planes, kernel_size=kernel_size, stride=stride, padding=padding,
                              dilation=dilation, groups=groups, bias=bias)
        self.bn = nn.BatchNorm2d(out_planes, eps=1e-5, momentum=0.01, affine=True) if bn else None
        self.relu = nn.ReLU() if relu else None

    def forward(self, x):
        x = self.conv(x)
        if self.bn is not None:
            x = self.bn(x)
        if self.relu is not None:
            x = self.relu(x)
        return x


class Flatten(nn.Module):
    def forward(self, x):
        return x.view(x.size(0), -1)


class ChannelGate(nn.Module):
    def __init__(self, gate_channels, reduction_ratio=16, pool_types=['avg', 'max']):
        super(ChannelGate, self).__init__()
        self.gate_channels = gate_channels
        self.mlp = nn.Sequential(
            Flatten(),
            nn.Linear(gate_channels, gate_channels // reduction_ratio),
            nn.ReLU(),
            nn.Linear(gate_channels // reduction_ratio, gate_channels)
        )
        self.pool_types = pool_types

    def forward(self, x):
        channel_att_sum = None
        for pool_type in self.pool_types:
            if pool_type == 'avg':
                avg_pool = F.avg_pool2d(x, (x.size(2), x.size(3)), stride=(x.size(2), x.size(3)))
                channel_att_raw = self.mlp(avg_pool)
            elif pool_type == 'max':
                max_pool = F.max_pool2d(x, (x.size(2), x.size(3)), stride=(x.size(2), x.size(3)))
                channel_att_raw = self.mlp(max_pool)

            if channel_att_sum is None:
                channel_att_sum = channel_att_raw
            else:
                channel_att_sum = channel_att_sum + channel_att_raw

        scale = F.sigmoid(channel_att_sum).unsqueeze(2).unsqueeze(3).expand_as(x)
        return x * scale


class ChannelPool(nn.Module):
    def forward(self, x):
        return torch.cat((torch.max(x, 1)[0].unsqueeze(1), torch.mean(x, 1).unsqueeze(1)), dim=1)


class SpatialGate(nn.Module):
    def __init__(self):
        super(SpatialGate, self).__init__()
        kernel_size = 7
        self.compress = ChannelPool()
        self.spatial = BasicConv(2, 1, kernel_size, stride=1, padding=(kernel_size - 1) // 2, relu=False)

    def forward(self, x):
        x_compress = self.compress(x)
        x_out = self.spatial(x_compress)
        scale = F.sigmoid(x_out)
        return x * scale


class CBAM(nn.Module):
    def __init__(self, gate_channels, reduction_ratio=16, pool_types=['avg', 'max'], no_spatial=False):
        super(CBAM, self).__init__()
        self.ChannelGate = ChannelGate(gate_channels, reduction_ratio, pool_types)
        self.no_spatial = no_spatial
        if not no_spatial:
            self.SpatialGate = SpatialGate()

    def forward(self, x):
        x_out = self.ChannelGate(x)
        if not self.no_spatial:
            x_out = self.SpatialGate(x_out)
        return x_out


###################################################################
# ########################## ASPP #################################
###################################################################
class _AtrousSpatialPyramidPoolingModule(nn.Module):
    def __init__(self, in_dim, reduction_dim=256, output_stride=8, rates=(6, 12, 18)):
        super(_AtrousSpatialPyramidPoolingModule, self).__init__()

        if output_stride == 8:
            rates = [2 * r for r in rates]
        elif output_stride == 16:
            pass
        else:
            raise ValueError('Unsupported output stride')

        self.features = []
        # 1x1 convolution
        self.features.append(nn.Sequential(
            nn.Conv2d(in_dim, reduction_dim, kernel_size=1, bias=False),
            nn.BatchNorm2d(reduction_dim),
            nn.ReLU(inplace=True)
        ))
        # Dilated convolutions
        for r in rates:
            self.features.append(nn.Sequential(
                nn.Conv2d(in_dim, reduction_dim, kernel_size=3, dilation=r, padding=r, bias=False),
                nn.BatchNorm2d(reduction_dim),
                nn.ReLU(inplace=True)
            ))
        # Global average pooling
        self.features.append(nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_dim, reduction_dim, kernel_size=1, bias=False),
            nn.BatchNorm2d(reduction_dim),
            nn.ReLU(inplace=True)
        ))
        
        self.features = nn.ModuleList(self.features)
        
        self.project = nn.Sequential(
            nn.Conv2d(reduction_dim * len(self.features), reduction_dim, kernel_size=1, bias=False),
            nn.BatchNorm2d(reduction_dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        x_size = x.size()
        
        out = []
        for f in self.features:
            y = f(x)
            if y.size(2) != x_size[2]:
                y = F.interpolate(y, size=(x_size[2], x_size[3]), mode='bilinear', align_corners=True)
            out.append(y)
            
        out = torch.cat(out, 1)
        out = self.project(out)
        return out


###################################################################
# ######################## LightLCFI ##############################
###################################################################
class LightLCFI(nn.Module):
    """Lightweight Large Context Feature Integration module with depthwise separable convolutions"""
    def __init__(self, input_channels, dr1=1, dr2=2, dr3=3, dr4=4):
        super(LightLCFI, self).__init__()
        self.input_channels = input_channels
        self.channels_single = int(input_channels / 4)
        self.channels_double = int(input_channels / 2)
        self.dr1, self.dr2, self.dr3, self.dr4 = dr1, dr2, dr3, dr4
        self.padding1, self.padding2, self.padding3, self.padding4 = dr1, 2*dr2, 3*dr3, 4*dr4

        # Channel reduction with 1x1 convolutions
        self.p1_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p1
        self.p1_d1 = nn.Sequential(
            nn.Conv2d(self.channels_single, self.channels_single, (3, 1), 1, padding=(self.padding1, 0),
                      dilation=(self.dr1, 1), groups=self.channels_single),
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_d2 = nn.Sequential(
            nn.Conv2d(self.channels_single, self.channels_single, (1, 3), 1, padding=(0, self.padding1),
                      dilation=(1, self.dr1), groups=self.channels_single),
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p2-p4 (结构类似p1，故简化代码)
        # p2
        self.p2_d1 = self._create_dw_sep_conv(self.channels_double, self.channels_single, 5, self.dr2)
        self.p2_d2 = self._create_dw_sep_conv(self.channels_double, self.channels_single, 5, self.dr2, horizontal=False)
        self.p2_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
            
        # p3
        self.p3_d1 = self._create_dw_sep_conv(self.channels_double, self.channels_single, 7, self.dr3)
        self.p3_d2 = self._create_dw_sep_conv(self.channels_double, self.channels_single, 7, self.dr3, horizontal=False)
        self.p3_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
            
        # p4
        self.p4_d1 = self._create_dw_sep_conv(self.channels_double, self.channels_single, 9, self.dr4)
        self.p4_d2 = self._create_dw_sep_conv(self.channels_double, self.channels_single, 9, self.dr4, horizontal=False)
        self.p4_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Efficient attention mechanism
        self.cbam = CBAM(self.input_channels)

        # Final channel reduction
        self.channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single),
            nn.ReLU())
            
    def _create_dw_sep_conv(self, in_ch, out_ch, kernel_size, dilation, horizontal=True):
        """创建深度可分离卷积块，水平或垂直方向"""
        if horizontal:
            return nn.Sequential(
                nn.Conv2d(in_ch, in_ch, (kernel_size, 1), 1, padding=(dilation*(kernel_size//2), 0),
                          dilation=(dilation, 1), groups=in_ch),
                nn.Conv2d(in_ch, out_ch, 1, 1, 0),
                nn.BatchNorm2d(out_ch), nn.ReLU())
        else:
            return nn.Sequential(
                nn.Conv2d(in_ch, in_ch, (1, kernel_size), 1, padding=(0, dilation*(kernel_size//2)),
                          dilation=(1, dilation), groups=in_ch),
                nn.Conv2d(in_ch, out_ch, 1, 1, 0),
                nn.BatchNorm2d(out_ch), nn.ReLU())

    def forward(self, x):
        p1_input = self.p1_channel_reduction(x)
        p1 = self.p1_fusion(torch.cat((self.p1_d1(p1_input), self.p1_d2(p1_input)), 1))

        p2_input = torch.cat((self.p2_channel_reduction(x), p1), 1)
        p2 = self.p2_fusion(torch.cat((self.p2_d1(p2_input), self.p2_d2(p2_input)), 1))

        p3_input = torch.cat((self.p3_channel_reduction(x), p2), 1)
        p3 = self.p3_fusion(torch.cat((self.p3_d1(p3_input), self.p3_d2(p3_input)), 1))

        p4_input = torch.cat((self.p4_channel_reduction(x), p3), 1)
        p4 = self.p4_fusion(torch.cat((self.p4_d1(p4_input), self.p4_d2(p4_input)), 1))

        # 融合多尺度特征并应用注意力
        features = torch.cat((p1, p2, p3, p4), 1)
        attended_features = self.cbam(features)
        channel_reduction = self.channel_reduction(attended_features)

        return channel_reduction


###################################################################
# ###################### Edge Enhancer ############################
###################################################################
class EdgeEnhancer(nn.Module):
    """Edge enhancement module combining features from different levels"""
    def __init__(self, in_channels):
        super(EdgeEnhancer, self).__init__()
        
        # 边缘特征提取
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
        # 拉普拉斯边缘检测核
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))
        
        # 边缘特征增强
        self.edge_enhance = nn.Sequential(
            nn.Conv2d(in_channels+1, in_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        # 提取拉普拉斯边缘
        edge_map = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_map = torch.sigmoid(edge_map)
        
        # 边缘特征提取
        edge_features = self.edge_conv(x)
        
        # 融合边缘信息
        enhanced = self.edge_enhance(torch.cat([edge_features, edge_map], dim=1))
        
        return enhanced


###################################################################
# ########################## NewCRF ###############################
###################################################################
class NewCRF(nn.Module):
    """融合GDNet、GDNetLightCRF和EBLNet优点的新型玻璃检测网络"""
    def __init__(self, backbone_path=None, num_classes=1, crf_iter=3, trainable_crf=True):
        super(NewCRF, self).__init__()
        
        # 基础骨干网络
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0
        self.layer1 = resnext.layer1
        self.layer2 = resnext.layer2
        self.layer3 = resnext.layer3
        self.layer4 = resnext.layer4
        
        # ASPP模块用于多尺度特征提取
        self.aspp = _AtrousSpatialPyramidPoolingModule(in_dim=2048, reduction_dim=256, output_stride=8)
        
        # 轻量化LCFI模块
        self.h5_conv = LightLCFI(2048, 1, 2, 3, 4)
        self.h4_conv = LightLCFI(1024, 1, 2, 3, 4)
        self.h3_conv = LightLCFI(512, 1, 2, 3, 4)
        self.l2_conv = LightLCFI(256, 1, 2, 3, 4)
        
        # 边缘增强模块
        self.edge_enhancer = EdgeEnhancer(896)
        
        # 从EBLNet借鉴的边缘提取器
        self.edge_extractor = nn.Sequential(
            nn.Conv2d(896+64, 256, kernel_size=3, padding=1), 
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        
        # h特征融合
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        self.h_fusion = CBAM(896)
        self.h_fusion_conv = nn.Sequential(
            nn.Conv2d(896, 896, 3, 1, 1, groups=896),
            nn.Conv2d(896, 896, 1, 1, 0),
            nn.BatchNorm2d(896), nn.ReLU())
            
        # l特征融合
        self.l_fusion_conv = nn.Sequential(
            nn.Conv2d(64, 64, 3, 1, 1, groups=64),
            nn.Conv2d(64, 64, 1, 1, 0),
            nn.BatchNorm2d(64), nn.ReLU())
        self.h2l = nn.ConvTranspose2d(896, 1, 8, 4, 2)
        
        # 最终特征融合
        self.h_up_for_final_fusion = nn.ConvTranspose2d(896, 256, 8, 4, 2)
        self.final_fusion = CBAM(320)
        self.final_fusion_conv = nn.Sequential(
            nn.Conv2d(320, 320, 3, 1, 1, groups=320),
            nn.Conv2d(320, 320, 1, 1, 0),
            nn.BatchNorm2d(320), nn.ReLU())
            
        # 预测器
        self.edge_predict = nn.Conv2d(256, 1, 3, 1, 1)  # 边缘预测
        self.body_predict = nn.Conv2d(896, 1, 3, 1, 1)  # 主体预测
        self.final_predict = nn.Conv2d(320, num_classes, 3, 1, 1)  # 最终预测
        
        # CRF后处理
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,
            gaussian_weight=5.0,
            bilateral_spatial_sigma=40.0,
            bilateral_color_sigma=3.0,
            gaussian_sigma=1.5,
            trainable=trainable_crf
        )
        
        # 类别平衡权重
        self.class_weights = nn.Parameter(torch.tensor([1.0, 1.5]), requires_grad=True)
        
        # 确保所有ReLU为inplace操作以节省内存
        for m in self.modules():
            if isinstance(m, nn.ReLU):
                m.inplace = True
                
    def _extract_body_and_edge(self, h_feat, l_feat):
        """提取主体和边缘特征"""
        combined = torch.cat([
            F.interpolate(h_feat, size=l_feat.size()[2:], mode='bilinear', align_corners=True), 
            l_feat
        ], dim=1)
        edge_feat = self.edge_extractor(combined)
        body_feat = F.interpolate(h_feat, l_feat.size()[2:], mode='bilinear', align_corners=True) - edge_feat
        return edge_feat, body_feat
        
    def forward(self, x):
        # 骨干网络特征提取
        x_size = x.size()
        layer0 = self.layer0(x)  # [-1, 64, h/2, w/2]
        layer1 = self.layer1(layer0)  # [-1, 256, h/4, w/4]
        layer2 = self.layer2(layer1)  # [-1, 512, h/8, w/8]
        layer3 = self.layer3(layer2)  # [-1, 1024, h/16, w/16]
        layer4 = self.layer4(layer3)  # [-1, 2048, h/32, w/32]
        
        # ASPP多尺度特征提取
        aspp_feat = self.aspp(layer4)
        
        # 使用LightLCFI处理各层特征
        h5_conv = self.h5_conv(layer4)
        h4_conv = self.h4_conv(layer3)
        h3_conv = self.h3_conv(layer2)
        l2_conv = self.l2_conv(layer1)
        
        # H特征融合
        h5_up = self.h5_up(h5_conv)
        h3_down = self.h3_down(h3_conv)
        h_fusion = self.h_fusion(torch.cat((h5_up, h4_conv, h3_down), 1))
        h_fusion = self.h_fusion_conv(h_fusion)
        
        # 边缘增强
        h_fusion = self.edge_enhancer(h_fusion)
        
        # 提取边缘和主体
        edge_feat, body_feat = self._extract_body_and_edge(h_fusion, layer0)
        
        # L特征融合
        l_fusion = self.l_fusion_conv(l2_conv)
        h2l = self.h2l(h_fusion)
        l_fusion = F.sigmoid(h2l) * l_fusion
        
        # 最终特征融合
        h_up_for_final_fusion = self.h_up_for_final_fusion(h_fusion)
        final_fusion = self.final_fusion(torch.cat((h_up_for_final_fusion, l_fusion), 1))
        final_fusion = self.final_fusion_conv(final_fusion)
        
        # 边缘预测
        edge_predict = self.edge_predict(edge_feat)
        edge_predict = F.interpolate(edge_predict, size=x_size[2:], mode='bilinear', align_corners=True)
        
        # 主体预测
        body_predict = self.body_predict(body_feat)
        body_predict = F.interpolate(body_predict, size=x_size[2:], mode='bilinear', align_corners=True)
        
        # 最终预测
        final_predict = self.final_predict(final_fusion)
        final_predict = F.interpolate(final_predict, size=x_size[2:], mode='bilinear', align_corners=True)
        
        # 应用sigmoid获取概率图
        edge_predict_prob = torch.sigmoid(edge_predict)
        body_predict_prob = torch.sigmoid(body_predict)
        final_predict_prob = torch.sigmoid(final_predict)
        
        # 为CRF准备2通道输入
        final_predict_prob = torch.clamp(final_predict_prob, min=1e-7, max=1.0 - 1e-7)
        bg_logits = torch.log((1 - final_predict_prob) * self.class_weights[0])
        fg_logits = torch.log(final_predict_prob * self.class_weights[1])
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
        
        # 归一化图像
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
            
        # 应用CRF后处理
        refined_predict = self.crf(combined_logits, normalized_img)
        
        return edge_predict_prob, body_predict_prob, final_predict_prob, refined_predict
