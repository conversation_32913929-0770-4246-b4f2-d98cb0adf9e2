"""
修复版Proteus ViT-S损失函数
解决NaN问题：
1. 修复IoU损失计算错误（值>1.0）
2. 增强数值稳定性检查
3. 简化动态权重调整
"""
import torch
import torch.nn.functional as F
import torch.nn as nn


class FixedBCELoss(nn.Module):
    """修复版BCE损失"""
    def __init__(self):
        super(FixedBCELoss, self).__init__()
        
    def forward(self, pred, target, eps=1e-7):
        # 确保输入在有效范围内
        pred = torch.clamp(pred, min=eps, max=1.0 - eps)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 数值稳定性检查
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)
            
        if torch.isnan(target).any() or torch.isinf(target).any():
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)
        
        # 标准BCE损失
        bce_loss = F.binary_cross_entropy(pred, target, reduction='mean')
        
        # 最终检查
        if torch.isnan(bce_loss) or torch.isinf(bce_loss):
            bce_loss = torch.tensor(0.69, device=pred.device)  # -ln(0.5)
            
        return bce_loss


class FixedIoULoss(nn.Module):
    """修复版IoU损失 - 解决>1.0的问题"""
    def __init__(self):
        super(FixedIoULoss, self).__init__()
        
    def forward(self, pred, target, eps=1e-7):
        # 确保在有效范围内
        pred = torch.clamp(pred, min=0.0, max=1.0)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 数值稳定性检查
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)

        if torch.isnan(target).any() or torch.isinf(target).any():
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)

        # 计算交集和并集
        intersection = torch.sum(target * pred, dim=(2, 3))  # [B, C]
        union = torch.sum(target, dim=(2, 3)) + torch.sum(pred, dim=(2, 3)) - intersection
        
        # 避免除零
        union = torch.clamp(union, min=eps)
        
        # 计算IoU
        iou = intersection / union  # [B, C]
        iou = torch.clamp(iou, min=0.0, max=1.0)  # 确保在[0,1]范围内
        
        # IoU损失 = 1 - IoU
        iou_loss = 1.0 - iou.mean()
        
        # 确保在[0,1]范围内
        iou_loss = torch.clamp(iou_loss, min=0.0, max=1.0)
        
        # 最终检查
        if torch.isnan(iou_loss) or torch.isinf(iou_loss):
            iou_loss = torch.tensor(0.5, device=pred.device)
            
        return iou_loss


class FixedEdgeLoss(nn.Module):
    """修复版边缘损失"""
    def __init__(self):
        super(FixedEdgeLoss, self).__init__()
        
        # Sobel算子
        self.register_buffer('sobel_x', torch.tensor([
            [-1, 0, 1], [-2, 0, 2], [-1, 0, 1]
        ]).float().view(1, 1, 3, 3))
        
        self.register_buffer('sobel_y', torch.tensor([
            [-1, -2, -1], [0, 0, 0], [1, 2, 1]
        ]).float().view(1, 1, 3, 3))
        
    def _extract_edges(self, x):
        """提取边缘"""
        edge_x = F.conv2d(x, self.sobel_x, padding=1)
        edge_y = F.conv2d(x, self.sobel_y, padding=1)
        edges = torch.sqrt(edge_x**2 + edge_y**2 + 1e-6)
        return torch.clamp(edges, min=0.0, max=1.0)
        
    def forward(self, pred, target):
        pred_edges = self._extract_edges(pred)
        target_edges = self._extract_edges(target)
        
        edge_loss = F.mse_loss(pred_edges, target_edges)
        
        # 最终检查
        if torch.isnan(edge_loss) or torch.isinf(edge_loss):
            edge_loss = torch.tensor(0.1, device=pred.device)
            
        return edge_loss


class FixedFocalLoss(nn.Module):
    """修复版Focal Loss - 专门处理玻璃检测的类别不平衡"""
    def __init__(self, alpha=0.25, gamma=2.0):
        super(FixedFocalLoss, self).__init__()
        self.alpha = alpha  # 玻璃类别权重，通常设为0.25-0.5
        self.gamma = gamma  # 聚焦参数，减少易分类样本的损失贡献
        
    def forward(self, pred, target, eps=1e-7):
        # 确保输入在有效范围内
        pred = torch.clamp(pred, min=eps, max=1.0 - eps)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 数值稳定性检查
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)
            
        if torch.isnan(target).any() or torch.isinf(target).any():
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)
        
        # 计算BCE损失
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        
        # 计算pt (正确类别的预测概率)
        pt = torch.where(target > 0.5, pred, 1 - pred)
        
        # 计算聚焦权重: (1-pt)^gamma
        focal_weight = (1 - pt) ** self.gamma
        
        # 计算alpha权重
        alpha_weight = torch.where(target > 0.5, self.alpha, 1 - self.alpha)
        
        # 计算Focal Loss
        focal_loss = alpha_weight * focal_weight * bce_loss
        
        # 最终检查
        focal_loss_mean = focal_loss.mean()
        if torch.isnan(focal_loss_mean) or torch.isinf(focal_loss_mean):
            focal_loss_mean = torch.tensor(0.69, device=pred.device)  # 默认BCE值
            
        return focal_loss_mean


class ProteusViTSFocalLoss(nn.Module):
    """基于Focal Loss的Proteus ViT-S损失函数 - 专门处理类别不平衡"""
    
    def __init__(self, 
                 focal_weight=0.4,      # Focal Loss权重，替代BCE
                 iou_weight=0.4,        # IoU损失权重
                 edge_weight=0.2,       # 边缘损失权重
                 focal_alpha=0.3,       # Focal Loss的alpha参数
                 focal_gamma=2.5):      # Focal Loss的gamma参数
        super(ProteusViTSFocalLoss, self).__init__()
        
        # 优化权重配置，专门针对类别不平衡
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight
        self.edge_weight = edge_weight
        
        # 损失函数
        self.focal_loss = FixedFocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.iou_loss = FixedIoULoss()
        self.edge_loss = FixedEdgeLoss()
        
    def forward(self, predictions, target, image=None):
        """
        Args:
            predictions: dict with model outputs
            target: [B, 1, H, W]
            image: [B, 3, H, W] (optional)
        """
        # 确保target维度正确
        if target.dim() == 3:
            target = target.unsqueeze(1)
        
        # 提取主要预测结果
        if 'ensemble_pred' in predictions:
            main_pred = predictions['ensemble_pred']
        elif 'final_pred' in predictions:
            main_pred = predictions['final_pred']
        elif 'main_pred' in predictions:
            main_pred = predictions['main_pred']
        else:
            # 使用第一个可用的预测
            main_pred = next(iter(predictions.values()))
        
        # 处理refined_pred的双通道输出
        if main_pred.size(1) == 2:
            main_pred = main_pred[:, 1:2]  # 取前景通道
        
        # 计算损失
        focal_loss = self.focal_loss(main_pred, target)
        iou_loss = self.iou_loss(main_pred, target)
        edge_loss = self.edge_loss(main_pred, target)
        
        # 总损失
        total_loss = (
            self.focal_weight * focal_loss +
            self.iou_weight * iou_loss +
            self.edge_weight * edge_loss
        )
        
        # 最终数值稳定性检查
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("⚠️  检测到NaN/Inf，使用默认损失值")
            total_loss = torch.tensor(1.0, device=target.device, requires_grad=True)
            focal_loss = torch.tensor(0.69, device=target.device)
            iou_loss = torch.tensor(0.5, device=target.device)
            edge_loss = torch.tensor(0.1, device=target.device)
        
        # 返回详细的损失信息
        loss_dict = {
            'total_loss': total_loss,
            'focal_loss': focal_loss,    # 替代bce_loss
            'iou_loss': iou_loss,
            'edge_loss': edge_loss
        }
        
        return total_loss, loss_dict


def create_proteus_vits_focal_loss(**kwargs):
    """创建基于Focal Loss的Proteus ViT-S损失函数"""
    return ProteusViTSFocalLoss(**kwargs)


class FixedProteusViTSLoss(nn.Module):
    """修复版Proteus ViT-S损失函数 - BCE版本"""
    
    def __init__(self, 
                 bce_weight=0.15,
                 iou_weight=0.45, 
                 edge_weight=0.4):
        super(FixedProteusViTSLoss, self).__init__()
        
        # 简化权重配置
        self.bce_weight = bce_weight
        self.iou_weight = iou_weight
        self.edge_weight = edge_weight
        
        # 修复版损失函数
        self.bce_loss = FixedBCELoss()
        self.iou_loss = FixedIoULoss()
        self.edge_loss = FixedEdgeLoss()
        
    def forward(self, predictions, target, image=None):
        """
        Args:
            predictions: dict with model outputs
            target: [B, 1, H, W]
            image: [B, 3, H, W] (optional)
        """
        # 确保target维度正确
        if target.dim() == 3:
            target = target.unsqueeze(1)
        
        # 提取主要预测结果
        if 'ensemble_pred' in predictions:
            main_pred = predictions['ensemble_pred']
        elif 'final_pred' in predictions:
            main_pred = predictions['final_pred']
        elif 'main_pred' in predictions:
            main_pred = predictions['main_pred']
        else:
            # 使用第一个可用的预测
            main_pred = next(iter(predictions.values()))
        
        # 处理refined_pred的双通道输出
        if main_pred.size(1) == 2:
            main_pred = main_pred[:, 1:2]  # 取前景通道
        
        # 计算损失
        bce_loss = self.bce_loss(main_pred, target)
        iou_loss = self.iou_loss(main_pred, target)
        edge_loss = self.edge_loss(main_pred, target)
        
        # 总损失
        total_loss = (
            self.bce_weight * bce_loss +
            self.iou_weight * iou_loss +
            self.edge_weight * edge_loss
        )
        
        # 最终数值稳定性检查
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("⚠️  检测到NaN/Inf，使用默认损失值")
            total_loss = torch.tensor(1.0, device=target.device, requires_grad=True)
            bce_loss = torch.tensor(0.69, device=target.device)
            iou_loss = torch.tensor(0.5, device=target.device)
            edge_loss = torch.tensor(0.1, device=target.device)
        
        # 返回详细的损失信息
        loss_dict = {
            'total_loss': total_loss,
            'bce_loss': bce_loss,
            'iou_loss': iou_loss,
            'edge_loss': edge_loss
        }
        
        return total_loss, loss_dict


def create_proteus_vits_loss(**kwargs):
    """创建修复版Proteus ViT-S损失函数 - BCE版本"""
    return FixedProteusViTSLoss(**kwargs)


# 测试函数
if __name__ == "__main__":
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    criterion = create_proteus_vits_focal_loss()
    
    # 模拟输出和目标
    B, H, W = 2, 416, 416
    predictions = {
        'ensemble_pred': torch.sigmoid(torch.randn(B, 1, H, W)),
    }
    
    targets = torch.rand(B, 1, H, W)
    
    # 计算损失
    total_loss, loss_dict = criterion(predictions, targets)
    
    print('✅ 修复版损失函数测试成功!')
    print(f'总损失: {total_loss.item():.4f}')
    for key, value in loss_dict.items():
        print(f'  {key}: {value.item():.4f}')
