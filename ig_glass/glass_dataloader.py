"""
专门用于玻璃检测的数据加载器
集成玻璃感知数据增强功能
"""

from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

import sys
import cv2
import numpy as np
import glob

import torch
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms

# 导入玻璃感知数据增强
try:
    from .glass_augmentation import apply_glass_augmentation, GLASS_AUGMENTATION_CONFIGS
    from .dataloader import pad_resize_image
except ImportError:
    from ig_glass.glass_augmentation import apply_glass_augmentation, GLASS_AUGMENTATION_CONFIGS
    from ig_glass.dataloader import pad_resize_image


class GlassDataLoader(Dataset):
    """
    专门用于玻璃检测的数据加载器
    集成玻璃感知数据增强功能
    """
    def __init__(
        self, 
        mode='train', 
        augment_data=False, 
        target_size=416,  # ViT-Base推荐使用更高分辨率
        glass_augmentation='moderate',
        dataset_path=None
    ):
        # 数据集路径配置
        if dataset_path is None:
            if mode == 'train':
                self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_msd/image'
                self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_msd/mask'
            elif mode == 'train_gdd':
                self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd/image'
                self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd/mask'
            elif mode == 'test_gdd':
                self.inp_path = '/home/<USER>/Documents/ig_slam_maskdata/test_GDD/image'
                self.out_path = '/home/<USER>/Documents/ig_slam_maskdata/test_GDD/mask'
            elif mode == 'test':
                self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/test_msd/image'
                self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/test_msd/mask'
            elif mode == 'mix_train':
                self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/mix_dataset3/image'
                self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/mix_dataset3/gt_mask'
            elif mode == 'mix_test':
                self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/mix_dataset3/image'
                self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/mix_dataset3/gt_mask'
            else:
                print("mode should be one of: 'train', 'test', 'train_gdd', 'test_gdd', 'mix_train', 'mix_test'")
                sys.exit(0)
        else:
            self.inp_path = dataset_path['image']
            self.out_path = dataset_path['mask']

        self.mode = mode
        self.augment_data = augment_data
        self.target_size = target_size
        self.glass_augmentation = glass_augmentation
        
        # 获取玻璃增强配置
        if glass_augmentation in GLASS_AUGMENTATION_CONFIGS:
            self.augmentation_config = GLASS_AUGMENTATION_CONFIGS[glass_augmentation]
        else:
            self.augmentation_config = GLASS_AUGMENTATION_CONFIGS['moderate']
            
        # ImageNet预训练权重的标准化参数
        self.normalize = transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )

        # 获取文件列表
        self.inp_files = sorted(glob.glob(self.inp_path + '/*'))
        self.out_files = sorted(glob.glob(self.out_path + '/*'))
        
        # 验证文件数量匹配
        if len(self.inp_files) != len(self.out_files):
            print(f"警告: 输入图像数量({len(self.inp_files)})与掩码数量({len(self.out_files)})不匹配")
        
        print(f"加载{mode}数据集: {len(self.inp_files)}张图像")
        print(f"使用玻璃增强配置: {glass_augmentation}")
        print(f"目标尺寸: {target_size}x{target_size}")

    def __getitem__(self, idx):
        # 读取输入图像
        inp_img = cv2.imread(self.inp_files[idx])
        if inp_img is None:
            raise ValueError(f"无法读取图像: {self.inp_files[idx]}")
        
        inp_img = cv2.cvtColor(inp_img, cv2.COLOR_BGR2RGB)
        inp_img = inp_img.astype('float32')

        # 读取掩码图像
        mask_img = cv2.imread(self.out_files[idx], 0)
        if mask_img is None:
            raise ValueError(f"无法读取掩码: {self.out_files[idx]}")
        
        mask_img = mask_img.astype('float32')
        
        # 归一化掩码到[0,1]
        if mask_img.max() > 1.0:
            mask_img = mask_img / 255.0

        # 应用玻璃感知数据增强
        if self.augment_data:
            inp_img, mask_img = apply_glass_augmentation(
                inp_img, mask_img, self.augmentation_config
            )

        # 填充和调整尺寸
        inp_img, mask_img = pad_resize_image(inp_img, mask_img, self.target_size)
        
        # 图像预处理
        inp_img = inp_img / 255.0  # 归一化到[0,1]
        inp_img = np.transpose(inp_img, axes=(2, 0, 1))  # HWC -> CHW
        inp_img = torch.from_numpy(inp_img).float()
        inp_img = self.normalize(inp_img)  # ImageNet标准化

        # 掩码预处理
        mask_img = np.expand_dims(mask_img, axis=0)  # 添加通道维度
        mask_img = torch.from_numpy(mask_img).float()

        return inp_img, mask_img

    def __len__(self):
        return len(self.inp_files)
    
    def get_sample_info(self, idx):
        """获取样本信息，用于调试"""
        return {
            'image_path': self.inp_files[idx],
            'mask_path': self.out_files[idx],
            'index': idx
        }


class GlassCrossValidationLoader(Dataset):
    """
    玻璃检测的交叉验证数据加载器
    支持5折交叉验证
    """
    def __init__(
        self, 
        mode='train', 
        fold=0,
        augment_data=False, 
        target_size=416,
        glass_augmentation='moderate'
    ):
        # 使用混合数据集
        self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/mix_dataset3/image'
        self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/mix_dataset3/gt_mask'
        
        self.mode = mode
        self.fold = fold
        self.augment_data = augment_data
        self.target_size = target_size
        self.glass_augmentation = glass_augmentation
        
        # 获取玻璃增强配置
        if glass_augmentation in GLASS_AUGMENTATION_CONFIGS:
            self.augmentation_config = GLASS_AUGMENTATION_CONFIGS[glass_augmentation]
        else:
            self.augmentation_config = GLASS_AUGMENTATION_CONFIGS['moderate']
            
        self.normalize = transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )

        # 获取所有文件
        all_inp_files = sorted(glob.glob(self.inp_path + '/*'))
        all_out_files = sorted(glob.glob(self.out_path + '/*'))
        
        # 5折交叉验证数据分割
        # 数据集结构: [350张GDD, 100张其他, 43张其他] = 493张总计
        train_indices_1, train_indices_2, train_indices_3 = [], [], []
        
        if mode == 'train':
            # 训练集：排除当前fold的数据
            for i in range(5):
                if i != fold:
                    start_1 = i * 70
                    start_2 = i * 20
                    start_3 = i * 8
                    end_1 = start_1 + 70
                    end_2 = start_2 + 20
                    end_3 = start_3 + 8
                    train_indices_1.extend(range(start_1, end_1))
                    train_indices_2.extend(range(start_2 + 350, end_2 + 350))
                    train_indices_3.extend(range(start_3 + 450, end_3 + 450))
            
            indices = train_indices_1 + train_indices_2 + train_indices_3
            
        elif mode == 'test':
            # 测试集：当前fold的数据
            start_1 = fold * 70
            start_2 = fold * 20
            start_3 = fold * 8
            end_1 = start_1 + 70
            end_2 = start_2 + 20
            end_3 = start_3 + 8
            
            test_indices_1 = list(range(start_1, end_1))
            test_indices_2 = list(range(start_2 + 350, end_2 + 350))
            test_indices_3 = list(range(start_3 + 450, end_3 + 450))
            
            indices = test_indices_1 + test_indices_2 + test_indices_3
        
        # 处理越界索引
        indices = [i % len(all_inp_files) for i in indices]
        
        # 选择对应的文件
        self.inp_files = [all_inp_files[i] for i in indices]
        self.out_files = [all_out_files[i] for i in indices]
        
        print(f"交叉验证 Fold {fold} - {mode}数据集: {len(self.inp_files)}张图像")

    def __getitem__(self, idx):
        # 读取输入图像
        inp_img = cv2.imread(self.inp_files[idx])
        if inp_img is None:
            raise ValueError(f"无法读取图像: {self.inp_files[idx]}")
        
        inp_img = cv2.cvtColor(inp_img, cv2.COLOR_BGR2RGB)
        inp_img = inp_img.astype('float32')

        # 读取掩码图像
        mask_img = cv2.imread(self.out_files[idx], 0)
        if mask_img is None:
            raise ValueError(f"无法读取掩码: {self.out_files[idx]}")
        
        mask_img = mask_img.astype('float32')
        
        # 归一化掩码到[0,1]
        if mask_img.max() > 1.0:
            mask_img = mask_img / 255.0

        # 应用玻璃感知数据增强
        if self.augment_data:
            inp_img, mask_img = apply_glass_augmentation(
                inp_img, mask_img, self.augmentation_config
            )

        # 填充和调整尺寸
        inp_img, mask_img = pad_resize_image(inp_img, mask_img, self.target_size)
        
        # 图像预处理
        inp_img = inp_img / 255.0
        inp_img = np.transpose(inp_img, axes=(2, 0, 1))
        inp_img = torch.from_numpy(inp_img).float()
        inp_img = self.normalize(inp_img)

        # 掩码预处理
        mask_img = np.expand_dims(mask_img, axis=0)
        mask_img = torch.from_numpy(mask_img).float()

        return inp_img, mask_img

    def __len__(self):
        return len(self.inp_files)


class GlassInferenceLoader(Dataset):
    """
    玻璃检测推理数据加载器
    """
    def __init__(self, img_folder, target_size=416):
        self.imgs_folder = img_folder
        self.img_paths = sorted(glob.glob(self.imgs_folder + '/*'))
        self.target_size = target_size
        
        self.normalize = transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )
        
        print(f"推理数据集: {len(self.img_paths)}张图像")

    def __getitem__(self, idx):
        """
        返回原始图像和预处理后的tensor
        """
        img = cv2.imread(self.img_paths[idx])
        if img is None:
            raise ValueError(f"无法读取图像: {self.img_paths[idx]}")
        
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # 填充和调整尺寸（保存原始图像用于可视化）
        img_resized = pad_resize_image(img, None, self.target_size)
        
        # 预处理用于模型推理
        img_tensor = img_resized.astype(np.float32) / 255.0
        img_tensor = np.transpose(img_tensor, axes=(2, 0, 1))
        img_tensor = torch.from_numpy(img_tensor).float()
        img_tensor = self.normalize(img_tensor)

        return img_resized, img_tensor, self.img_paths[idx]

    def __len__(self):
        return len(self.img_paths)


def create_glass_dataloaders(
    mode='train',
    batch_size=8,
    num_workers=4,
    target_size=416,
    augment_data=True,
    glass_augmentation='moderate',
    use_cross_validation=False,
    fold=0
):
    """
    创建玻璃检测数据加载器的便捷函数
    
    Args:
        mode: 'train' 或 'test'
        batch_size: 批次大小
        num_workers: 数据加载进程数
        target_size: 目标图像尺寸
        augment_data: 是否使用数据增强
        glass_augmentation: 玻璃增强配置 ('conservative', 'moderate', 'aggressive')
        use_cross_validation: 是否使用交叉验证
        fold: 交叉验证的折数
    
    Returns:
        DataLoader对象
    """
    if use_cross_validation:
        dataset = GlassCrossValidationLoader(
            mode=mode,
            fold=fold,
            augment_data=augment_data,
            target_size=target_size,
            glass_augmentation=glass_augmentation
        )
    else:
        dataset = GlassDataLoader(
            mode=mode,
            augment_data=augment_data,
            target_size=target_size,
            glass_augmentation=glass_augmentation
        )
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=(mode == 'train'),
        num_workers=num_workers,
        pin_memory=True,
        drop_last=(mode == 'train')
    )
    
    return dataloader


if __name__ == '__main__':
    # 测试玻璃数据加载器
    print("测试玻璃数据加载器...")
    
    # 测试基本数据加载器
    train_loader = create_glass_dataloaders(
        mode='train',
        batch_size=4,
        target_size=416,
        augment_data=True,
        glass_augmentation='moderate'
    )
    
    print(f"训练数据加载器: {len(train_loader)}个批次")
    
    # 测试一个批次
    for batch_idx, (images, masks) in enumerate(train_loader):
        print(f"批次 {batch_idx}: 图像形状 {images.shape}, 掩码形状 {masks.shape}")
        print(f"图像值范围: [{images.min():.3f}, {images.max():.3f}]")
        print(f"掩码值范围: [{masks.min():.3f}, {masks.max():.3f}]")
        if batch_idx >= 2:
            break
    
    print("✅ 玻璃数据加载器测试成功！") 