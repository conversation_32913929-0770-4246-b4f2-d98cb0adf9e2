"""
高级玻璃物理特性检测模块
包含基于物理原理的创新检测方法：
1. 偏振效应模拟
2. 菲涅尔反射建模
3. 色散分析
4. 纹理连续性检测
5. 光谱残差分析
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


###################################################################
# ############## 偏振效应模拟模块 ###############################
###################################################################

class PolarizationSimulator(nn.Module):
    """
    偏振效应模拟器：模拟玻璃表面的偏振特性
    虽然无法获得真实的偏振图像，但可以通过学习模拟偏振效应
    """
    def __init__(self, in_channels):
        super(PolarizationSimulator, self).__init__()
        
        # 偏振角度生成器（模拟不同角度的偏振器）
        self.polarization_angles = nn.Parameter(
            torch.linspace(0, np.pi, 8).view(8, 1, 1, 1), requires_grad=True
        )
        
        # 偏振强度预测器
        self.polarization_predictor = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, 8, 3, 1, 1),  # 8个偏振角度
            nn.Sigmoid()
        )
        
        # 偏振融合网络
        self.polarization_fusion = nn.Sequential(
            nn.Conv2d(8, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 预测各个偏振角度下的强度
        polarization_intensity = self.polarization_predictor(x)
        
        # 模拟偏振效应：玻璃会产生特定的偏振模式
        polarization_response = []
        for i, angle in enumerate(self.polarization_angles):
            # 马吕斯定律的简化版本: I = I0 * cos²(θ)
            malus_factor = torch.cos(angle)**2
            response = polarization_intensity[:, i:i+1] * malus_factor
            polarization_response.append(response)
        
        polarization_stack = torch.cat(polarization_response, dim=1)
        
        # 融合偏振信息
        polarization_features = self.polarization_fusion(polarization_stack)
        
        # 计算偏振度（Degree of Polarization）
        pol_max = torch.max(polarization_stack, dim=1, keepdim=True)[0]
        pol_min = torch.min(polarization_stack, dim=1, keepdim=True)[0]
        polarization_degree = (pol_max - pol_min) / (pol_max + pol_min + 1e-7)
        
        return polarization_features, polarization_degree


###################################################################
# ############## 菲涅尔反射建模模块 ############################
###################################################################

class FresnelReflectionModel(nn.Module):
    """
    菲涅尔反射建模：基于菲涅尔方程预测玻璃的反射特性
    """
    def __init__(self, in_channels):
        super(FresnelReflectionModel, self).__init__()
        
        # 入射角估计器
        self.incident_angle_estimator = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, 1, 1),
            nn.Sigmoid()  # 归一化到[0,1]，对应[0°,90°]
        )
        
        # 折射率估计器（玻璃的折射率通常在1.4-1.6之间）
        self.refractive_index_estimator = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, 1, 1),
            nn.Sigmoid()  # 归一化后映射到[1.4, 1.6]
        )
        
        # 反射率预测器
        self.reflectance_predictor = nn.Sequential(
            nn.Conv2d(2, 16, 3, 1, 1),  # 输入：入射角 + 折射率
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, 3, 1, 1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 估计入射角（转换为弧度）
        incident_angle_norm = self.incident_angle_estimator(x)
        incident_angle = incident_angle_norm * np.pi / 2  # [0, π/2]
        
        # 估计折射率
        n_norm = self.refractive_index_estimator(x)
        refractive_index = 1.4 + 0.2 * n_norm  # [1.4, 1.6]
        
        # 菲涅尔方程计算（简化版本）
        cos_theta_i = torch.cos(incident_angle)
        sin_theta_i = torch.sin(incident_angle)
        
        # 斯涅尔定律：n1*sin(θ1) = n2*sin(θ2)，这里n1=1（空气）
        sin_theta_t = sin_theta_i / refractive_index
        cos_theta_t = torch.sqrt(1 - sin_theta_t**2 + 1e-7)
        
        # 菲涅尔反射系数（s偏振和p偏振的平均）
        rs = (cos_theta_i - refractive_index * cos_theta_t) / (cos_theta_i + refractive_index * cos_theta_t)
        rp = (refractive_index * cos_theta_i - cos_theta_t) / (refractive_index * cos_theta_i + cos_theta_t)
        
        fresnel_reflectance = 0.5 * (rs**2 + rp**2)
        
        # 结合估计的反射率
        input_features = torch.cat([incident_angle_norm, n_norm], dim=1)
        predicted_reflectance = self.reflectance_predictor(input_features)
        
        # 融合理论和预测的反射率
        final_reflectance = 0.7 * fresnel_reflectance + 0.3 * predicted_reflectance
        
        return final_reflectance, incident_angle_norm, refractive_index


###################################################################
# ############## 色散分析模块 ###################################
###################################################################

class DispersionAnalyzer(nn.Module):
    """
    色散分析模块：分析玻璃的色散特性
    不同波长的光在玻璃中有不同的折射率
    """
    def __init__(self, in_channels):
        super(DispersionAnalyzer, self).__init__()
        
        # RGB通道分离处理
        self.red_processor = self._make_wavelength_processor(in_channels//3)
        self.green_processor = self._make_wavelength_processor(in_channels//3)
        self.blue_processor = self._make_wavelength_processor(in_channels//3)
        
        # 色散特征融合
        self.dispersion_fusion = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//4, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True)
        )
        
        # 阿贝数预测器（衡量色散程度）
        self.abbe_number_predictor = nn.Sequential(
            nn.Conv2d(in_channels//4, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 1),
            nn.Sigmoid()
        )
        
    def _make_wavelength_processor(self, channels):
        return nn.Sequential(
            nn.Conv2d(channels, channels, 3, 1, 1),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 1)
        )
        
    def forward(self, x, rgb_input):
        # 分离RGB通道进行处理
        r_features = self.red_processor(x[:, :x.size(1)//3])
        g_features = self.green_processor(x[:, x.size(1)//3:2*x.size(1)//3])
        b_features = self.blue_processor(x[:, 2*x.size(1)//3:])
        
        # 重建特征
        dispersion_features = torch.cat([r_features, g_features, b_features], dim=1)
        
        # 融合色散特征
        fused_dispersion = self.dispersion_fusion(dispersion_features)
        
        # 预测阿贝数（玻璃的阿贝数通常在25-65之间）
        abbe_number = self.abbe_number_predictor(fused_dispersion)
        
        # 计算色散强度：基于RGB通道的差异
        rgb_mean = torch.mean(rgb_input, dim=1, keepdim=True)
        color_deviation = torch.sqrt(torch.sum((rgb_input - rgb_mean)**2, dim=1, keepdim=True))
        
        return fused_dispersion, abbe_number, color_deviation


###################################################################
# ############## 纹理连续性检测模块 ############################
###################################################################

class TextureContinuityDetector(nn.Module):
    """
    纹理连续性检测：透明玻璃会保持背景纹理的连续性，但会有微小扭曲
    """
    def __init__(self, in_channels):
        super(TextureContinuityDetector, self).__init__()
        
        # 多方向纹理检测器
        self.texture_detectors = nn.ModuleList([
            self._make_directional_detector(in_channels, angle)
            for angle in [0, 45, 90, 135]  # 四个主要方向
        ])
        
        # 纹理连续性分析器
        self.continuity_analyzer = nn.Sequential(
            nn.Conv2d(in_channels * 4, in_channels, 3, 1, 1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True)
        )
        
        # 扭曲检测器
        self.distortion_detector = nn.Sequential(
            nn.Conv2d(in_channels//2, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, 1, 1),
            nn.Sigmoid()
        )
        
    def _make_directional_detector(self, in_channels, angle):
        # 创建方向性卷积核
        kernel_size = 5
        kernel = self._create_directional_kernel(kernel_size, angle)
        
        detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//4, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True)
        )
        
        # 注册方向性卷积核
        detector.register_buffer('directional_kernel', kernel)
        return detector
        
    def _create_directional_kernel(self, size, angle):
        """创建方向性检测核"""
        kernel = torch.zeros(1, 1, size, size)
        center = size // 2
        
        # 创建线性核
        for i in range(size):
            for j in range(size):
                # 计算相对位置
                y, x = i - center, j - center
                
                # 旋转坐标
                angle_rad = np.radians(angle)
                rotated_x = x * np.cos(angle_rad) - y * np.sin(angle_rad)
                rotated_y = x * np.sin(angle_rad) + y * np.cos(angle_rad)
                
                # 如果点在主轴附近，设置权重
                if abs(rotated_y) <= 1:
                    kernel[0, 0, i, j] = 1.0 - abs(rotated_y)
        
        # 归一化
        kernel = kernel / torch.sum(kernel)
        return kernel
        
    def forward(self, x):
        # 多方向纹理检测
        directional_features = []
        for detector in self.texture_detectors:
            # 应用方向性检测
            features = detector[:-1](x)  # 除了最后的卷积核
            directional_response = F.conv2d(
                features.mean(dim=1, keepdim=True), 
                detector.directional_kernel, 
                padding=2
            )
            directional_features.append(
                features * directional_response.expand_as(features)
            )
        
        # 融合所有方向的特征
        all_directional = torch.cat(directional_features, dim=1)
        continuity_features = self.continuity_analyzer(all_directional)
        
        # 检测扭曲程度
        distortion_map = self.distortion_detector(continuity_features)
        
        return continuity_features, distortion_map


###################################################################
# ############## 光谱残差分析模块 ###############################
###################################################################

class SpectralResidualAnalyzer(nn.Module):
    """
    光谱残差分析：基于傅里叶变换的频域分析
    玻璃会改变图像的频域特性
    """
    def __init__(self, in_channels):
        super(SpectralResidualAnalyzer, self).__init__()
        
        # 频域特征提取器
        self.freq_feature_extractor = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True)
        )
        
        # 残差预测器
        self.residual_predictor = nn.Sequential(
            nn.Conv2d(in_channels//4, in_channels//8, 3, 1, 1),
            nn.BatchNorm2d(in_channels//8),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//8, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 转换到频域（使用FFT）
        x_freq = torch.fft.fft2(x.float())
        
        # 计算幅度谱和相位谱
        magnitude = torch.abs(x_freq)
        phase = torch.angle(x_freq)
        
        # 对数幅度谱
        log_magnitude = torch.log(magnitude + 1e-7)
        
        # 计算光谱残差
        # 这里简化处理，实际应该计算平滑的对数幅度谱
        smoothed_log_mag = F.avg_pool2d(log_magnitude, kernel_size=3, stride=1, padding=1)
        spectral_residual = log_magnitude - smoothed_log_mag
        
        # 重建到空域
        residual_complex = torch.polar(torch.exp(spectral_residual), phase)
        residual_spatial = torch.real(torch.fft.ifft2(residual_complex))
        
        # 提取频域特征
        freq_features = self.freq_feature_extractor(residual_spatial)
        
        # 预测玻璃概率
        glass_probability = self.residual_predictor(freq_features)
        
        return freq_features, glass_probability, torch.mean(torch.abs(spectral_residual), dim=1, keepdim=True)


###################################################################
# ############## 高级物理特性融合模块 ##########################
###################################################################

class AdvancedGlassPhysicsFusion(nn.Module):
    """
    高级玻璃物理特性融合模块
    """
    def __init__(self, feature_dims):
        super(AdvancedGlassPhysicsFusion, self).__init__()
        
        total_dim = sum(feature_dims.values())
        
        # 多物理特性注意力
        self.physics_attention = nn.MultiheadAttention(
            embed_dim=total_dim//4,
            num_heads=8,
            batch_first=True
        )
        
        # 特征压缩
        self.feature_compression = nn.Sequential(
            nn.Conv2d(total_dim, total_dim//2, 3, 1, 1),
            nn.BatchNorm2d(total_dim//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(total_dim//2, total_dim//4, 3, 1, 1),
            nn.BatchNorm2d(total_dim//4),
            nn.ReLU(inplace=True)
        )
        
        # 物理置信度融合
        self.confidence_fusion = nn.Sequential(
            nn.Conv2d(total_dim//4, total_dim//8, 3, 1, 1),
            nn.BatchNorm2d(total_dim//8),
            nn.ReLU(inplace=True),
            nn.Conv2d(total_dim//8, 1, 1),
            nn.Sigmoid()
        )
        
    def forward(self, physics_features_dict):
        """
        融合所有高级物理特性
        """
        # 拼接所有物理特征
        all_features = torch.cat(list(physics_features_dict.values()), dim=1)
        
        # 压缩特征
        compressed_features = self.feature_compression(all_features)
        
        # 应用自注意力（需要reshape为序列格式）
        B, C, H, W = compressed_features.shape
        features_seq = compressed_features.view(B, C, H*W).permute(0, 2, 1)  # [B, HW, C]
        
        attended_features, _ = self.physics_attention(features_seq, features_seq, features_seq)
        attended_features = attended_features.permute(0, 2, 1).view(B, C, H, W)
        
        # 生成最终置信度
        final_confidence = self.confidence_fusion(attended_features)
        
        return attended_features, final_confidence


def create_advanced_glass_detector(base_channels=512):
    """
    创建完整的高级玻璃物理检测器
    """
    detectors = {
        'polarization': PolarizationSimulator(base_channels),
        'fresnel': FresnelReflectionModel(base_channels),
        'dispersion': DispersionAnalyzer(base_channels),
        'texture': TextureContinuityDetector(base_channels),
        'spectral': SpectralResidualAnalyzer(base_channels)
    }
    
    # 计算各个检测器的输出维度
    feature_dims = {
        'polarization': 8,
        'fresnel': 1,
        'dispersion': base_channels//4,
        'texture': base_channels//2,
        'spectral': base_channels//4
    }
    
    fusion_module = AdvancedGlassPhysicsFusion(feature_dims)
    
    return detectors, fusion_module 