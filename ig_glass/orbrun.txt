
./Examples/Monocular/mono_euroc ./Vocabulary/ORBvoc.txt ./Examples/Monocular/EuRoC.yaml ./Dataset ./Examples/Monocular/EuRoC_TimeStamps/office.txt dataset-office_mono

./Examples/Monocular/mono_euroc ./Vocabulary/ORBvoc.txt ./Examples/Monocular/euroc.yaml ./Dataset ./Examples/Monocular/EuRoC_TimeStamps/IR1227.txt IR_mo1227

evo_traj tum f_dataset-MH_origin_mono.txt f_dataset-MH_ig_mono.txt -p --plot_mode=xz

evo_ape tum f_dataset-MH_origin_mono.txt f_dataset-MH_ig_mono.txt -va --plot --plot_mode xz

evo_rpe tum f_dataset-MH_origin_mono.txt f_dataset-MH_ig_mono.txt -va --plot --plot_mode xyz

sed 's/,/ /g' MH01_GT.txt > new_MH01_GT.txt

evo_ape tum MH01_GT.txt f_dataset-MH_ig_mono.txt --align --plot

evo_rpe tum MH01_GT.txt f_dataset-MH_ig_mono.txt -r trans_part --delta_unit f --delta 1 --plot
--------------------------------------------------------------------------------------------------------------
evo_ape tum MH01_GT.txt f_ORB-SLAM3.txt -va --plot --plot_mode xz
evo_ape tum MH01_GT.txt f_IG-SLAM.txt -va --plot --plot_mode xz

evo_rpe tum MH01_GT.txt f_ORB-SLAM3.txt -va --plot --plot_mode xyz
evo_rpe tum MH01_GT.txt f_IG-SLAM.txt -va --plot --plot_mode xyz

evo_traj tum f_IR12-evening.txt -p --plot_mode=xz

./Examples/RGB-D/rgbd_tum Vocabulary/ORBvoc.txt ./Examples/RGB-D/TUM02.yaml ./Dataset/mav0/cam0 ./Examples/RGB-D/associations/associations02.txt
