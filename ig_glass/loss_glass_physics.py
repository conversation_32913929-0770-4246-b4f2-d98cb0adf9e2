"""
基于玻璃物理特性的损失函数
利用玻璃的反射、透明度、边缘等物理特性设计专门的损失
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from .loss import IOU, EdgeSaliencyLoss


class PhysicsConsistencyLoss(nn.Module):
    """
    物理一致性损失：确保预测结果符合玻璃的物理特性
    """
    def __init__(self, reflection_weight=1.0, transparency_weight=1.0, edge_weight=1.0):
        super(PhysicsConsistencyLoss, self).__init__()
        self.reflection_weight = reflection_weight
        self.transparency_weight = transparency_weight
        self.edge_weight = edge_weight
        
        # 玻璃反射模式的期望特征
        self.register_buffer('expected_reflection_pattern', 
                           torch.tensor([[0, -1, 0], [-1, 4, -1], [0, -1, 0]]).float().view(1, 1, 3, 3))
        
    def forward(self, pred, physics_maps):
        """
        Args:
            pred: 预测的玻璃mask [B, 1, H, W]
            physics_maps: 物理特性图 dict
        """
        device = pred.device
        
        # 反射一致性：玻璃区域应该有高亮度变化
        luminance_map = physics_maps['luminance']
        reflection_consistency = self._reflection_consistency_loss(pred, luminance_map)
        
        # 透明度一致性：玻璃区域应该有适中的透明度
        transparency_map = physics_maps['transparency']
        transparency_consistency = self._transparency_consistency_loss(pred, transparency_map)
        
        # 边缘锐度一致性：玻璃边缘应该有特定的锐度特征
        sharpness_map = physics_maps['sharpness']
        edge_consistency = self._edge_consistency_loss(pred, sharpness_map)
        
        total_loss = (self.reflection_weight * reflection_consistency +
                     self.transparency_weight * transparency_consistency +
                     self.edge_weight * edge_consistency)
        
        return total_loss
    
    def _reflection_consistency_loss(self, pred, luminance_map):
        """反射一致性损失"""
        # 玻璃区域应该有较高的亮度变化
        pred_binary = (pred > 0.5).float()
        
        # 计算亮度梯度 - 确保设备匹配
        reflection_pattern = self.expected_reflection_pattern.to(luminance_map.device)
        luminance_grad = F.conv2d(luminance_map, reflection_pattern, padding=1)
        luminance_grad = torch.abs(luminance_grad)
        
        # 玻璃区域的亮度变化应该更明显
        glass_regions = pred_binary * luminance_grad
        non_glass_regions = (1 - pred_binary) * luminance_grad
        
        # 期望玻璃区域有更高的亮度变化
        glass_mean = torch.mean(glass_regions) + 1e-7
        non_glass_mean = torch.mean(non_glass_regions) + 1e-7
        
        # 如果玻璃区域的亮度变化不够明显，增加损失
        consistency_loss = torch.max(torch.tensor(0.0, device=pred.device), 
                                   non_glass_mean - glass_mean + 0.1)
        
        return consistency_loss
    
    def _transparency_consistency_loss(self, pred, transparency_map):
        """透明度一致性损失"""
        # 玻璃应该有中等透明度（不是完全透明也不是完全不透明）
        pred_binary = (pred > 0.5).float()
        
        # 期望透明度在0.3-0.8之间
        expected_transparency = 0.55
        tolerance = 0.25
        
        glass_transparency = pred_binary * transparency_map
        
        # 计算透明度偏差
        transparency_error = torch.abs(glass_transparency - expected_transparency)
        transparency_penalty = torch.max(torch.tensor(0.0, device=pred.device),
                                       transparency_error - tolerance)
        
        return torch.mean(transparency_penalty)
    
    def _edge_consistency_loss(self, pred, sharpness_map):
        """边缘一致性损失"""
        # 玻璃边缘应该有特定的锐度特征
        pred_edges = self._extract_edges(pred)
        
        # 边缘区域的锐度应该适中
        edge_sharpness = pred_edges * sharpness_map
        
        # 期望边缘锐度在0.4-0.7之间
        expected_sharpness = 0.55
        tolerance = 0.15
        
        sharpness_error = torch.abs(edge_sharpness - expected_sharpness)
        sharpness_penalty = torch.max(torch.tensor(0.0, device=pred.device),
                                    sharpness_error - tolerance)
        
        return torch.mean(sharpness_penalty)
    
    def _extract_edges(self, pred):
        """提取边缘"""
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], 
                              dtype=torch.float, device=pred.device).view(1, 1, 3, 3)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], 
                              dtype=torch.float, device=pred.device).view(1, 1, 3, 3)
        
        grad_x = F.conv2d(pred, sobel_x, padding=1)
        grad_y = F.conv2d(pred, sobel_y, padding=1)
        
        edges = torch.sqrt(grad_x**2 + grad_y**2)
        return torch.sigmoid(edges)


class MultiPhysicsLoss(nn.Module):
    """
    多物理特性损失：结合多种物理特性的综合损失
    """
    def __init__(self, device, focal_weight=1.0, physics_weight=0.5, consistency_weight=0.3, edge_weight=0.2, iou_weight=0.4):
        super(MultiPhysicsLoss, self).__init__()
        self.focal_weight = focal_weight
        self.physics_weight = physics_weight
        self.consistency_weight = consistency_weight
        self.edge_weight = edge_weight
        self.iou_weight = iou_weight
        
        # 基础损失
        self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0)
        self.physics_consistency = PhysicsConsistencyLoss()
        # 专业边缘检测损失
        self.edge_saliency_loss = EdgeSaliencyLoss(device=device, alpha_sal=0.7)
        # IoU损失 - 分割任务的关键损失
        self.iou_loss = IOU(size_average=True)
        
    def forward(self, predictions, target, physics_maps):
        """
        Args:
            predictions: dict with keys ['main_pred', 'physics_pred', 'final_pred', 'ensemble_pred']
            target: ground truth [B, 1, H, W]
            physics_maps: 物理特性图 dict
        """
        # 基础focal loss
        main_focal = self.focal_loss(predictions['main_pred'], target)
        physics_focal = self.focal_loss(predictions['physics_pred'], target)
        final_focal = self.focal_loss(predictions['final_pred'], target)
        ensemble_focal = self.focal_loss(predictions['ensemble_pred'], target)
        
        total_focal = (main_focal + physics_focal + final_focal + ensemble_focal) / 4
        
        # 物理一致性损失
        physics_consistency = self.physics_consistency(predictions['ensemble_pred'], physics_maps)
        
        # 物理预测专门损失：鼓励物理分支学习到真实的物理特性
        physics_specialization = self._physics_specialization_loss(
            predictions['physics_pred'], target, physics_maps
        )
        
        # 专业边缘检测损失 - 应用于最终预测，增加安全检查
        try:
            # 确保预测值在有效范围内
            safe_pred = torch.clamp(predictions['ensemble_pred'], min=1e-7, max=1.0-1e-7)
            edge_loss = self.edge_saliency_loss(safe_pred, target)
            
            # 检查是否产生NaN
            if torch.isnan(edge_loss) or torch.isinf(edge_loss):
                print("警告: 边缘损失产生NaN/Inf，使用备用损失")
                edge_loss = F.binary_cross_entropy(safe_pred, target, reduction='mean')
        except Exception as e:
            print(f"边缘损失计算错误: {e}，使用备用损失")
            safe_pred = torch.clamp(predictions['ensemble_pred'], min=1e-7, max=1.0-1e-7)
            edge_loss = F.binary_cross_entropy(safe_pred, target, reduction='mean')
        
        # IoU损失 - 使用经过验证的原始IoU计算方法
        # 应用于主要预测，使用合理权重
        iou_main = self.iou_loss(predictions['main_pred'], target)
        iou_final = self.iou_loss(predictions['final_pred'], target) 
        iou_ensemble = self.iou_loss(predictions['ensemble_pred'], target)
        
        # 组合IoU损失，主要关注最终预测
        total_iou = (0.2 * iou_main + 0.3 * iou_final + 0.5 * iou_ensemble)
        
        total_loss = (self.focal_weight * total_focal +
                     self.physics_weight * physics_specialization +
                     self.consistency_weight * physics_consistency +
                     self.edge_weight * edge_loss +
                     self.iou_weight * total_iou)
        
        return {
            'total_loss': total_loss,
            'focal_loss': total_focal,
            'physics_specialization': physics_specialization,
            'physics_consistency': physics_consistency,
            'edge_loss': edge_loss,
            'iou_loss': total_iou
        }
    
    def _physics_specialization_loss(self, physics_pred, target, physics_maps):
        """物理分支专门化损失"""
        # 物理分支应该更好地利用物理特性信息
        confidence_map = physics_maps['confidence']
        
        # 在高置信度的物理特性区域，物理预测应该更准确
        weighted_target = target * confidence_map
        weighted_pred = physics_pred * confidence_map
        
        # 加权BCE损失
        eps = 1e-7
        bce_loss = -(weighted_target * torch.log(weighted_pred + eps) + 
                    (1 - weighted_target) * torch.log(1 - weighted_pred + eps))
        
        return torch.mean(bce_loss)


class FocalLoss(nn.Module):
    """标准Focal Loss"""
    def __init__(self, alpha=0.25, gamma=2.0):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, pred, target):
        pred = torch.clamp(pred, min=1e-7, max=1.0 - 1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        pt = target * pred + (1 - target) * (1 - pred)
        focal_weight = (1 - pt) ** self.gamma
        alpha_weight = target * self.alpha + (1 - target) * (1 - self.alpha)
        
        bce_loss = -(target * torch.log(pred) + (1 - target) * torch.log(1 - pred))
        focal_loss = alpha_weight * focal_weight * bce_loss
        
        return focal_loss.mean()


class GlassPhysicsLoss(nn.Module):
    """
    玻璃物理特性专用损失函数的主要接口
    """
    def __init__(self, device, 
                 main_weight=0.3, physics_weight=0.3, final_weight=0.4,
                 focal_weight=1.0, physics_consistency_weight=0.3, 
                 specialization_weight=0.2):
        super(GlassPhysicsLoss, self).__init__()
        self.device = device
        self.main_weight = main_weight
        self.physics_weight = physics_weight
        self.final_weight = final_weight
        
        # 多物理特性损失
        self.multi_physics_loss = MultiPhysicsLoss(
            device=device,
            focal_weight=focal_weight,
            physics_weight=specialization_weight,
            consistency_weight=physics_consistency_weight,
            edge_weight=specialization_weight * 0.3,  # 降低边缘权重，避免过度影响
            iou_weight=0.4  # 恢复合理的IoU损失权重，基于EBLNet核心技术
        )
        
    def forward(self, predictions, target):
        """
        Args:
            predictions: GDNetGlassPhysics的输出dict
            target: 真值 [B, 1, H, W]
        """
        # 确保target维度正确
        if target.dim() == 3:
            target = target.unsqueeze(1)
        
        # 使用多物理特性损失
        loss_dict = self.multi_physics_loss(predictions, target, predictions['physics_maps'])
        
        return loss_dict 