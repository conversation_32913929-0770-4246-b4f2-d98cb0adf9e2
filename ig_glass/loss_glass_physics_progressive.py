"""
渐进式玻璃物理特性损失函数
适配渐进式网络架构，包含边缘损失、物理融合损失和CRF损失
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

# 从本地导入核心损失函数
from ig_glass.loss import IOU

class ProgressiveFocalLoss(nn.Module):
    """渐进式Focal损失"""
    def __init__(self, alpha=0.25, gamma=2.0, weight=1.0):
        super(ProgressiveFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.weight = weight
        
    def forward(self, pred, target):
        # 数值稳定性检查
        pred = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 检查目标值是否在有效范围内
        if torch.any(torch.isnan(target)) or torch.any(torch.isinf(target)):
            print(f"警告: 目标值包含NaN/Inf")
            target = torch.nan_to_num(target, nan=0.0, posinf=1.0, neginf=0.0)
            target = torch.clamp(target, min=0.0, max=1.0)
        
        # 计算focal loss
        pt = target * pred + (1 - target) * (1 - pred)
        focal_weight = (1 - pt) ** self.gamma
        alpha_weight = target * self.alpha + (1 - target) * (1 - self.alpha)
        
        focal_loss = -alpha_weight * focal_weight * torch.log(pt + 1e-7)
        return focal_loss.mean() * self.weight

class ProgressiveEdgeLoss(nn.Module):
    """渐进式边缘损失"""
    def __init__(self, weight=1.0):
        super(ProgressiveEdgeLoss, self).__init__()
        self.weight = weight
        
        # Laplacian边缘检测核
        laplacian_kernel = torch.tensor([
            [-1., -1., -1.],
            [-1.,  8., -1.],
            [-1., -1., -1.]
        ], dtype=torch.float32).view(1, 1, 3, 3)
        self.register_buffer('laplacian_kernel', laplacian_kernel)
        
    def forward(self, pred, target):
        # 数值稳定性检查
        pred = torch.clamp(pred, min=0.0, max=1.0)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # 确保Laplacian核心在正确的设备上
        kernel = self.laplacian_kernel.to(pred.device)
        
        # 提取边缘
        pred_edges = torch.abs(F.conv2d(pred, kernel, padding=1))
        target_edges = torch.abs(F.conv2d(target, kernel, padding=1))
        
        # 边缘损失
        edge_loss = F.mse_loss(pred_edges, target_edges)
        return edge_loss * self.weight

class ProgressiveMultiScaleEdgeLoss(nn.Module):
    """多尺度边缘损失（加权版）"""
    def __init__(self, weight=1.0):
        super(ProgressiveMultiScaleEdgeLoss, self).__init__()
        self.weight = weight
        self.edge_loss = ProgressiveEdgeLoss(weight=1.0)
        # 你可以根据实际情况调整这些权重
        # 渐进式多尺度边缘权重策略 - 保持融合层增强但避免过度
        self.loss_weights = {
            'edge_l2': 0.8,   # 细节层：中等权重
            'edge_l3': 1.0,   # 核心层：标准权重
            'edge_l4': 1.2,   # 语义层：稍高权重
            'edge_fused': 1.5 # 融合层：最高权重（合理增强）
        }

    def forward(self, edge_maps, target):
        total_loss = 0.0
        total_weight = 0.0

        for key, w in self.loss_weights.items():
            if key in edge_maps:
                target_resized = F.adaptive_avg_pool2d(target, edge_maps[key].shape[2:])
                total_loss += self.edge_loss(edge_maps[key], target_resized) * w
                total_weight += w

        if total_weight > 0:
            return (total_loss / total_weight) * self.weight
        else:
            return torch.tensor(0.0, device=target.device, dtype=target.dtype)
        
class ProgressiveConsistencyLoss(nn.Module):
    """预测一致性损失"""
    def __init__(self, weight=1.0):
        super(ProgressiveConsistencyLoss, self).__init__()
        self.weight = weight
        
    def forward(self, main_pred, edge_pred, final_pred):
        # 主预测与边缘预测的一致性
        main_edge_consistency = F.mse_loss(main_pred, edge_pred)
        
        # 主预测与最终预测的一致性
        main_final_consistency = F.mse_loss(main_pred, final_pred)
        
        # 边缘预测与最终预测的一致性
        edge_final_consistency = F.mse_loss(edge_pred, final_pred)
        
        total_consistency = (main_edge_consistency + main_final_consistency + edge_final_consistency) / 3.0
        return total_consistency * self.weight

class ProgressiveCRFLoss(nn.Module):
    """CRF损失"""
    def __init__(self, weight=1.0):
        super(ProgressiveCRFLoss, self).__init__()
        self.weight = weight
        
    def forward(self, crf_pred, ensemble_pred, target):
        # CRF预测与ensemble预测的一致性
        crf_ensemble_consistency = F.mse_loss(crf_pred, ensemble_pred)
        
        # CRF预测与目标的损失
        crf_target_loss = F.binary_cross_entropy(crf_pred, target)
        
        # 组合损失
        total_crf_loss = 0.3 * crf_ensemble_consistency + 0.7 * crf_target_loss
        return total_crf_loss * self.weight

class ProgressiveGlassPhysicsLoss(nn.Module):
    """渐进式玻璃物理特性损失函数"""
    def __init__(self, device='cuda',
                 focal_weight=0.15,
                 iou_weight=0.45,
                 edge_weight=0.4,
                 multiscale_edge_weight=0.2,
                 consistency_weight=0.1,
                 crf_weight=0.05):
        super(ProgressiveGlassPhysicsLoss, self).__init__()
        
        self.device = device
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight
        self.edge_weight = edge_weight
        self.multiscale_edge_weight = multiscale_edge_weight
        self.consistency_weight = consistency_weight
        self.crf_weight = crf_weight
        
        # 初始化损失函数
        self.focal_loss = ProgressiveFocalLoss(alpha=0.25, gamma=2.0, weight=1.0)
        self.iou_loss = IOU(size_average=True)
        self.edge_loss = ProgressiveEdgeLoss(weight=1.0)
        self.multiscale_edge_loss = ProgressiveMultiScaleEdgeLoss(weight=1.0)
        self.consistency_loss = ProgressiveConsistencyLoss(weight=1.0)
        self.crf_loss = ProgressiveCRFLoss(weight=1.0)
        
        print(f"🔧 初始化渐进式损失函数")
        print(f"   📊 权重配置: Focal({focal_weight}) + IoU({iou_weight}) + Edge({edge_weight}) + MultiEdge({multiscale_edge_weight}) + Consistency({consistency_weight}) + CRF({crf_weight})")
        
    def forward(self, outputs, targets):
        """
        计算渐进式损失
        Args:
            outputs: 网络输出字典，包含各种预测
            targets: 真实标签 [B, 1, H, W]
        """
        try:
            # 提取预测结果
            main_pred = outputs['main_pred']
            edge_pred = outputs['edge_pred']
            final_pred = outputs['final_pred']
            ensemble_pred = outputs['ensemble_pred']
            crf_pred = outputs['crf_pred']
            edge_maps = outputs.get('edge_maps', {})
            
            # 强化目标值验证
            targets = torch.clamp(targets, min=0.0, max=1.0)
            if torch.any(torch.isnan(targets)) or torch.any(torch.isinf(targets)):
                print(f"❌ 目标值包含NaN/Inf，已修复")
                targets = torch.nan_to_num(targets, nan=0.0, posinf=1.0, neginf=0.0)
                targets = torch.clamp(targets, min=0.0, max=1.0)
            
            # 确保所有预测都在有效范围内
            main_pred = torch.clamp(main_pred, min=1e-7, max=1.0-1e-7)
            edge_pred = torch.clamp(edge_pred, min=1e-7, max=1.0-1e-7)
            final_pred = torch.clamp(final_pred, min=1e-7, max=1.0-1e-7)
            ensemble_pred = torch.clamp(ensemble_pred, min=1e-7, max=1.0-1e-7)
            crf_pred = torch.clamp(crf_pred, min=1e-7, max=1.0-1e-7)
            
            # 1. Focal损失 - 主要用于最终预测
            focal_main = self.focal_loss(main_pred, targets)
            focal_edge = self.focal_loss(edge_pred, targets)
            focal_final = self.focal_loss(final_pred, targets)
            focal_ensemble = self.focal_loss(ensemble_pred, targets)
            
            total_focal = (0.2 * focal_main + 0.2 * focal_edge + 0.3 * focal_final + 0.3 * focal_ensemble)
            
            # 2. IoU损失 - 用于所有预测
            iou_main = self.iou_loss(main_pred, targets)
            iou_edge = self.iou_loss(edge_pred, targets)
            iou_final = self.iou_loss(final_pred, targets)
            iou_ensemble = self.iou_loss(ensemble_pred, targets)
            
            total_iou = (0.2 * iou_main + 0.2 * iou_edge + 0.3 * iou_final + 0.3 * iou_ensemble)
            
            # 3. 边缘损失 - 主要用于边缘预测
            edge_loss_main = self.edge_loss(main_pred, targets)
            edge_loss_edge = self.edge_loss(edge_pred, targets)
            edge_loss_final = self.edge_loss(final_pred, targets)
            
            total_edge = (0.2 * edge_loss_main + 0.5 * edge_loss_edge + 0.3 * edge_loss_final)
            
            # 4. 多尺度边缘损失
            multiscale_edge_loss_val = self.multiscale_edge_loss(edge_maps, targets)
            
            # 5. 一致性损失
            consistency_loss_val = self.consistency_loss(main_pred, edge_pred, final_pred)
            
            # 6. CRF损失
            crf_loss_val = self.crf_loss(crf_pred, ensemble_pred, targets)
            
            # 计算总损失
            total_loss = (
                self.focal_weight * total_focal +
                self.iou_weight * total_iou +
                self.edge_weight * total_edge +
                self.multiscale_edge_weight * multiscale_edge_loss_val +
                self.consistency_weight * consistency_loss_val +
                self.crf_weight * crf_loss_val
            )
            
            # 返回损失字典
            loss_dict = {
                'total_loss': total_loss,
                'focal_loss': total_focal,
                'iou_loss': total_iou,
                'edge_loss': total_edge,
                'multiscale_edge_loss': multiscale_edge_loss_val,
                'consistency_loss': consistency_loss_val,
                'crf_loss': crf_loss_val,
                # 详细损失
                'focal_main': focal_main,
                'focal_edge': focal_edge,
                'focal_final': focal_final,
                'focal_ensemble': focal_ensemble,
                'iou_main': iou_main,
                'iou_edge': iou_edge,
                'iou_final': iou_final,
                'iou_ensemble': iou_ensemble
            }
            
            return loss_dict
            
        except Exception as e:
            print(f"❌ 渐进式损失计算错误: {e}")
            # 返回简单的focal损失作为后备
            if 'ensemble_pred' in outputs:
                fallback_loss = F.binary_cross_entropy(outputs['ensemble_pred'], targets)
            elif 'final_pred' in outputs:
                fallback_loss = F.binary_cross_entropy(outputs['final_pred'], targets)
            else:
                fallback_loss = torch.tensor(1.0, device=targets.device, requires_grad=True)
            
            return {
                'total_loss': fallback_loss,
                'focal_loss': fallback_loss,
                'iou_loss': torch.tensor(0.0, device=targets.device),
                'edge_loss': torch.tensor(0.0, device=targets.device),
                'multiscale_edge_loss': torch.tensor(0.0, device=targets.device),
                'consistency_loss': torch.tensor(0.0, device=targets.device),
                'crf_loss': torch.tensor(0.0, device=targets.device)
            } 