"""
 @Time    : 2024
 <AUTHOR> <PERSON><PERSON>

 @Project : IG_SLAM
 @File    : loss_lightcrf.py
 @Function: Lightweight loss functions for GDNetLightCRF

"""
import torch
import torch.nn as nn
import torch.nn.functional as F

import os
os.environ['CUDA_LAUNCH_BLOCKING'] = "1"


class EdgeAwareBCELoss(nn.Module):
    """
    Edge-aware BCE loss with balanced weights for foreground and background.
    Lightweight implementation for real-time applications.
    """
    def __init__(self, device, edge_weight=1.2, body_weight=0.8):
        super(EdgeAwareBCELoss, self).__init__()
        self.edge_weight = edge_weight
        self.body_weight = body_weight
        
        # Laplacian kernel for edge detection
        self.laplacian_kernel = torch.tensor([[-1., -1., -1.], [-1., 8., -1.], [-1., -1., -1.]], 
                                            dtype=torch.float, requires_grad=False)
        self.laplacian_kernel = self.laplacian_kernel.view((1, 1, 3, 3))
        self.laplacian_kernel = self.laplacian_kernel.to(device)

    def forward(self, pred, target, eps=1e-7):
        # Print shapes for debugging
        #print(f"EdgeAwareBCELoss input shapes: pred={pred.shape}, target={target.shape}")
        
        # Ensure inputs are in valid range
        pred = torch.clamp(pred, min=eps, max=1.0 - eps)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # Check for NaN or Inf values
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            print("Warning: NaN or Inf values detected in predictions in EdgeAwareBCELoss")
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)
            
        if torch.isnan(target).any() or torch.isinf(target).any():
            print("Warning: NaN or Inf values detected in targets in EdgeAwareBCELoss")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)
        
        # Ensure pred and target have at least one channel
        if pred.dim() == 3:
            pred = pred.unsqueeze(1)
        if target.dim() == 3:
            target = target.unsqueeze(1)
            
        # Check if pred has 0 channels
        if pred.size(1) == 0:
            print("Error: pred has 0 channels. Creating dummy prediction.")
            pred = torch.ones_like(target) * 0.5
        
        # Extract edges from target and prediction
        try:
            target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel, padding=1)))
            pred_edges = F.relu(torch.tanh(F.conv2d(pred, self.laplacian_kernel, padding=1)))
            
            # Create edge-aware weight map
            edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
            
            # Compute standard BCE loss
            bce_loss = -(target * torch.log(pred + eps) + (1 - target) * torch.log(1 - pred + eps))
            
            # Apply edge-aware weighting
            weighted_loss = bce_loss * edge_weight_map
            
            return weighted_loss.mean()
        except Exception as e:
            print(f"Error in EdgeAwareBCELoss: {e}")
            # Return a simple BCE loss as fallback
            return F.binary_cross_entropy(pred, target, reduction='mean')


class EdgeAwareFocalLoss(nn.Module):
    """
    Edge-aware Focal Loss with balanced weights for foreground and background.
    Lightweight implementation for real-time applications.
    """
    def __init__(self, device, alpha=0.25, gamma=2.0, edge_weight=1.2, body_weight=0.8):
        super(EdgeAwareFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.edge_weight = edge_weight
        self.body_weight = body_weight
        
        # Laplacian kernel for edge detection
        self.laplacian_kernel = torch.tensor([[-1., -1., -1.], [-1., 8., -1.], [-1., -1., -1.]], 
                                            dtype=torch.float, requires_grad=False)
        self.laplacian_kernel = self.laplacian_kernel.view((1, 1, 3, 3))
        self.laplacian_kernel = self.laplacian_kernel.to(device)

    def forward(self, pred, target, eps=1e-7):
        # Ensure inputs are in valid range
        pred = torch.clamp(pred, min=eps, max=1.0 - eps)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # Check for NaN or Inf values
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            print("Warning: NaN or Inf values detected in predictions in EdgeAwareFocalLoss")
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)
            
        if torch.isnan(target).any() or torch.isinf(target).any():
            print("Warning: NaN or Inf values detected in targets in EdgeAwareFocalLoss")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)
        
        # Ensure pred and target have at least one channel
        if pred.dim() == 3:
            pred = pred.unsqueeze(1)
        if target.dim() == 3:
            target = target.unsqueeze(1)
            
        # Check if pred has 0 channels
        if pred.size(1) == 0:
            print("Error: pred has 0 channels. Creating dummy prediction.")
            pred = torch.ones_like(target) * 0.5
        
        # Extract edges from target and prediction
        try:
            target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel, padding=1)))
            
            # Create edge-aware weight map
            edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
            
            # Compute focal loss components
            pt = target * pred + (1 - target) * (1 - pred)  # 正确分类的概率
            focal_weight = (1 - pt) ** self.gamma
            alpha_weight = target * self.alpha + (1 - target) * (1 - self.alpha)
            
            # Compute Focal loss
            focal_loss = -alpha_weight * focal_weight * torch.log(pt + eps)
            
            # Apply edge-aware weighting
            weighted_loss = focal_loss * edge_weight_map
            
            return weighted_loss.mean()
        except Exception as e:
            print(f"Error in EdgeAwareFocalLoss: {e}")
            # Return a simple Focal loss as fallback
            pt = target * pred + (1 - target) * (1 - pred)
            focal_weight = (1 - pt) ** self.gamma
            alpha_weight = target * self.alpha + (1 - target) * (1 - self.alpha)
            return (-alpha_weight * focal_weight * torch.log(pt + eps)).mean()


class LightIOU(nn.Module):
    """
    Lightweight IoU loss implementation for real-time applications
    """
    def __init__(self):
        super(LightIOU, self).__init__()
        
    def forward(self, pred, target):
        # Ensure pred and target are in valid range
        pred = torch.clamp(pred, min=0.0, max=1.0)
        target = torch.clamp(target, min=0.0, max=1.0)

        # Check for NaN or Inf values
        if torch.isnan(pred).any() or torch.isinf(pred).any():
            print("Warning: NaN or Inf values detected in predictions in IOU loss")
            pred = torch.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)

        if torch.isnan(target).any() or torch.isinf(target).any():
            print("Warning: NaN or Inf values detected in targets in IOU loss")
            target = torch.nan_to_num(target, nan=0.5, posinf=1.0, neginf=0.0)

        # Compute intersection and union
        intersection = torch.sum(target * pred, dim=(1, 2, 3))
        union = torch.sum(target, dim=(1, 2, 3)) + torch.sum(pred, dim=(1, 2, 3)) - intersection
        
        # Add small epsilon to avoid division by zero
        union = torch.clamp(union, min=1e-7)
        
        # Compute IoU loss (1 - IoU)
        iou_loss = 1.0 - (intersection / union).mean()
        
        return iou_loss


class CompositeLoss(nn.Module):
    """
    Composite loss function combining edge-aware Focal and IoU loss
    with weighting for CRF predictions
    """
    def __init__(self, device, bce_weight=0.7, iou_weight=0.3, crf_weight=1.5, 
                 focal_alpha=0.25, focal_gamma=2.0, use_focal=True):
        super(CompositeLoss, self).__init__()
        # 根据use_focal参数决定使用BCE Loss还是Focal Loss
        if use_focal:
            self.cls_loss = EdgeAwareFocalLoss(device, alpha=focal_alpha, gamma=focal_gamma)
        else:
            self.cls_loss = EdgeAwareBCELoss(device)
            
        self.iou_loss = LightIOU()
        # 保留参数名称以兼容现有代码
        self.bce_weight = bce_weight
        self.iou_weight = iou_weight
        self.crf_weight = crf_weight
        self.use_focal = use_focal
        
    def forward(self, preds, target):
        """
        Args:
            preds: tuple of (h_pred, l_pred, final_pred, refined_pred)
            target: ground truth mask
        """
        h_pred, l_pred, final_pred, refined_pred = preds
        
        # 使用设置的权重
        cls_weight = self.bce_weight
        iou_weight = self.iou_weight
        
        # 计算每个预测的损失
        h_cls_loss = self.cls_loss(h_pred, target)
        h_iou_loss = self.iou_loss(h_pred, target)
        h_total_loss = cls_weight * h_cls_loss + iou_weight * h_iou_loss
        
        l_cls_loss = self.cls_loss(l_pred, target)
        l_iou_loss = self.iou_loss(l_pred, target)
        l_total_loss = cls_weight * l_cls_loss + iou_weight * l_iou_loss
        
        final_cls_loss = self.cls_loss(final_pred, target)
        final_iou_loss = self.iou_loss(final_pred, target)
        final_total_loss = cls_weight * final_cls_loss + iou_weight * final_iou_loss
        
        # 处理CRF预测
        if refined_pred.size(1) == 0:
            print("Warning: refined_pred has 0 channels. Using a dummy tensor.")
            refined_pred_fg = torch.zeros_like(target)
        elif refined_pred.size(1) == 2:
            refined_pred_fg = refined_pred[:, 1:2]
        elif refined_pred.size(1) == 1:
            refined_pred_fg = refined_pred
        else:
            print(f"Warning: refined_pred has unexpected number of channels: {refined_pred.size(1)}. Using first channel.")
            refined_pred_fg = refined_pred[:, 0:1]
            
        refined_cls_loss = self.cls_loss(refined_pred_fg, target)
        refined_iou_loss = self.iou_loss(refined_pred_fg, target)
        refined_total_loss = cls_weight * refined_cls_loss + iou_weight * refined_iou_loss
        
        # 规范化CRF权重以避免梯度过大
        norm_crf_weight = min(0.4, self.crf_weight / 4.0)
        
        # 调整权重以使总和为1.0，增强稳定性
        base_weight = (1.0 - norm_crf_weight) / 3.0
        h_weight = base_weight * 0.5
        l_weight = base_weight * 0.5
        final_weight = base_weight * 2.0
        
        total_loss = h_weight * h_total_loss + l_weight * l_total_loss + final_weight * final_total_loss + norm_crf_weight * refined_total_loss
        
        return total_loss 