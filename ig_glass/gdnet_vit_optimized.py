"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_vit_optimized.py
 @Function: 优化的ViT玻璃检测网络，目标突破90% IoU

核心优化策略：
1. 修复patch_size为16，获得26x26规整特征图
2. 多尺度ViT特征提取，保留层次信息
3. ViT-CNN协同注意力，避免冲突
4. 轻量级特征融合，减少信息损失
5. 专门的ViT位置编码优化
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import sys
import os
from functools import partial

# 添加Proteus路径
proteus_path = '/home/<USER>/ws/IG_SLAM/Proteus-pytorch/pretrain'
if proteus_path not in sys.path:
    sys.path.append(proteus_path)

try:
    from models_dinov2 import DinoVisionTransformer, Attention, Block
except ImportError:
    print("Warning: Proteus models not found, using fallback implementation")
    DinoVisionTransformer = None

from ig_glass.diff_crf import SimplifiedDiffCRF


class OptimizedViTBackbone(nn.Module):
    """
    优化的ViT骨干网络 - 专门针对玻璃检测优化
    """
    def __init__(self, pretrained_path=None, img_size=416, patch_size=16):
        super(OptimizedViTBackbone, self).__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2  # 26x26 = 676
        
        # 使用更合适的patch_size=16
        if DinoVisionTransformer is not None:
            self.vit_model = DinoVisionTransformer(
                patch_size=patch_size,
                embed_dim=768,  # ViT-B
                depth=12,
                num_heads=12,
                mlp_ratio=4,
                block_fn=partial(Block, attn_class=Attention),
                num_register_tokens=0
            )
            
            # 加载预训练权重
            if pretrained_path and os.path.exists(pretrained_path):
                print(f"📦 加载ViT预训练权重: {pretrained_path}")
                try:
                    checkpoint = torch.load(pretrained_path, map_location='cpu')
                    if 'model' in checkpoint:
                        model_weights = checkpoint['model']
                        student_weights = {}
                        for key, value in model_weights.items():
                            if key.startswith('student.backbone.'):
                                new_key = key.replace('student.backbone.', '')
                                student_weights[new_key] = value
                        self.vit_model.load_state_dict(student_weights, strict=False)
                    else:
                        self.vit_model.load_state_dict(checkpoint, strict=False)
                    print(f"✅ ViT权重加载成功")
                except Exception as e:
                    print(f"⚠️ ViT权重加载失败: {e}")
        else:
            # Fallback implementation
            self.vit_model = self._create_simple_vit()
            
        self.embed_dim = 768
        
        # 多尺度特征提取 - 从不同ViT层提取特征
        self.feature_scales = [3, 6, 9, 12]  # 提取第3,6,9,12层特征
        
        # 特征适配器 - 简化版本，减少信息损失
        self.scale_adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(self.embed_dim, 256, 1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True)
            ) for _ in self.feature_scales
        ])
        
        print(f"✅ 优化ViT创建成功:")
        print(f"   图像尺寸: {img_size}x{img_size}")
        print(f"   Patch尺寸: {patch_size}x{patch_size}")
        print(f"   特征图尺寸: {img_size//patch_size}x{img_size//patch_size}")
        print(f"   多尺度层: {self.feature_scales}")
        
    def _create_simple_vit(self):
        """简单的ViT实现作为fallback"""
        class SimpleViT(nn.Module):
            def __init__(self):
                super().__init__()
                self.patch_embed = nn.Conv2d(3, 768, kernel_size=16, stride=16)
                self.pos_embed = nn.Parameter(torch.randn(1, 676, 768) * 0.02)
                self.blocks = nn.ModuleList([
                    nn.TransformerEncoderLayer(768, 12, 3072, dropout=0.1)
                    for _ in range(12)
                ])
                self.norm = nn.LayerNorm(768)
                
            def forward_features(self, x):
                B, C, H, W = x.shape
                x = self.patch_embed(x)  # [B, 768, 26, 26]
                x = x.flatten(2).transpose(1, 2)  # [B, 676, 768]
                x = x + self.pos_embed
                
                features = []
                for i, block in enumerate(self.blocks):
                    x = block(x)
                    if i + 1 in [3, 6, 9, 12]:
                        features.append(x)
                
                return {"intermediate_features": features, "x_norm_patchtokens": features[-1]}
        
        return SimpleViT()
        
    def forward(self, x):
        """
        提取多尺度ViT特征
        Args:
            x: [B, 3, 416, 416]
        Returns:
            multi_scale_features: list of [B, 256, 26, 26]
        """
        B, C, H, W = x.shape
        
        # ViT前向传播
        if hasattr(self.vit_model, 'forward_features'):
            # DINOv2 style
            vit_output = self.vit_model.forward_features(x)
            if "intermediate_features" in vit_output:
                intermediate_features = vit_output["intermediate_features"]
            else:
                # 如果没有中间特征，重复使用最终特征
                final_features = vit_output["x_norm_patchtokens"]
                intermediate_features = [final_features] * len(self.feature_scales)
        else:
            # Fallback
            vit_output = self.vit_model.forward_features(x)
            intermediate_features = vit_output["intermediate_features"]
        
        # 转换为特征图格式并适配
        multi_scale_features = []
        patch_h = patch_w = self.img_size // self.patch_size  # 26
        
        for i, features in enumerate(intermediate_features):
            # [B, 676, 768] -> [B, 768, 26, 26]
            feature_map = features.transpose(1, 2).reshape(B, self.embed_dim, patch_h, patch_w)
            
            # 特征适配
            adapted_features = self.scale_adapters[i](feature_map)  # [B, 256, 26, 26]
            multi_scale_features.append(adapted_features)
        
        return multi_scale_features


class ViTCNNFusion(nn.Module):
    """
    ViT-CNN协同融合模块 - 避免注意力冲突
    """
    def __init__(self, vit_dim=256, cnn_channels=[64, 128, 256, 512]):
        super(ViTCNNFusion, self).__init__()
        
        # 轻量级CNN分支 - 提供局部细节
        self.cnn_layers = nn.ModuleList()
        in_ch = 3
        for out_ch in cnn_channels:
            self.cnn_layers.append(nn.Sequential(
                nn.Conv2d(in_ch, out_ch, 3, 2, 1),
                nn.BatchNorm2d(out_ch),
                nn.ReLU(inplace=True)
            ))
            in_ch = out_ch
        
        # CNN特征适配
        self.cnn_adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(ch, vit_dim, 1),
                nn.BatchNorm2d(vit_dim),
                nn.ReLU(inplace=True)
            ) for ch in cnn_channels
        ])
        
        # 跨模态注意力 - ViT指导CNN，CNN补充ViT
        self.cross_attention = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(vit_dim * 2, vit_dim, 3, 1, 1),
                nn.BatchNorm2d(vit_dim),
                nn.ReLU(inplace=True),
                nn.Conv2d(vit_dim, vit_dim, 1),
                nn.Sigmoid()  # 注意力权重
            ) for _ in range(4)
        ])
        
        # 融合输出
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(vit_dim * 4, vit_dim * 2, 3, 1, 1),
            nn.BatchNorm2d(vit_dim * 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(vit_dim * 2, vit_dim, 3, 1, 1),
            nn.BatchNorm2d(vit_dim),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, vit_features, x):
        """
        Args:
            vit_features: list of 4 ViT features [B, 256, 26, 26]
            x: input image [B, 3, 416, 416]
        Returns:
            fused_feature: [B, 256, 26, 26]
        """
        # CNN特征提取
        cnn_features = []
        cnn_x = x
        for i, layer in enumerate(self.cnn_layers):
            cnn_x = layer(cnn_x)
            cnn_features.append(cnn_x)
        
        # 跨模态融合
        fused_features = []
        target_size = vit_features[0].shape[2:]  # [26, 26]
        
        for i in range(4):
            vit_feat = vit_features[i]  # [B, 256, 26, 26]
            
            # 调整CNN特征尺寸
            cnn_feat = cnn_features[i]
            if cnn_feat.shape[2:] != target_size:
                cnn_feat = F.interpolate(cnn_feat, size=target_size, 
                                       mode='bilinear', align_corners=True)
            
            # 适配CNN特征维度
            cnn_feat_adapted = self.cnn_adapters[i](cnn_feat)  # [B, 256, 26, 26]
            
            # 跨模态注意力
            combined = torch.cat([vit_feat, cnn_feat_adapted], dim=1)  # [B, 512, 26, 26]
            attention_weight = self.cross_attention[i](combined)  # [B, 256, 26, 26]
            
            # 融合特征
            fused_feat = vit_feat * attention_weight + cnn_feat_adapted * (1 - attention_weight)
            fused_features.append(fused_feat)
        
        # 最终融合
        all_features = torch.cat(fused_features, dim=1)  # [B, 1024, 26, 26]
        final_feature = self.fusion_conv(all_features)   # [B, 256, 26, 26]
        
        return final_feature


class GlassEdgeEnhancer(nn.Module):
    """
    专门针对玻璃的边缘增强模块
    """
    def __init__(self, in_channels=256):
        super(GlassEdgeEnhancer, self).__init__()
        
        # 玻璃特有的边缘检测
        self.register_buffer('glass_edge_kernel', torch.tensor([
            [0, -1, 0],
            [-1, 5, -1], 
            [0, -1, 0]
        ]).float().view(1, 1, 3, 3))
        
        # 多方向边缘检测
        self.edge_detectors = nn.ModuleList([
            nn.Conv2d(in_channels, in_channels//4, 3, 1, 1, groups=in_channels//4),
            nn.Conv2d(in_channels, in_channels//4, 5, 1, 2, groups=in_channels//4),
            nn.Conv2d(in_channels, in_channels//4, 7, 1, 3, groups=in_channels//4)
        ])
        
        # 边缘融合
        self.edge_fusion = nn.Sequential(
            nn.Conv2d(in_channels//4 * 3 + 1, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True)
        )
        
        # 最终增强
        self.enhance_conv = nn.Sequential(
            nn.Conv2d(in_channels + in_channels//4, in_channels, 3, 1, 1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        """
        Args:
            x: [B, 256, 26, 26]
        Returns:
            enhanced_x: [B, 256, 26, 26]
        """
        # 玻璃边缘检测
        gray = torch.mean(x, dim=1, keepdim=True)
        glass_edges = F.conv2d(gray, self.glass_edge_kernel, padding=1)
        glass_edges = torch.sigmoid(glass_edges)
        
        # 多尺度边缘检测
        edge_features = []
        for detector in self.edge_detectors:
            edge_feat = detector(x)
            edge_features.append(edge_feat)
        
        # 融合边缘特征
        all_edges = torch.cat(edge_features + [glass_edges], dim=1)
        edge_map = self.edge_fusion(all_edges)
        
        # 特征增强
        enhanced_features = torch.cat([x, edge_map], dim=1)
        enhanced_x = self.enhance_conv(enhanced_features)
        
        return enhanced_x


class GDNetViTOptimized(nn.Module):
    """
    优化的ViT玻璃检测网络 - 目标90% IoU
    """
    def __init__(self, backbone_path=None, crf_iter=6, trainable_crf=True):
        super(GDNetViTOptimized, self).__init__()
        
        # 优化的ViT骨干
        self.vit_backbone = OptimizedViTBackbone(
            pretrained_path=backbone_path,
            img_size=416,
            patch_size=16
        )
        
        # ViT-CNN融合
        self.vit_cnn_fusion = ViTCNNFusion(vit_dim=256)
        
        # 玻璃边缘增强
        self.edge_enhancer = GlassEdgeEnhancer(in_channels=256)
        
        # 多尺度预测头
        self.predict_high = nn.Sequential(
            nn.Conv2d(256, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 1, 3, 1, 1)
        )
        
        self.predict_edge = nn.Sequential(
            nn.Conv2d(256, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 1, 3, 1, 1)
        )
        
        # 优化的CRF
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=8.0,
            gaussian_weight=4.0,
            bilateral_spatial_sigma=40.0,
            bilateral_color_sigma=3.0,
            gaussian_sigma=1.5,
            trainable=trainable_crf
        )
        
        # 自适应权重
        self.adaptive_weights = nn.Parameter(torch.tensor([0.7, 0.3]), requires_grad=True)
        
        print(f"✅ 优化ViT玻璃检测网络创建成功!")
        
    def forward(self, x):
        """
        Args:
            x: [B, 3, 416, 416]
        Returns:
            predictions dict
        """
        # ViT多尺度特征提取
        vit_features = self.vit_backbone(x)  # list of [B, 256, 26, 26]
        
        # ViT-CNN融合
        fused_features = self.vit_cnn_fusion(vit_features, x)  # [B, 256, 26, 26]
        
        # 玻璃边缘增强
        enhanced_features = self.edge_enhancer(fused_features)  # [B, 256, 26, 26]
        
        # 预测
        pred_high = self.predict_high(enhanced_features)  # [B, 1, 26, 26]
        pred_edge = self.predict_edge(fused_features)     # [B, 1, 26, 26]
        
        # 上采样到原始尺寸
        pred_high = F.interpolate(pred_high, size=x.size()[2:], mode='bilinear', align_corners=True)
        pred_edge = F.interpolate(pred_edge, size=x.size()[2:], mode='bilinear', align_corners=True)
        
        # 应用sigmoid
        pred_high_prob = torch.sigmoid(pred_high)
        pred_edge_prob = torch.sigmoid(pred_edge)
        
        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = weights[0] * pred_high_prob + weights[1] * pred_edge_prob
        ensemble_pred = torch.clamp(ensemble_pred, min=1e-7, max=1.0 - 1e-7)
        
        # CRF后处理
        bg_logits = torch.log(1 - ensemble_pred + 1e-7)
        fg_logits = torch.log(ensemble_pred + 1e-7)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
        
        # 归一化图像
        normalized_img = self._normalize_image(x)
        
        # 应用CRF
        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - ensemble_pred, ensemble_pred], dim=1)
        
        return {
            'pred_high': pred_high_prob,
            'pred_edge': pred_edge_prob,
            'ensemble_pred': ensemble_pred,
            'refined_pred': refined_predict
        }
    
    def _normalize_image(self, x):
        """图像归一化"""
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        return normalized_img


def create_vit_optimized_model(backbone_path=None, crf_iter=6, trainable_crf=True):
    """创建优化ViT模型"""
    return GDNetViTOptimized(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )


if __name__ == "__main__":
    # 测试模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = create_vit_optimized_model().to(device)
    
    # 测试前向传播
    with torch.no_grad():
        x = torch.randn(2, 3, 416, 416).to(device)
        outputs = model(x)
        
        print("模型输出:")
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
        
        # 计算参数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"\n模型参数统计:")
        print(f"  总参数量: {total_params:,} ({total_params/1e6:.1f}M)")
        print(f"  可训练参数: {trainable_params:,} ({trainable_params/1e6:.1f}M)")