import torch
from torch import nn
import os

from ig_glass.backbone.resnext import resnext_101_32x4d_
from ig_glass.backbone.resnext.resnext101_32x8 import ResNeXt101_32x8


class ResNeXt101(nn.Module):
    def __init__(self, backbone_path):
        super(ResNeXt101, self).__init__()
        
        # 根据backbone文件名判断使用哪种方式加载
        if backbone_path is not None:
            if '32x8' in os.path.basename(backbone_path):
                # 使用本地实现的32x8模型
                net = ResNeXt101_32x8(pretrained_path=backbone_path)
                print("Load ResNeXt-101-32x8 Weights Succeed!")
            else:
                # 使用原有的方式加载32x4d模型
                net = resnext_101_32x4d_.resnext_101_32x4d
                weights = torch.load(backbone_path)
                net.load_state_dict(weights, strict=True)
                print("Load ResNeXt-101-32x4d Weights Succeed!")
        else:
            # 如果没有提供backbone路径，使用默认的32x8模型
            net = ResNeXt101_32x8(pretrained_path=backbone_path)

        # 提取网络层
        if isinstance(net, nn.Sequential):
            # 32x4d模型的处理方式
            net = list(net.children())
            self.layer0 = nn.Sequential(*net[:3])
            self.layer1 = nn.Sequential(*net[3: 5])
            self.layer2 = net[5]
            self.layer3 = net[6]
            self.layer4 = net[7]
        else:
            # 32x8模型的处理方式
            self.layer0 = nn.Sequential(
                net.conv1,
                net.bn1,
                net.relu,
                net.maxpool
            )
            self.layer1 = net.layer1
            self.layer2 = net.layer2
            self.layer3 = net.layer3
            self.layer4 = net.layer4

    def forward(self, x):
        layer0 = self.layer0(x)
        layer1 = self.layer1(layer0)
        layer2 = self.layer2(layer1)
        layer3 = self.layer3(layer2)
        layer4 = self.layer4(layer3)
        return layer4
