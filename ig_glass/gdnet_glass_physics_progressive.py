"""
渐进式玻璃物理特性网络
逐步添加物理模块，首先引入边缘检测、物理融合和CRF后处理
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF

def check_tensor_health(tensor, name="tensor"):
    """检查张量的数值健康状况"""
    if torch.isnan(tensor).any():
        print(f"❌ {name} 包含 NaN!")
        return False
    if torch.isinf(tensor).any():
        print(f"❌ {name} 包含 Inf!")
        return False
    if tensor.max() > 1e6:
        print(f"⚠️ {name} 最大值过大: {tensor.max().item()}")
    if tensor.min() < -1e6:
        print(f"⚠️ {name} 最小值过小: {tensor.min().item()}")
    return True

class SafeEdgeDetector(nn.Module):
    """安全的边缘检测模块"""
    def __init__(self, in_channels):
        super(SafeEdgeDetector, self).__init__()
        
        # 简化的边缘检测器
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels//2, 3, 1, 1),
            nn.BatchNorm2d(in_channels//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//2, in_channels//4, 3, 1, 1),
            nn.BatchNorm2d(in_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels//4, 1, 1),
            nn.Sigmoid()
        )
        
        # Laplacian边缘检测核（固定）
        laplacian_kernel = torch.tensor([
            [-1., -1., -1.],
            [-1.,  8., -1.],
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3)
        self.register_buffer('laplacian_kernel', laplacian_kernel)
        
    def forward(self, x):
        if not check_tensor_health(x, "edge_input"):
            return torch.zeros(x.size(0), 1, x.size(2), x.size(3), device=x.device)
        
        # 学习的边缘特征
        edge_features = self.edge_conv(x)
        check_tensor_health(edge_features, "learned_edges")
        
        # 传统边缘检测（作为辅助）
        if x.size(1) == 1:
            # 如果输入是单通道，直接使用
            traditional_edges = F.conv2d(x, self.laplacian_kernel, padding=1)
        else:
            # 如果是多通道，先转为灰度
            gray = torch.mean(x, dim=1, keepdim=True)
            traditional_edges = F.conv2d(gray, self.laplacian_kernel, padding=1)
        
        traditional_edges = torch.abs(traditional_edges)
        traditional_edges = torch.sigmoid(traditional_edges)
        check_tensor_health(traditional_edges, "traditional_edges")
        
        # 融合学习和传统边缘
        combined_edges = 0.7 * edge_features + 0.3 * traditional_edges
        combined_edges = torch.clamp(combined_edges, 0, 1)
        
        check_tensor_health(combined_edges, "combined_edges")
        return combined_edges

class SafePhysicsFusion(nn.Module):
    """安全的物理特性融合模块"""
    def __init__(self, main_channels, edge_channels=1):
        super(SafePhysicsFusion, self).__init__()
        
        # 特征适配器
        self.main_adapter = nn.Sequential(
            nn.Conv2d(main_channels, 256, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        
        self.edge_adapter = nn.Sequential(
            nn.Conv2d(edge_channels, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )
        
        # 融合网络
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(256 + 64, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True)
        )
        
        # 注意力机制（简化版）
        self.attention = nn.Sequential(
            nn.Conv2d(128, 64, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 1),
            nn.Sigmoid()
        )
        
    def forward(self, main_features, edge_features):
        if not check_tensor_health(main_features, "main_features"):
            return torch.zeros(main_features.size(0), 128, main_features.size(2), main_features.size(3), device=main_features.device)
        if not check_tensor_health(edge_features, "edge_features"):
            return torch.zeros(main_features.size(0), 128, main_features.size(2), main_features.size(3), device=main_features.device)
        
        # 特征适配
        main_adapted = self.main_adapter(main_features)
        check_tensor_health(main_adapted, "main_adapted")
        
        # 上采样边缘特征到主特征尺寸
        edge_upsampled = F.interpolate(edge_features, size=main_adapted.shape[2:], mode='bilinear', align_corners=True)
        edge_adapted = self.edge_adapter(edge_upsampled)
        check_tensor_health(edge_adapted, "edge_adapted")
        
        # 特征融合
        fused = torch.cat([main_adapted, edge_adapted], dim=1)
        check_tensor_health(fused, "fused_features")
        
        fused = self.fusion_conv(fused)
        check_tensor_health(fused, "fusion_conv_output")
        
        # 注意力加权
        attention_weights = self.attention(fused)
        check_tensor_health(attention_weights, "attention_weights")
        
        enhanced = fused * attention_weights
        check_tensor_health(enhanced, "enhanced_features")
        
        return enhanced

class GDNetGlassPhysicsProgressive(nn.Module):
    """渐进式玻璃物理特性网络"""
    def __init__(self, backbone_path=None, crf_iter=3, trainable_crf=True):
        super(GDNetGlassPhysicsProgressive, self).__init__()
        
        # 骨干网络
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0
        self.layer1 = resnext.layer1
        self.layer2 = resnext.layer2
        self.layer3 = resnext.layer3
        self.layer4 = resnext.layer4

        # 边缘检测模块
        self.edge_detector_l2 = SafeEdgeDetector(512)   # layer2: 512通道
        self.edge_detector_l3 = SafeEdgeDetector(1024)  # layer3: 1024通道
        self.edge_detector_l4 = SafeEdgeDetector(2048)  # layer4: 2048通道
        
        # 多尺度特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(2048 + 1024 + 512, 1024, 3, 1, 1),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )
        
        # 物理特性融合
        self.physics_fusion = SafePhysicsFusion(512, 1)
        
        # 预测头
        self.main_predict = nn.Sequential(
            nn.Conv2d(512, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 1, 3, 1, 1)
        )
        
        self.edge_predict = nn.Sequential(
            nn.Conv2d(1, 32, 3, 1, 1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 1, 3, 1, 1)
        )
        
        self.final_predict = nn.Sequential(
            nn.Conv2d(128, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 1, 3, 1, 1)
        )

        # CRF后处理
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=8.0,
            gaussian_weight=4.0,
            bilateral_spatial_sigma=35.0,
            bilateral_color_sigma=2.5,
            gaussian_sigma=1.2,
            trainable=trainable_crf
        )

        # 自适应权重
        self.adaptive_weights = nn.Parameter(torch.tensor([0.4, 0.3, 0.3]), requires_grad=True)

    def forward(self, x):
        # 检查输入数据
        if not check_tensor_health(x, "网络输入"):
            raise ValueError("输入数据包含异常值！")
        
        # 确保输入在合理范围内
        if x.max() > 10 or x.min() < -10:
            x = torch.clamp(x, min=0.0, max=1.0)
        
        # 提取骨干特征
        layer0 = self.layer0(x)
        check_tensor_health(layer0, "layer0")
        
        layer1 = self.layer1(layer0)
        check_tensor_health(layer1, "layer1")
        
        layer2 = self.layer2(layer1)
        check_tensor_health(layer2, "layer2")
        
        layer3 = self.layer3(layer2)
        check_tensor_health(layer3, "layer3")
        
        layer4 = self.layer4(layer3)
        check_tensor_health(layer4, "layer4")

        # 多尺度边缘检测
        edge_l2 = self.edge_detector_l2(layer2)
        edge_l3 = self.edge_detector_l3(layer3)
        edge_l4 = self.edge_detector_l4(layer4)
        
        # 统一尺寸到layer3
        target_size = layer3.shape[2:]
        edge_l2_up = F.interpolate(edge_l2, size=target_size, mode='bilinear', align_corners=True)
        edge_l4_down = F.adaptive_avg_pool2d(edge_l4, target_size)
        
        # 边缘融合
        edge_fused = (edge_l2_up + edge_l3 + edge_l4_down) / 3.0
        check_tensor_health(edge_fused, "edge_fused")
        
        # 多尺度特征融合
        layer2_up = F.interpolate(layer2, size=target_size, mode='bilinear', align_corners=True)
        layer4_down = F.adaptive_avg_pool2d(layer4, target_size)
        
        multi_scale_features = torch.cat([layer2_up, layer3, layer4_down], dim=1)
        check_tensor_health(multi_scale_features, "multi_scale_features")
        
        fused_features = self.feature_fusion(multi_scale_features)
        check_tensor_health(fused_features, "fused_features")
        
        # 物理特性融合
        physics_features = self.physics_fusion(fused_features, edge_fused)
        
        # 预测
        main_pred = self.main_predict(fused_features)
        edge_pred = self.edge_predict(edge_fused)
        final_pred = self.final_predict(physics_features)
        
        check_tensor_health(main_pred, "main_pred")
        check_tensor_health(edge_pred, "edge_pred")
        check_tensor_health(final_pred, "final_pred")

        # 上采样到原始尺寸
        main_pred = F.interpolate(main_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        edge_pred = F.interpolate(edge_pred, size=x.size()[2:], mode='bilinear', align_corners=True)
        final_pred = F.interpolate(final_pred, size=x.size()[2:], mode='bilinear', align_corners=True)

        # Sigmoid激活
        main_pred = torch.sigmoid(main_pred)
        edge_pred = torch.sigmoid(edge_pred)
        final_pred = torch.sigmoid(final_pred)
        
        check_tensor_health(main_pred, "main_pred_sigmoid")
        check_tensor_health(edge_pred, "edge_pred_sigmoid")
        check_tensor_health(final_pred, "final_pred_sigmoid")

        # 集成预测
        weights = torch.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = weights[0] * main_pred + weights[1] * edge_pred + weights[2] * final_pred
        check_tensor_health(ensemble_pred, "ensemble_pred")

        # CRF后处理 - 修复通道数问题
        try:
            # 确保CRF输入是2通道格式
            if ensemble_pred.size(1) == 1:
                # 将单通道转换为2通道：[背景, 前景]
                background = 1.0 - ensemble_pred
                crf_input = torch.cat([background, ensemble_pred], dim=1)
            else:
                crf_input = ensemble_pred
            
            crf_pred = self.crf(crf_input, x)
            
            # 如果CRF输出是2通道，只取前景通道
            if crf_pred.size(1) == 2:
                crf_pred = crf_pred[:, 1:2]  # 取前景通道
            
            check_tensor_health(crf_pred, "crf_pred")
        except Exception as e:
            crf_pred = ensemble_pred
        
        return {
            'main_pred': main_pred,
            'edge_pred': edge_pred,
            'final_pred': final_pred,
            'ensemble_pred': ensemble_pred,
            'crf_pred': crf_pred,
            'physics_pred': final_pred,  # 兼容性
            'edge_maps': {
                'edge_l2': edge_l2,
                'edge_l3': edge_l3,
                'edge_l4': edge_l4,
                'edge_fused': edge_fused
            }
        }

def create_progressive_glass_physics_model(backbone_path=None, crf_iter=3, trainable_crf=True):
    """创建渐进式玻璃物理特性模型"""
    return GDNetGlassPhysicsProgressive(backbone_path, crf_iter, trainable_crf) 