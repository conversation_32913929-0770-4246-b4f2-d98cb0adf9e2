"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : train_vit_optimized.py
 @Function: 优化ViT玻璃检测的训练脚本

专门针对ViT的训练策略：
1. 分阶段训练：预训练+微调
2. 适应性学习率调度
3. ViT特化的数据增强
4. 多指标验证评估
5. 自适应权重调节
"""
import os
import sys
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
from pathlib import Path
import logging
from tqdm import tqdm
import json
from collections import defaultdict

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from ig_glass.gdnet_vit_optimized import create_vit_optimized_model
from ig_glass.loss_vit_optimized import create_vit_optimized_loss
from ig_glass.glass_mask import GlassDataset


def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'train_vit_{time.strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


class ViTTrainingScheduler:
    """
    ViT特化的训练调度器
    """
    def __init__(self, optimizer, total_epochs, warmup_epochs=5):
        self.optimizer = optimizer
        self.total_epochs = total_epochs
        self.warmup_epochs = warmup_epochs
        self.current_epoch = 0
        
        # 学习率调度
        self.warmup_scheduler = optim.lr_scheduler.LinearLR(
            optimizer, start_factor=0.1, end_factor=1.0, total_iters=warmup_epochs
        )
        
        self.main_scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=total_epochs - warmup_epochs
        )
        
    def step(self):
        """调度器步进"""
        if self.current_epoch < self.warmup_epochs:
            self.warmup_scheduler.step()
        else:
            self.main_scheduler.step()
        self.current_epoch += 1
        
    def get_lr(self):
        """获取当前学习率"""
        return self.optimizer.param_groups[0]['lr']


class ViTMetrics:
    """
    ViT专用评估指标
    """
    def __init__(self):
        self.reset()
        
    def reset(self):
        self.intersection = 0
        self.union = 0
        self.total_pixels = 0
        self.true_positives = 0
        self.false_positives = 0
        self.false_negatives = 0
        
    def update(self, pred, target, threshold=0.5):
        """更新指标"""
        pred_binary = (pred > threshold).float()
        target_binary = target.float()
        
        # IoU计算
        intersection = (pred_binary * target_binary).sum()
        union = pred_binary.sum() + target_binary.sum() - intersection
        
        self.intersection += intersection.item()
        self.union += union.item()
        
        # F1计算
        self.true_positives += intersection.item()
        self.false_positives += (pred_binary * (1 - target_binary)).sum().item()
        self.false_negatives += ((1 - pred_binary) * target_binary).sum().item()
        
        self.total_pixels += target.numel()
        
    def compute(self):
        """计算最终指标"""
        # IoU
        iou = self.intersection / (self.union + 1e-8)
        
        # F1 Score
        precision = self.true_positives / (self.true_positives + self.false_positives + 1e-8)
        recall = self.true_positives / (self.true_positives + self.false_negatives + 1e-8)
        f1 = 2 * precision * recall / (precision + recall + 1e-8)
        
        # Accuracy
        accuracy = (self.true_positives + (self.total_pixels - self.false_positives - self.false_negatives - self.true_positives)) / self.total_pixels
        
        return {
            'iou': iou,
            'f1': f1,
            'precision': precision,
            'recall': recall,
            'accuracy': accuracy
        }


class ViTTrainer:
    """
    ViT训练器
    """
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = setup_logging(args.log_dir)
        
        # 创建模型
        self.model = create_vit_optimized_model(
            backbone_path=args.backbone_path,
            crf_iter=args.crf_iter,
            trainable_crf=args.trainable_crf
        ).to(self.device)
        
        # 创建损失函数
        self.criterion = create_vit_optimized_loss(
            adaptive_weights=args.adaptive_loss
        ).to(self.device)
        
        # 优化器 - ViT需要不同的学习率策略
        self.optimizer = self._create_optimizer()
        
        # 学习率调度器
        self.scheduler = ViTTrainingScheduler(
            self.optimizer, 
            args.epochs, 
            warmup_epochs=args.warmup_epochs
        )
        
        # 数据加载器
        self.train_loader, self.val_loader = self._create_dataloaders()
        
        # 训练状态
        self.best_iou = 0.0
        self.best_epoch = 0
        self.train_history = defaultdict(list)
        
        self.logger.info(f"ViT训练器初始化完成")
        self.logger.info(f"模型参数量: {sum(p.numel() for p in self.model.parameters()) / 1e6:.1f}M")
        
    def _create_optimizer(self):
        """创建优化器 - ViT专用配置"""
        # 分组参数：ViT backbone vs 其他部分
        vit_params = []
        other_params = []
        
        for name, param in self.model.named_parameters():
            if 'vit_backbone' in name:
                vit_params.append(param)
            else:
                other_params.append(param)
        
        # ViT backbone使用较小的学习率
        optimizer = optim.AdamW([
            {'params': vit_params, 'lr': self.args.lr * 0.1, 'weight_decay': 0.05},
            {'params': other_params, 'lr': self.args.lr, 'weight_decay': 0.01}
        ], betas=(0.9, 0.999))
        
        return optimizer
        
    def _create_dataloaders(self):
        """创建数据加载器"""
        # ViT特化的数据增强
        train_transforms = [
            ('resize', (416, 416)),
            ('horizontal_flip', 0.5),
            ('vertical_flip', 0.3),
            ('rotation', 15),
            ('color_jitter', {'brightness': 0.2, 'contrast': 0.2, 'saturation': 0.2}),
            ('normalize', {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]})
        ]
        
        val_transforms = [
            ('resize', (416, 416)),
            ('normalize', {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]})
        ]
        
        train_dataset = GlassDataset(
            self.args.train_data_path,
            transforms=train_transforms,
            mode='train'
        )
        
        val_dataset = GlassDataset(
            self.args.val_data_path,
            transforms=val_transforms,
            mode='val'
        )
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            pin_memory=True
        )
        
        self.logger.info(f"训练数据: {len(train_dataset)} 样本")
        self.logger.info(f"验证数据: {len(val_dataset)} 样本")
        
        return train_loader, val_loader
        
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        train_metrics = ViTMetrics()
        total_loss = 0
        loss_components = defaultdict(float)
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.args.epochs}')
        
        for batch_idx, (images, targets) in enumerate(progress_bar):
            images = images.to(self.device)
            targets = targets.to(self.device)
            
            # 前向传播
            outputs = self.model(images)
            
            # 计算损失
            loss, losses = self.criterion(outputs, targets)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪 - ViT训练稳定性
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 更新指标
            if 'refined_pred' in outputs:
                pred = outputs['refined_pred'][:, 1:2]
            else:
                pred = outputs['ensemble_pred']
                
            train_metrics.update(pred, targets)
            
            # 记录损失
            total_loss += loss.item()
            for key, value in losses.items():
                if isinstance(value, torch.Tensor):
                    loss_components[key] += value.item()
            
            # 更新进度条
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'lr': f'{self.scheduler.get_lr():.6f}'
            })
            
            # 定期打印损失权重（如果使用自适应权重）
            if batch_idx % 100 == 0 and self.args.adaptive_loss:
                weights = self.criterion.get_loss_weights()
                self.logger.info(f"Epoch {epoch+1}, Batch {batch_idx}, Loss weights: {weights}")
        
        # 计算epoch指标
        epoch_metrics = train_metrics.compute()
        avg_loss = total_loss / len(self.train_loader)
        
        # 记录训练历史
        self.train_history['train_loss'].append(avg_loss)
        self.train_history['train_iou'].append(epoch_metrics['iou'])
        self.train_history['train_f1'].append(epoch_metrics['f1'])
        
        self.logger.info(f"训练 Epoch {epoch+1}: "
                        f"Loss={avg_loss:.4f}, "
                        f"IoU={epoch_metrics['iou']:.4f}, "
                        f"F1={epoch_metrics['f1']:.4f}")
        
        return avg_loss, epoch_metrics
        
    def validate(self, epoch):
        """验证"""
        self.model.eval()
        val_metrics = ViTMetrics()
        total_loss = 0
        
        with torch.no_grad():
            for images, targets in tqdm(self.val_loader, desc='Validation'):
                images = images.to(self.device)
                targets = targets.to(self.device)
                
                # 前向传播
                outputs = self.model(images)
                
                # 计算损失
                loss, _ = self.criterion(outputs, targets)
                total_loss += loss.item()
                
                # 更新指标
                if 'refined_pred' in outputs:
                    pred = outputs['refined_pred'][:, 1:2]
                else:
                    pred = outputs['ensemble_pred']
                    
                val_metrics.update(pred, targets)
        
        # 计算验证指标
        epoch_metrics = val_metrics.compute()
        avg_loss = total_loss / len(self.val_loader)
        
        # 记录验证历史
        self.train_history['val_loss'].append(avg_loss)
        self.train_history['val_iou'].append(epoch_metrics['iou'])
        self.train_history['val_f1'].append(epoch_metrics['f1'])
        
        self.logger.info(f"验证 Epoch {epoch+1}: "
                        f"Loss={avg_loss:.4f}, "
                        f"IoU={epoch_metrics['iou']:.4f}, "
                        f"F1={epoch_metrics['f1']:.4f}")
        
        # 保存最佳模型
        if epoch_metrics['iou'] > self.best_iou:
            self.best_iou = epoch_metrics['iou']
            self.best_epoch = epoch + 1
            self.save_model('best_model.pth', epoch, epoch_metrics)
            self.logger.info(f"🎉 新的最佳IoU: {self.best_iou:.4f}")
        
        return avg_loss, epoch_metrics
        
    def save_model(self, filename, epoch, metrics):
        """保存模型"""
        save_path = os.path.join(self.args.save_dir, filename)
        os.makedirs(self.args.save_dir, exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': {
                'current_epoch': self.scheduler.current_epoch
            },
            'metrics': metrics,
            'best_iou': self.best_iou,
            'train_history': dict(self.train_history),
            'args': vars(self.args)
        }
        
        torch.save(checkpoint, save_path)
        self.logger.info(f"模型已保存: {save_path}")
        
    def train(self):
        """完整训练流程"""
        self.logger.info("开始ViT训练...")
        
        for epoch in range(self.args.epochs):
            # 训练
            train_loss, train_metrics = self.train_epoch(epoch)
            
            # 验证
            val_loss, val_metrics = self.validate(epoch)
            
            # 更新学习率
            self.scheduler.step()
            
            # 定期保存检查点
            if (epoch + 1) % self.args.save_interval == 0:
                self.save_model(f'checkpoint_epoch_{epoch+1}.pth', epoch, val_metrics)
            
            # 早停检查
            if epoch - self.best_epoch > self.args.patience:
                self.logger.info(f"早停触发，最佳IoU: {self.best_iou:.4f} (Epoch {self.best_epoch})")
                break
        
        # 保存训练历史
        history_path = os.path.join(self.args.save_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            json.dump(dict(self.train_history), f, indent=2)
        
        self.logger.info(f"训练完成！最佳IoU: {self.best_iou:.4f} (Epoch {self.best_epoch})")


def main():
    parser = argparse.ArgumentParser(description='ViT优化玻璃检测训练')
    
    # 数据参数
    parser.add_argument('--train_data_path', type=str, required=True, help='训练数据路径')
    parser.add_argument('--val_data_path', type=str, required=True, help='验证数据路径')
    parser.add_argument('--batch_size', type=int, default=8, help='批次大小')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载器线程数')
    
    # 模型参数
    parser.add_argument('--backbone_path', type=str, default=None, help='预训练骨干网络路径')
    parser.add_argument('--crf_iter', type=int, default=6, help='CRF迭代次数')
    parser.add_argument('--trainable_crf', type=bool, default=True, help='CRF是否可训练')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=200, help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    parser.add_argument('--warmup_epochs', type=int, default=5, help='预热轮数')
    parser.add_argument('--patience', type=int, default=20, help='早停耐心值')
    
    # 损失函数参数
    parser.add_argument('--adaptive_loss', type=bool, default=True, help='是否使用自适应损失权重')
    
    # 保存参数
    parser.add_argument('--save_dir', type=str, default='./checkpoints_vit', help='模型保存目录')
    parser.add_argument('--log_dir', type=str, default='./logs_vit', help='日志保存目录')
    parser.add_argument('--save_interval', type=int, default=10, help='模型保存间隔')
    
    args = parser.parse_args()
    
    # 创建训练器并开始训练
    trainer = ViTTrainer(args)
    trainer.train()


if __name__ == '__main__':
    main()