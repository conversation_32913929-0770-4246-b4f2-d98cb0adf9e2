import os
import time
import cv2
import numpy as np
import ig_glass.misc as misc
import random

def evaluate_results(output_folder,gt_folder):
    # 获取所有图像文件
    image_files = [f for f in os.listdir(output_folder) if f != 'temp.png']
    # 打乱图像文件列表
    random.shuffle(image_files)
    # 计算每组的大致数量
    group_size = len(image_files) // 3
    # 分割成 6 组
    groups = [image_files[i*group_size:(i+1)*group_size] for i in range(3)]
    if len(image_files) % 3 != 0:
        # 处理剩余的文件
        remaining = image_files[3*group_size:]
        for i, file in enumerate(remaining):
            groups[i].append(file)

    all_results = []
    for group in groups:
        count = 0
        iou, acc, fm, mae, ber,aber = 0, 0, 0, 0,0,0 
        for image_file in group:
            temp_name = image_file.split('.')[0]
            image_path = os.path.join(output_folder, image_file)
            gt_name = temp_name + '.png'
            gt_path = os.path.join(gt_folder, gt_name)
            image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
            prediction = image.astype(np.float32) / 255.0
            gt = gt_mask.astype(np.float32) / 255.0
            tiou = misc.compute_iou(prediction, gt)
            tacc = misc.compute_acc(prediction, gt)
            precision, recall = misc.compute_precision_recall(prediction, gt)
            tfm = misc.compute_fmeasure(precision, recall)
            tmae = misc.compute_mae(prediction, gt)
            tber = misc.compute_ber(prediction, gt)
            taber = misc.compute_aber(prediction,gt)
            count += 1
            iou += tiou
            acc += tacc
            fm += tfm
            mae += tmae
            ber += tber
            aber +=taber
        if count > 0:
            iou = iou / count
            acc = acc / count
            fm = fm / count
            mae = mae / count
            ber = ber / count
            aber = aber/count
        all_results.append((iou, acc, fm, mae, ber,aber))
    #all results / len(groups) get avg results
    avg_iou = sum([result[0] for result in all_results]) / len(all_results)
    avg_acc = sum([result[1] for result in all_results]) / len(all_results)
    avg_fm = sum([result[2] for result in all_results]) / len(all_results)
    avg_mae = sum([result[3] for result in all_results]) / len(all_results)
    avg_ber = sum([result[4] for result in all_results]) / len(all_results)
    avg_aber = sum([result[5] for result in all_results]) / len(all_results)

    return avg_iou, avg_acc, avg_fm, avg_mae, avg_ber,avg_aber


def evaluate_ber(output_folder,gt_folder):
    # 获取所有图像文件
    image_files = [f for f in os.listdir(output_folder) if f != 'temp.png']
    # 打乱图像文件列表
    random.shuffle(image_files)
    # 计算每组的大致数量
    group_size = len(image_files) // 3
    # 分割成 6 组
    groups = [image_files[i*group_size:(i+1)*group_size] for i in range(3)]
    if len(image_files) % 3 != 0:
        # 处理剩余的文件
        remaining = image_files[3*group_size:]
        for i, file in enumerate(remaining):
            groups[i].append(file)

    all_results = []
    for group in groups:
        count = 0
        iou, acc, fm, mae, ber,aber = 0, 0, 0, 0,0,0 
        for image_file in group:
            temp_name = image_file.split('.')[0]
            image_path = os.path.join(output_folder, image_file)
            gt_name = temp_name + '.png'
            gt_path = os.path.join(gt_folder, gt_name)
            image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
            prediction = image.astype(np.float32) / 255.0
            gt = gt_mask.astype(np.float32) / 255.0
            # tiou = misc.compute_iou(prediction, gt)
            # tacc = misc.compute_acc(prediction, gt)
            # precision, recall = misc.compute_precision_recall(prediction, gt)
            # tfm = misc.compute_fmeasure(precision, recall)
            # tmae = misc.compute_mae(prediction, gt)
            tber = misc.compute_ber(prediction, gt)
            taber = misc.compute_aber(prediction,gt)
            count += 1
            # iou += tiou
            # acc += tacc
            # fm += tfm
            # mae += tmae
            ber += tber
            aber +=taber
        if count > 0:
            # iou = iou / count
            # acc = acc / count
            # fm = fm / count
            # mae = mae / count
            ber = ber / count
            aber = aber/count
        all_results.append((ber,aber))
    #all results / len(groups) get avg results
    # avg_iou = sum([result[0] for result in all_results]) / len(all_results)
    # avg_acc = sum([result[1] for result in all_results]) / len(all_results)
    # avg_fm = sum([result[2] for result in all_results]) / len(all_results)
    # avg_mae = sum([result[3] for result in all_results]) / len(all_results)
    avg_ber = sum([result[0] for result in all_results]) / len(all_results)
    avg_aber = sum([result[1] for result in all_results]) / len(all_results)

    return avg_ber,avg_aber

def main():
    data_path = "/home/<USER>/Documents/ig_slam_maskdata/new_trainset"
    pred_path = ["200_mask","rere300_mask","glass_mask_crf","glass_mask_best"]
    gt_floder = data_path + '/mask'
    for temp in pred_path:
        output_folder = data_path + '/' + temp
        print(f"Results for {temp}:")
        avg_ber,avg_aber = evaluate_ber(output_folder, gt_floder)
        print(f"BER: {avg_ber}")
        print(f"ABER: {avg_aber}") 

if __name__ == '__main__':
    main()