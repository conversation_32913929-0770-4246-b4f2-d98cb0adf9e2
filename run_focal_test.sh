#!/bin/bash
# 运行Focal Loss与IoU Loss权重比例测试脚本

# 基本测试 - 使用默认参数
echo "运行测试1: 默认参数 (Focal Loss权重=0.6, IoU Loss权重=0.4)"
python train_lightcrf.py --use_focal=True --bce_weight=0.6 --iou_weight=0.4 --focal_alpha=0.25 --focal_gamma=2.0

# 测试2 - 增加IoU权重
echo "运行测试2: 增加IoU权重 (Focal Loss权重=0.5, IoU Loss权重=0.5)"
python train_lightcrf.py --use_focal=True --bce_weight=0.5 --iou_weight=0.5 --focal_alpha=0.25 --focal_gamma=2.0

# 测试3 - 调整gamma参数
echo "运行测试3: 增加gamma参数 (Focal Loss权重=0.6, IoU Loss权重=0.4, gamma=3.0)"
python train_lightcrf.py --use_focal=True --bce_weight=0.6 --iou_weight=0.4 --focal_alpha=0.25 --focal_gamma=3.0

# 测试4 - 调整alpha参数
echo "运行测试4: 调整alpha参数 (Focal Loss权重=0.6, IoU Loss权重=0.4, alpha=0.3)"
python train_lightcrf.py --use_focal=True --bce_weight=0.6 --iou_weight=0.4 --focal_alpha=0.3 --focal_gamma=2.0

# 测试5 - 对比BCE Loss与Focal Loss
echo "运行测试5: 使用BCE Loss代替Focal Loss (BCE权重=0.6, IoU Loss权重=0.4)"
python train_lightcrf.py --use_focal=False --bce_weight=0.6 --iou_weight=0.4

# 测试6 - Focal Loss为主
echo "运行测试6: Focal Loss为主 (Focal Loss权重=0.7, IoU Loss权重=0.3)"
python train_lightcrf.py --use_focal=True --bce_weight=0.7 --iou_weight=0.3 --focal_alpha=0.25 --focal_gamma=2.0

# 测试7 - IoU Loss为主
echo "运行测试7: IoU Loss为主 (Focal Loss权重=0.3, IoU Loss权重=0.7)"
python train_lightcrf.py --use_focal=True --bce_weight=0.3 --iou_weight=0.7 --focal_alpha=0.25 --focal_gamma=2.0 