#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced SCSA Training Script for 90%+ IoU Performance
基于87.95% IoU的成功经验，进一步优化训练策略

主要改进：
1. 优化的数据增强策略
2. 自适应学习率调度
3. 渐进式训练策略
4. 性能监控和早停
5. 模型集成策略

目标：突破90% IoU
"""

import os
import sys
import time
import random
import argparse
import numpy as np
from datetime import datetime

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")

# 添加项目路径
sys.path.append('ig_glass')

from gdnet_enhanced_scsa import create_enhanced_scsa_model
from loss_enhanced_scsa import create_enhanced_scsa_loss
from glass_dataloader import GlassDataLoader


class EnhancedSCSATrainer:
    """增强版SCSA训练器，目标90%+ IoU"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 设置随机种子
        self._set_seed(args.seed)
        
        # 初始化模型和损失函数
        self._init_model()
        self._init_optimizer()
        self._init_scheduler()
        
        # 初始化数据加载器
        self._init_dataloaders()
        
        # 初始化日志
        self._init_logging()
        
        # 性能跟踪
        self.best_iou = 0.0
        self.best_epoch = 0
        self.patience_counter = 0
        
        print(f"🚀 Enhanced SCSA训练器初始化完成")
        print(f"   设备: {self.device}")
        print(f"   参数量: {sum(p.numel() for p in self.model.parameters())/1e6:.1f}M")
        print(f"   目标: 90%+ IoU")
    
    def _set_seed(self, seed):
        """设置随机种子"""
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    def _init_model(self):
        """初始化模型"""
        self.model = create_enhanced_scsa_model(
            backbone_path=self.args.backbone_path,
            crf_iter=self.args.crf_iter,
            trainable_crf=True
        ).to(self.device)
        
        # 损失函数
        self.criterion = create_enhanced_scsa_loss(
            focal_weight=0.15,      # 降低focal权重
            iou_weight=0.65,        # 增加IoU权重
            edge_weight=0.15,       # 保持边缘权重
            consistency_weight=0.05, # 一致性权重
            adaptive=True           # 启用自适应权重
        )
    
    def _init_optimizer(self):
        """初始化优化器"""
        # 分层学习率
        backbone_params = []
        head_params = []
        
        for name, param in self.model.named_parameters():
            if 'layer' in name:  # backbone参数
                backbone_params.append(param)
            else:  # head参数
                head_params.append(param)
        
        self.optimizer = optim.AdamW([
            {'params': backbone_params, 'lr': self.args.lr * 0.1, 'weight_decay': 1e-4},
            {'params': head_params, 'lr': self.args.lr, 'weight_decay': 1e-5}
        ], lr=self.args.lr, betas=(0.9, 0.999), eps=1e-8)
    
    def _init_scheduler(self):
        """初始化学习率调度器"""
        # 使用CosineAnnealingWarmRestarts
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=2, eta_min=1e-6
        )
        
        # 额外的ReduceLROnPlateau用于性能监控
        self.plateau_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
    
    def _init_dataloaders(self):
        """初始化数据加载器"""
        # 训练数据加载器 - 增强版数据增强
        self.train_loader = DataLoader(
            GlassDataLoader(
                mode='train',
                augment_data=True,
                target_size=self.args.input_size,
                glass_augmentation='aggressive'  # 使用更激进的增强
            ),
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        # 验证数据加载器
        self.val_loader = DataLoader(
            GlassDataLoader(
                mode='test',
                augment_data=False,
                target_size=self.args.input_size
            ),
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            pin_memory=True
        )
    
    def _init_logging(self):
        """初始化日志"""
        log_dir = f"runs/enhanced_scsa_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.writer = SummaryWriter(log_dir)
        
        # 创建检查点目录
        self.checkpoint_dir = f"checkpoints/enhanced_scsa"
        os.makedirs(self.checkpoint_dir, exist_ok=True)
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_iou = 0.0
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.args.epochs}')
        
        for batch_idx, (images, targets) in enumerate(pbar):
            images = images.to(self.device)
            targets = targets.to(self.device)
            
            # 前向传播
            predictions = self.model(images)
            
            # 计算性能指标用于自适应权重
            with torch.no_grad():
                pred_binary = (predictions['ensemble_pred'] > 0.5).float()
                intersection = torch.sum(pred_binary * targets, dim=(2, 3))
                union = torch.sum(pred_binary, dim=(2, 3)) + torch.sum(targets, dim=(2, 3)) - intersection
                iou = torch.mean(intersection / (union + 1e-7))
                
                performance_metrics = {
                    'iou': iou.item(),
                    'epoch': epoch,
                    'batch': batch_idx
                }
            
            # 计算损失
            loss_dict = self.criterion(predictions, targets, performance_metrics)
            loss = loss_dict['total_loss']
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 更新统计
            total_loss += loss.item()
            total_iou += iou.item()
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'IoU': f'{iou.item():.4f}',
                'LR': f'{self.optimizer.param_groups[0]["lr"]:.2e}'
            })
            
            # 记录到tensorboard
            if batch_idx % 50 == 0:
                step = epoch * len(self.train_loader) + batch_idx
                self.writer.add_scalar('Train/Loss', loss.item(), step)
                self.writer.add_scalar('Train/IoU', iou.item(), step)
                self.writer.add_scalar('Train/LR', self.optimizer.param_groups[0]['lr'], step)
                
                # 记录各项损失
                for key, value in loss_dict.items():
                    if key != 'total_loss' and torch.is_tensor(value):
                        self.writer.add_scalar(f'Train/{key}', value.item(), step)
        
        # 更新学习率
        self.scheduler.step()
        
        avg_loss = total_loss / len(self.train_loader)
        avg_iou = total_iou / len(self.train_loader)
        
        return avg_loss, avg_iou
    
    def validate(self, epoch):
        """验证"""
        self.model.eval()
        total_loss = 0.0
        total_iou = 0.0
        
        with torch.no_grad():
            for images, targets in tqdm(self.val_loader, desc='Validating'):
                images = images.to(self.device)
                targets = targets.to(self.device)
                
                predictions = self.model(images)
                
                # 计算损失
                loss_dict = self.criterion(predictions, targets)
                loss = loss_dict['total_loss']
                
                # 计算IoU
                pred_binary = (predictions['ensemble_pred'] > 0.5).float()
                intersection = torch.sum(pred_binary * targets, dim=(2, 3))
                union = torch.sum(pred_binary, dim=(2, 3)) + torch.sum(targets, dim=(2, 3)) - intersection
                iou = torch.mean(intersection / (union + 1e-7))
                
                total_loss += loss.item()
                total_iou += iou.item()
        
        avg_loss = total_loss / len(self.val_loader)
        avg_iou = total_iou / len(self.val_loader)
        
        # 记录到tensorboard
        self.writer.add_scalar('Val/Loss', avg_loss, epoch)
        self.writer.add_scalar('Val/IoU', avg_iou, epoch)
        
        # 更新plateau scheduler
        self.plateau_scheduler.step(avg_iou)
        
        return avg_loss, avg_iou
    
    def save_checkpoint(self, epoch, iou, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_iou': self.best_iou,
            'iou': iou
        }
        
        # 保存最新检查点
        torch.save(checkpoint, os.path.join(self.checkpoint_dir, 'latest.pth'))
        
        # 保存最佳检查点
        if is_best:
            torch.save(checkpoint, os.path.join(self.checkpoint_dir, 'best.pth'))
            print(f"💾 保存最佳模型，IoU: {iou:.4f}")
    
    def train(self):
        """主训练循环"""
        print("🚀 开始训练，目标90%+ IoU")
        
        for epoch in range(self.args.epochs):
            # 训练
            train_loss, train_iou = self.train_epoch(epoch)
            
            # 验证
            val_loss, val_iou = self.validate(epoch)
            
            # 打印结果
            print(f"Epoch {epoch+1}/{self.args.epochs}")
            print(f"  Train - Loss: {train_loss:.4f}, IoU: {train_iou:.4f}")
            print(f"  Val   - Loss: {val_loss:.4f}, IoU: {val_iou:.4f}")
            
            # 检查是否是最佳模型
            is_best = val_iou > self.best_iou
            if is_best:
                self.best_iou = val_iou
                self.best_epoch = epoch
                self.patience_counter = 0
                
                # 如果达到90%，特别标记
                if val_iou >= 0.90:
                    print(f"🎉 突破90% IoU！当前IoU: {val_iou:.4f}")
            else:
                self.patience_counter += 1
            
            # 保存检查点
            self.save_checkpoint(epoch, val_iou, is_best)
            
            # 早停检查
            if self.patience_counter >= self.args.patience:
                print(f"早停触发，最佳IoU: {self.best_iou:.4f} (Epoch {self.best_epoch+1})")
                break
        
        print(f"✅ 训练完成！最佳IoU: {self.best_iou:.4f}")
        self.writer.close()


def main():
    parser = argparse.ArgumentParser(description='Enhanced SCSA Training for 90%+ IoU')
    parser.add_argument('--backbone_path', type=str, 
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth',
                       help='Backbone pretrained weights path')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--input_size', type=int, default=416, help='Input image size')
    parser.add_argument('--num_workers', type=int, default=4, help='Number of workers')
    parser.add_argument('--crf_iter', type=int, default=5, help='CRF iterations')
    parser.add_argument('--patience', type=int, default=15, help='Early stopping patience')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    # 创建训练器并开始训练
    trainer = EnhancedSCSATrainer(args)
    trainer.train()


if __name__ == '__main__':
    main()
