"""
基于玻璃物理特性的测试脚本
"""
import os
import argparse
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torchvision import transforms
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import cv2
from torch.autograd import Variable
from ig_glass.misc import *
from ig_glass.gdnet_glass_physics_progressive import create_progressive_glass_physics_model

# Device setup
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# Paths
backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth'

# Parameter set
ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/models_glass_physics/IG-SLAM-GlassPhysics'
exp_name = 'IG-SLAM-GlassPhysics'
args = {
    'snapshot': 'best_model_loss0.1611_iou0.8762_mae0.0541',  # 第60轮最佳模型
    'scale': 416,
    'crf': False,  # 使用CRF后处理
    'glass_threshold': 0.5,  # 玻璃区域检测阈值
    'crf_iter': 3,  # CRF迭代次数
    'bilateral_weight': 8.0,  # 双边滤波权重
    'gaussian_weight': 4.0,  # 高斯滤波权重
    'bilateral_spatial_sigma': 35.0,  # 双边滤波空间sigma
    'bilateral_color_sigma': 2.5,  # 双边滤波颜色sigma
    'gaussian_sigma': 1.2,  # 高斯滤波sigma
}

# 预处理
img_transform = transforms.Compose([
    transforms.Resize((args['scale'], args['scale'])),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

to_pil = transforms.ToPILImage()


def parse_args():
    parser = argparse.ArgumentParser(description='玻璃物理特性模型测试')
    
    parser.add_argument('--model-path', type=str, required=True,
                        help='模型权重路径')
    parser.add_argument('--test-dir', type=str, default='./test_images',
                        help='测试图像目录')
    parser.add_argument('--output-dir', type=str, default='./results_physics',
                        help='结果保存目录')
    parser.add_argument('--device', type=str, default='cuda:0',
                        help='设备')
    parser.add_argument('--crf-iter', type=int, default=3,
                        help='CRF迭代次数')
    parser.add_argument('--save-physics-maps', action='store_true',
                        help='是否保存物理特性图')
    
    return parser.parse_args()


def load_model(model_path, device, crf_iter=3):
    """加载模型"""
    print(f'加载模型: {model_path}')
    
    # 创建模型
    model = create_progressive_glass_physics_model(
        backbone_path=None,
        crf_iter=crf_iter,
        trainable_crf=False  # 测试时固定CRF参数
    )
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    model.eval()
    
    return model


def preprocess_image(image_path, target_size=(256, 256)):
    """预处理图像"""
    transform = transforms.Compose([
        transforms.Resize(target_size),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    image = Image.open(image_path).convert('RGB')
    original_size = image.size
    image_tensor = transform(image).unsqueeze(0)
    
    return image_tensor, original_size


def save_results(image_name, outputs, original_size, output_dir, save_physics_maps=False):
    """保存结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取主要预测结果
    if isinstance(outputs, dict):
        final_pred = outputs['ensemble_pred']
        if 'refined_pred' in outputs and outputs['refined_pred'].size(1) > 1:
            refined_pred = torch.softmax(outputs['refined_pred'], dim=1)[:, 1:2]  # 取前景通道
        else:
            refined_pred = final_pred
    else:
        # 如果是简单的tensor输出
        final_pred = outputs
        refined_pred = outputs
    
    # 上采样到原始尺寸
    final_pred = F.interpolate(final_pred, size=original_size[::-1], mode='bilinear', align_corners=True)
    refined_pred = F.interpolate(refined_pred, size=original_size[::-1], mode='bilinear', align_corners=True)
    
    # 转换为numpy
    final_pred_np = final_pred.squeeze().cpu().numpy()
    refined_pred_np = refined_pred.squeeze().cpu().numpy()
    
    # 保存预测结果
    base_name = os.path.splitext(image_name)[0]
    
    # 保存最终预测
    final_pred_img = Image.fromarray((final_pred_np * 255).astype(np.uint8))
    final_pred_img.save(os.path.join(output_dir, f'{base_name}_final.png'))
    
    # 保存CRF精化结果
    refined_pred_img = Image.fromarray((refined_pred_np * 255).astype(np.uint8))
    refined_pred_img.save(os.path.join(output_dir, f'{base_name}_refined.png'))
    
    # 保存物理特性图（如果需要）
    if save_physics_maps and isinstance(outputs, dict) and 'physics_maps' in outputs:
        physics_maps = outputs['physics_maps']
        physics_dir = os.path.join(output_dir, 'physics_maps')
        os.makedirs(physics_dir, exist_ok=True)
        
        for map_name, map_tensor in physics_maps.items():
            map_tensor = F.interpolate(map_tensor, size=original_size[::-1], mode='bilinear', align_corners=True)
            map_np = map_tensor.squeeze().cpu().numpy()
            map_img = Image.fromarray((map_np * 255).astype(np.uint8))
            map_img.save(os.path.join(physics_dir, f'{base_name}_{map_name}.png'))
    
    print(f'结果已保存: {base_name}')


def create_visualization(image_path, outputs, output_dir, image_name):
    """创建可视化对比图"""
    # 读取原始图像
    original_img = Image.open(image_path).convert('RGB')
    
    # 提取预测结果
    if isinstance(outputs, dict):
        final_pred = outputs['ensemble_pred'].squeeze().cpu().numpy()
        if 'physics_maps' in outputs:
            physics_maps = outputs['physics_maps']
        else:
            physics_maps = {}
    else:
        final_pred = outputs.squeeze().cpu().numpy()
        physics_maps = {}
    
    # 创建子图
    num_maps = len(physics_maps)
    fig, axes = plt.subplots(2, max(3, (num_maps + 2) // 2), figsize=(15, 8))
    axes = axes.flatten()
    
    # 显示原始图像
    axes[0].imshow(original_img)
    axes[0].set_title('原始图像')
    axes[0].axis('off')
    
    # 显示最终预测
    axes[1].imshow(final_pred, cmap='gray')
    axes[1].set_title('玻璃检测结果')
    axes[1].axis('off')
    
    # 显示物理特性图
    for i, (map_name, map_tensor) in enumerate(physics_maps.items()):
        if i + 2 < len(axes):
            map_np = map_tensor.squeeze().cpu().numpy()
            axes[i + 2].imshow(map_np, cmap='viridis')
            axes[i + 2].set_title(f'{map_name}特性')
            axes[i + 2].axis('off')
    
    # 隐藏多余的子图
    for j in range(len(physics_maps) + 2, len(axes)):
        axes[j].axis('off')
    
    plt.tight_layout()
    
    # 保存可视化结果
    base_name = os.path.splitext(image_name)[0]
    viz_path = os.path.join(output_dir, f'{base_name}_visualization.png')
    plt.savefig(viz_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f'可视化已保存: {viz_path}')


def path_set(tempdata_path, new_path):
    results_root = os.path.join(tempdata_path, new_path)
    return results_root


def verify_model_weights(model):
    """验证模型权重是否正确加载"""
    try:
        # 检查backbone权重是否为随机初始化
        conv1_weight = model.layer0[0].weight
        weight_std = conv1_weight.std().item()
        weight_mean = conv1_weight.mean().item()
        
        print(f"🔍 权重验证:")
        print(f"   - Conv1权重标准差: {weight_std:.6f}")
        print(f"   - Conv1权重均值: {weight_mean:.6f}")
        
        # 如果标准差太接近随机初始化的值，说明可能有问题
        if 0.01 < weight_std < 0.1 and abs(weight_mean) < 0.01:
            print("✅ 权重看起来是训练好的")
            return True
        else:
            print("⚠️  权重可能是随机初始化的")
            return False
    except Exception as e:
        print(f"❌ 权重验证失败: {e}")
        return False


def detect_glass_and_evaluate(image_folder, output_folder, gt_folder, model, glass_threshold):
    """检测玻璃区域并评估结果"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]

    count = 0
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0

    # 用于性能测量
    inference_times = []

    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)

        try:
            # 读取图像
            img_pil = Image.open(image_path)
            if img_pil.mode != 'RGB':
                img_pil = img_pil.convert('RGB')
            w, h = img_pil.size

            # 预测
            img_var = Variable(img_transform(img_pil).unsqueeze(0)).cuda(device_ids[0])

            # 测量推理时间
            start_time = torch.cuda.Event(enable_timing=True)
            end_time = torch.cuda.Event(enable_timing=True)

            start_time.record()
            with torch.no_grad():
                outputs = model(img_var)
                # 提取CRF精炼后的预测结果
                if isinstance(outputs, dict):
                    crf_pred = outputs['crf_pred']
                else:
                    crf_pred = outputs
            end_time.record()

            # 等待GPU操作完成
            torch.cuda.synchronize()
            inference_time = start_time.elapsed_time(end_time) / 1000.0  # 转换为秒
            inference_times.append(inference_time)

            # 处理CRF输出
            if crf_pred.size(1) == 2:
                # 如果是2通道输出，取前景通道
                glass_predict = crf_pred[:, 1:2]
            else:
                # 单通道输出
                glass_predict = crf_pred

            # 转换预测结果
            glass_predict = glass_predict.data.squeeze(0).cpu()
            glass_predict_np = np.array(transforms.Resize((h, w))(to_pil(glass_predict)))

            # 二值化预测结果
            glass_mask = (glass_predict_np > glass_threshold * 255).astype(np.uint8) * 255

            # 保存结果
            temp_name = image_file.split('.')[0]
            if '.png' in image_file:
                result_name = image_file
            else:
                result_name = temp_name + '.png'
            cv2.imwrite(os.path.join(output_folder, result_name), glass_mask)

            # 评估结果
            if '.png' in image_file:
                gt_name = image_file
            else:
                gt_name = temp_name + '.png'
            gt_path = os.path.join(gt_folder, gt_name)

            if os.path.exists(gt_path):
                gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)

                # 确保尺寸一致
                if gt_mask.shape != glass_mask.shape:
                    gt_mask = cv2.resize(gt_mask, (glass_mask.shape[1], glass_mask.shape[0]))

                # 计算评估指标
                prediction = glass_mask.astype(np.float32) / 255.0
                gt = gt_mask.astype(np.float32) / 255.0

                tiou = compute_iou(prediction, gt)
                tacc = compute_acc(prediction, gt)
                precision, recall = compute_precision_recall(prediction, gt)
                tfm = compute_fmeasure(precision, recall)
                tmae = compute_mae(prediction, gt)
                tber = compute_ber(prediction, gt)
                taber = compute_aber(prediction, gt)

                count += 1
                iou += tiou
                acc += tacc
                fm += tfm
                mae += tmae
                ber += tber
                aber += taber

        except Exception as e:
            print(f"❌ 处理图像 {image_file} 时出错: {e}")
            continue

    if count > 0:
        iou = iou / count
        acc = acc / count
        fm = fm / count
        mae = mae / count
        ber = ber / count
        aber = aber / count

    # 计算平均推理时间（跳过前几次作为预热）
    if len(inference_times) > 5:
        avg_inference_time = np.mean(inference_times[5:])
        fps = 1.0 / avg_inference_time
        print(f"\nAverage inference time: {avg_inference_time*1000:.2f} ms")
        print(f"FPS: {fps:.2f}")

    return iou, acc, fm, mae, ber, aber


def verify_weight_overwrite():
    """验证权重是否被正确覆盖"""
    # 创建两个相同的模型
    model1 = create_progressive_glass_physics_model(
        backbone_path='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth',
        crf_iter=3, trainable_crf=False
    )
    
    model2 = create_progressive_glass_physics_model(
        backbone_path=None,  # 不加载预训练权重
        crf_iter=3, trainable_crf=False
    )
    
    # 加载相同的checkpoint到两个模型
    checkpoint = torch.load('/home/<USER>/ws/IG_SLAM/ig_glass/models_glass_physics/IG-SLAM-GlassPhysics/best_model_loss0.1611_iou0.8762_mae0.0541.pth')
    model1.load_state_dict(checkpoint['model_state_dict'], strict=False)
    model2.load_state_dict(checkpoint['model_state_dict'], strict=False)
    
    # 比较关键层的权重
    layer1_weight1 = model1.layer1[0].conv1.weight
    layer1_weight2 = model2.layer1[0].conv1.weight
    
    if torch.allclose(layer1_weight1, layer1_weight2, atol=1e-6):
        print("✅ 权重完全一致！checkpoint权重覆盖了预训练权重")
        return True
    else:
        print("❌ 权重不一致！存在问题")
        return False


def main():
    print("🚀 开始渐进式玻璃物理检测网络测试")
    verify_weight_overwrite()
    # 加载模型 - 关键修复：测试时不加载backbone预训练权重
    print("📋 创建模型...")
    try:
        model = create_progressive_glass_physics_model(
            backbone_path=None,  # ✅ 测试时不加载ImageNet预训练权重
            crf_iter=args['crf_iter'], 
            trainable_crf=False
        )
        print("✅ 模型创建成功")
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return

    # 设置CRF参数
    if hasattr(model, 'crf'):
        try:
            if hasattr(model.crf, 'bilateral_weight'):
                model.crf.bilateral_weight = torch.nn.Parameter(torch.tensor(args['bilateral_weight']))
                model.crf.gaussian_weight = torch.nn.Parameter(torch.tensor(args['gaussian_weight']))
                model.crf.bilateral_spatial_sigma = torch.nn.Parameter(torch.tensor(args['bilateral_spatial_sigma']))
                model.crf.bilateral_color_sigma = torch.nn.Parameter(torch.tensor(args['bilateral_color_sigma']))
                model.crf.gaussian_sigma = torch.nn.Parameter(torch.tensor(args['gaussian_sigma']))
                print("✅ CRF参数设置成功")
        except Exception as e:
            print(f"⚠️  CRF参数设置失败: {e}")

    # 加载训练好的模型权重
    print("📋 加载训练好的模型权重...")
    try:
        if args['snapshot'].startswith('best_model'):
            # 查找最新的best_model文件
            model_files = [f for f in os.listdir(ckpt_path) if f.startswith('best_model')]
            if model_files:
                model_path = os.path.join(ckpt_path, sorted(model_files)[-1])  # 获取最新的
                print(f"🔍 找到最佳模型: {model_path}")
            else:
                print("❌ 未找到best_model文件")
                return
        else:
            # 加载指定的模型
            model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
            if not os.path.exists(model_path):
                print(f"❌ 模型文件不存在: {model_path}")
                return
            print(f"🔍 加载指定模型: {model_path}")

        # 加载checkpoint
        checkpoint = torch.load(model_path, map_location='cpu')
        print(f"✅ Checkpoint加载成功")
        
        # 智能权重加载
        if 'model_state_dict' in checkpoint:
            missing_keys, unexpected_keys = model.load_state_dict(checkpoint['model_state_dict'], strict=False)
            print("✅ 加载model_state_dict成功")
        elif 'model' in checkpoint:
            missing_keys, unexpected_keys = model.load_state_dict(checkpoint['model'], strict=False)
            print("✅ 加载model成功")
        else:
            missing_keys, unexpected_keys = model.load_state_dict(checkpoint, strict=False)
            print("✅ 加载checkpoint成功")
            
        # 报告权重加载状态
        if missing_keys:
            print(f"⚠️  缺失的权重: {len(missing_keys)} 个")
        if unexpected_keys:
            print(f"⚠️  意外的权重: {len(unexpected_keys)} 个")
            
        # 显示模型信息
        if isinstance(checkpoint, dict) and 'epoch' in checkpoint:
            epoch = checkpoint.get('epoch', 'unknown')
            print(f"📊 模型来自第 {epoch} 轮")

    except Exception as e:
        print(f"❌ 权重加载失败: {e}")
        return

    # 移动模型到GPU
    try:
        model.cuda(device_ids[0])
        model.eval()
        print("✅ 模型已移动到GPU并设置为评估模式")
    except Exception as e:
        print(f"❌ GPU设置失败: {e}")
        return

    # 验证权重
    is_weights_ok = verify_model_weights(model)
    if not is_weights_ok:
        print("⚠️  权重验证有问题，但继续测试...")

    # 设置路径
    data_path = "/home/<USER>/Documents/ig_slam_maskdata/test_GDD"  # 测试数据路径
    current = "glass_mask_physics_progressive"  # 结果文件夹名称

    # 设置输入输出路径
    image_folder = path_set(data_path, "image")  # 输入图像文件夹
    output_folder = path_set(data_path, current)  # 输出结果文件夹
    gt_folder = path_set(data_path, "mask")  # 真值文件夹

    # 验证路径
    if not os.path.exists(image_folder):
        print(f"❌ 图像文件夹不存在: {image_folder}")
        return
    if not os.path.exists(gt_folder):
        print(f"❌ 真值文件夹不存在: {gt_folder}")
        return

    print(f"📁 输入图像: {image_folder}")
    print(f"📁 输出结果: {output_folder}")
    print(f"📁 真值标签: {gt_folder}")

    # 检测玻璃区域并评估结果
    print("🔄 开始检测和评估...")
    try:
        iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate(
            image_folder, output_folder, gt_folder, model, args['glass_threshold']
        )

        # 打印评估结果
        print("\n🎯 渐进式玻璃物理检测网络 - 平均评估指标:")
        print(f"📊 IoU: {iou:.4f} ({iou*100:.2f}%)")
        print(f"🎯 Accuracy: {acc:.4f}")
        print(f"📈 F-measure: {fm:.4f}")
        print(f"📉 MAE: {mae:.4f}")
        print(f"⚖️ BER: {ber:.4f}")
        print(f"🔄 ABER: {aber:.4f}")
        
        # 显示损失权重修复后的改进
        print(f"\n✅ 多尺度边缘损失权重已优化为渐进式策略")
        print(f"🔧 权重配置: L2(0.8) + L3(1.0) + L4(1.2) + Fused(1.5)")
        
        if iou > 0.876:
            print(f"🎉 IoU已突破87.6%瓶颈！当前: {iou*100:.2f}%")
        else:
            print(f"📈 IoU: {iou*100:.2f}% (目标: >87.6%)")

        return iou, acc, fm, mae, ber, aber
        
    except Exception as e:
        print(f"❌ 检测和评估过程出错: {e}")
        import traceback
        traceback.print_exc()
        return


if __name__ == '__main__':
    main() 