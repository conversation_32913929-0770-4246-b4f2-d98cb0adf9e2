from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

import os
import argparse
import numpy as np
import torch
import torch.optim as optim
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from tqdm import tqdm
from ig_glass.dataloader import SODLoader, FODLoader
from ig_glass.gdnet_lightcrf import GDNetLightCRF
from ig_glass.loss_lightcrf import CompositeLoss
from torch.utils.tensorboard import SummaryWriter

backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext_101_32x4d.pth'
# Initialize writers
writer_train = SummaryWriter(log_dir='runs/lightcrf_train')
writer_vad = SummaryWriter(log_dir='runs/lightcrf_valid')

def parse_arguments():
    parser = argparse.ArgumentParser(description='Parameters to train your model.')
    parser.add_argument('--epochs', default=302, help='Number of epochs to train the model for', type=int)
    parser.add_argument('--bs', default=6, help='Batch size', type=int)
    parser.add_argument('--lr', default=0.001, help='Learning Rate', type=float)
    parser.add_argument('--wd', default=0.0005, help='L2 Weight decay for sgd', type=float)
    parser.add_argument('--img_size', default=416, help='Image size to be used for training', type=int)
    parser.add_argument('--aug', default=True, help='Whether to use Image augmentation', type=bool)
    parser.add_argument('--n_worker', default=4, help='Number of workers to use for loading data', type=int)
    parser.add_argument('--test_interval', default=10, help='Number of epochs after which to test the weights', type=int)
    parser.add_argument('--save_interval', default=20, help='Number of epochs after which to save the weights. If None, does not save', type=int)
    parser.add_argument('--save_opt', default=False, help='Whether to save optimizer along with model weights or not', type=bool)
    parser.add_argument('--log_interval', default=250, help='Logging interval (in #batches)', type=int)
    parser.add_argument('--res_mod', default='./ckpt', help='Path to the model to resume from, default None', type=str)
    parser.add_argument('--res_opt', default=None, help='Path to the optimizer to resume from', type=str)
    parser.add_argument('--use_gpu', default=True, help='Flag to use GPU or not', type=bool)
    parser.add_argument('--base_save_path', default='/home/<USER>/ws/IG_SLAM/ig_glass/models_lightcrf', help='Base path for the models to be saved', type=str)
    parser.add_argument('--crf_iter', default=3, help='Number of CRF iterations', type=int)
    parser.add_argument('--trainable_crf', default=True, help='Whether to make CRF parameters trainable', type=bool)
    parser.add_argument('--crf_weight', default=1.5, help='Weight for CRF loss', type=float)
    # CRF parameters based on grid search
    parser.add_argument('--bilateral_weight', default=10.0, help='Weight for bilateral filter (from compat=10)', type=float)
    parser.add_argument('--gaussian_weight', default=5.0, help='Weight for gaussian filter (from compat=5)', type=float)
    parser.add_argument('--bilateral_spatial_sigma', default=40.0, help='Spatial sigma for bilateral filter (from sxy=40)', type=float)
    parser.add_argument('--bilateral_color_sigma', default=3.0, help='Color sigma for bilateral filter (from srgb=3)', type=float)
    parser.add_argument('--gaussian_sigma', default=1.5, help='Sigma for gaussian filter (from sxy=1.5)', type=float)
    # 修改默认权重值以适应Focal Loss
    parser.add_argument('--bce_weight', default=0.6, help='Weight for BCE/Focal loss component', type=float)
    parser.add_argument('--iou_weight', default=0.4, help='Weight for IoU loss component', type=float)
    # 新增Focal Loss相关参数
    parser.add_argument('--focal_alpha', default=0.25, help='Alpha parameter for Focal Loss', type=float)
    parser.add_argument('--focal_gamma', default=2.0, help='Gamma parameter for Focal Loss', type=float)
    parser.add_argument('--use_focal', default=True, help='Whether to use Focal Loss instead of BCE Loss', type=bool)
    parser.add_argument('--use_kfold', default=False, help='Whether to use k-fold cross validation', type=bool)

    return parser.parse_args()

class EarlyStopping:
    def __init__(self, patience=7, min_delta=0.001):
        """
        patience: Number of epochs with no improvement after which training will be stopped
        min_delta: Minimum change in the monitored quantity to qualify as an improvement
        """
        self.patience = patience
        self.min_delta = min_delta
        self.best_loss = float('inf')
        self.counter = 0

    def step(self, val_loss):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0  # Reset counter
        else:
            self.counter += 1  # Increment counter for no improvement

        return self.counter >= self.patience  # Return True if patience exceeded

class Engine:
    def __init__(self, args):
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.wd = args.wd
        self.img_size = args.img_size
        self.aug = args.aug
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.save_opt = args.save_opt
        self.log_interval = args.log_interval
        self.res_mod_path = args.res_mod
        self.res_opt_path = args.res_opt
        self.use_gpu = args.use_gpu
        self.crf_iter = args.crf_iter
        self.trainable_crf = args.trainable_crf
        self.crf_weight = args.crf_weight
        self.bce_weight = args.bce_weight
        self.iou_weight = args.iou_weight
        self.use_kfold = args.use_kfold
        # 添加Focal Loss参数
        self.focal_alpha = args.focal_alpha
        self.focal_gamma = args.focal_gamma
        self.use_focal = args.use_focal

        # CRF parameters
        self.bilateral_weight = args.bilateral_weight
        self.gaussian_weight = args.gaussian_weight
        self.bilateral_spatial_sigma = args.bilateral_spatial_sigma
        self.bilateral_color_sigma = args.bilateral_color_sigma
        self.gaussian_sigma = args.gaussian_sigma

        self.model_path = args.base_save_path + '/IG-SLAM-LightCRF'
        print('Models would be saved at : {}\n'.format(self.model_path))
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))

        if torch.cuda.is_available():
            self.device = torch.device(device='cuda')
        else:
            self.device = torch.device(device='cpu')

        # Initialize lightweight model
        self.model = GDNetLightCRF(
            backbone_path=backbone_path,
            crf_iter=self.crf_iter,
            trainable_crf=self.trainable_crf
        )

        # Update CRF parameters in the model
        if hasattr(self.model, 'crf') and hasattr(self.model.crf, 'bilateral_weight'):
            self.model.crf.bilateral_weight = nn.Parameter(torch.tensor(self.bilateral_weight)) if self.trainable_crf else self.bilateral_weight
            self.model.crf.gaussian_weight = nn.Parameter(torch.tensor(self.gaussian_weight)) if self.trainable_crf else self.gaussian_weight
            self.model.crf.bilateral_spatial_sigma = nn.Parameter(torch.tensor(self.bilateral_spatial_sigma)) if self.trainable_crf else self.bilateral_spatial_sigma
            self.model.crf.bilateral_color_sigma = nn.Parameter(torch.tensor(self.bilateral_color_sigma)) if self.trainable_crf else self.bilateral_color_sigma
            self.model.crf.gaussian_sigma = nn.Parameter(torch.tensor(self.gaussian_sigma)) if self.trainable_crf else self.gaussian_sigma

        self.model.to(self.device)

        # Initialize loss function
        self.criterion = CompositeLoss(device=self.device, 
                                      bce_weight=self.bce_weight,
                                      iou_weight=self.iou_weight, 
                                      crf_weight=self.crf_weight,
                                      focal_alpha=self.focal_alpha,
                                      focal_gamma=self.focal_gamma,
                                      use_focal=self.use_focal)

        # Initialize optimizer with weight decay for all parameters except batch norm and bias
        params = self._split_params_for_weight_decay(self.model)
        self.optimizer = optim.SGD(params, lr=self.lr, momentum=0.9)

        # Load model and optimizer if resumed
        ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        exp_name = 'IG-SLAM'
        args = {
            'snapshot': 'light-150',
            'scale': 416,
            'crf': False
        }

        if self.res_mod_path is not None:
            chkpt = os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')
            print(f"Loading checkpoint from: {chkpt}")

            # Load base GDNet weights
            if args['snapshot'] != '200':
                base_weights = torch.load(chkpt)['model']
            else:
                base_weights = torch.load(os.path.join(chkpt))

            # Initialize the model weights
            model_state_dict = self.model.state_dict()

            # Create a new state dict with matching keys
            new_state_dict = {}
            for k, v in base_weights.items():
                if k in model_state_dict:
                    if model_state_dict[k].shape == v.shape:
                        new_state_dict[k] = v
                    else:
                        print(f"Skipping parameter {k} due to shape mismatch: {v.shape} vs {model_state_dict[k].shape}")

            # Update model state dict
            model_state_dict.update(new_state_dict)
            self.model.load_state_dict(model_state_dict, strict=False)
            print(f"Successfully loaded {len(new_state_dict)} parameters from checkpoint.")

        # Set up early stopping
        self.early_stopping = EarlyStopping(patience=7)

    def _split_params_for_weight_decay(self, model):
        """
        Split parameters into two groups:
        - with weight decay: weights of conv and linear layers
        - without weight decay: all biases and params of batchnorm
        """
        decay = []
        no_decay = []

        for name, param in model.named_parameters():
            if not param.requires_grad:
                continue
            if 'weight' in name and 'bn' not in name and 'batch_norm' not in name:
                decay.append(param)
            else:
                no_decay.append(param)

        return [
            {'params': decay, 'weight_decay': self.wd},
            {'params': no_decay, 'weight_decay': 0.0}
        ]

    def train(self):
        """Train the GDNetLightCRF model"""
        print('Starting the training...\n')

        # 设置k-fold范围
        k_range = range(5) if self.use_kfold else range(1)

        for k in k_range:
            if self.use_kfold:
                print(f'K-Fold Training: Fold {k+1}')
                self.train_data = FODLoader(mode='train', augment_data=self.aug, target_size=self.img_size, k=k)
                self.test_data = FODLoader(mode='test', augment_data=False, target_size=self.img_size, k=k)
            else:
                self.train_data = SODLoader(mode='train_gdd', augment_data=self.aug, target_size=self.img_size)
                self.test_data = SODLoader(mode='test_gdd', augment_data=False, target_size=self.img_size)

            self.train_dataloader = DataLoader(self.train_data, batch_size=self.bs, shuffle=True, num_workers=self.n_worker)
            self.test_dataloader = DataLoader(self.test_data, batch_size=self.bs, shuffle=False, num_workers=self.n_worker)

            print(f"Training samples: {len(self.train_data)}, Validation samples: {len(self.test_data)}")

            # 设置学习率调度器
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, 'min', patience=3, factor=0.5, verbose=True)

            best_loss = float('inf')

            # 初始化早停机制
            early_stopping = EarlyStopping(patience=7, min_delta=0.001)

            for epoch in range(1, self.epochs + 1):
                # 训练阶段
                self.model.train()
                running_loss = 0.0

                train_tqdm = tqdm(self.train_dataloader, desc=f"Epoch {epoch}/{self.epochs}")
                for batch_idx, (inp_imgs, gt_masks) in enumerate(train_tqdm):
                    inp_imgs = inp_imgs.to(self.device)
                    gt_masks = gt_masks.to(self.device)

                    self.optimizer.zero_grad()

                    # 前向传播
                    h_pred, l_pred, final_pred, refined_pred = self.model(inp_imgs)

                    # 计算损失
                    loss = self.criterion((h_pred, l_pred, final_pred, refined_pred), gt_masks)

                    # 反向传播和优化
                    loss.backward()
                    self.optimizer.step()

                    running_loss += loss.item()

                    # 更新进度条
                    train_tqdm.set_postfix(loss=loss.item(), avg_loss=running_loss/(batch_idx+1))

                    # 记录到TensorBoard
                    if batch_idx % self.log_interval == 0:
                        global_step = (epoch - 1) * len(self.train_dataloader) + batch_idx
                        writer_train.add_scalar('Loss', loss.item(), global_step)

                        # 记录学习率
                        current_lr = self.optimizer.param_groups[0]['lr']
                        writer_train.add_scalar('Learning Rate', current_lr, global_step)

                epoch_loss = running_loss / len(self.train_dataloader)
                print(f"Train Epoch: {epoch} Loss: {epoch_loss:.6f}")

                # 验证阶段
                if epoch % self.test_interval == 0:
                    metrics = self.test(self.test_dataloader, epoch)
                    val_loss = metrics[0]  # 第一个返回值是验证损失

                    # 根据验证损失更新调度器
                    scheduler.step(val_loss)

                    # 检查早停
                    if early_stopping.step(val_loss):
                        print(f"Early stopping triggered after {epoch} epochs")
                        self._save_model(epoch, is_early_stop=True, metrics=metrics)
                        break

                    # 如果是最佳模型则保存
                    if val_loss < best_loss:
                        best_loss = val_loss
                        print(f"New best model with validation loss: {best_loss:.6f}")
                        self._save_model(epoch, is_best=True, metrics=metrics)

                # 定期保存
                if self.save_interval is not None and epoch % self.save_interval == 0:
                    # 如果这个epoch没有运行验证，则运行一次验证并保存指标
                    if epoch % self.test_interval != 0:
                        metrics = self.test(self.test_dataloader, epoch=0)  # 使用epoch=0避免额外的TensorBoard记录
                        self._save_model(epoch, metrics=metrics)
                    else:
                        # 如果这个epoch已经运行了验证，则使用之前的指标
                        self._save_model(epoch, metrics=metrics)

            print(f"Fold {k+1} training completed!")

        print("All training completed!")
        writer_train.close()
        writer_vad.close()

    def test(self, dataloader=None, epoch=0):
        """Test the model on validation data"""
        self.model.eval()

        if dataloader is None:
            # 如果没有提供dataloader，则创建一个用于验证
            validation_dataset = SODLoader(mode='test_gdd', augment_data=False, target_size=self.img_size)
            dataloader = DataLoader(validation_dataset, batch_size=self.bs, shuffle=False,
                                num_workers=self.n_worker)

        running_loss = 0.0
        tp_fp = 0   # TruePositive + TrueNegative, for accuracy
        tp = 0      # TruePositive
        pred_true = 0   # Number of '1' predictions, for precision
        gt_true = 0     # Number of '1's in gt mask, for recall
        mae_list = []   # List to save mean absolute error of each image
        iou_list = []   # List to save IoU of each image
        total_pixels = 0  # Total number of pixels across all test images
        acc_list = []    # List to save accuracy of each image

        with torch.no_grad():
            test_tqdm = tqdm(dataloader, desc="Validation")
            for batch_idx, (inp_imgs, gt_masks) in enumerate(test_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)

                # 前向传播
                h_pred, l_pred, final_pred, refined_pred = self.model(inp_imgs)

                # 计算损失
                loss = self.criterion((h_pred, l_pred, final_pred, refined_pred), gt_masks)

                running_loss += loss.item()

                # 更新进度条
                test_tqdm.set_postfix(loss=loss.item(), avg_loss=running_loss/(batch_idx+1))

                # 使用refined_pred进行评估 - 简化处理以提高性能
                # 如果是2通道，取第二个通道（前景）
                pred_masks = refined_pred[:, 1:2] if refined_pred.size(1) == 2 else refined_pred

                # 转换为numpy进行一致的评估
                pred_np = pred_masks.detach().cpu().numpy()
                gt_np = gt_masks.detach().cpu().numpy()

                # 计算每个批次中每张图像的指标
                for b in range(pred_np.shape[0]):
                    # 获取单张图像的预测和真实值
                    pred_single = pred_np[b, 0]  # 现在我们确保pred_masks是[B, 1, H, W]格式
                    gt_single = gt_np[b, 0]      # Shape: H x W

                    # 二值化预测 (阈值为0.5)
                    pred_binary = (pred_single > 0.5).astype(np.float32)

                    # 计算TP, TN, FP, FN
                    TP = np.sum(np.logical_and(pred_binary, gt_single))
                    TN = np.sum(np.logical_and(np.logical_not(pred_binary), np.logical_not(gt_single)))

                    # 计算准确率
                    N_p = np.sum(gt_single)
                    N_n = np.sum(np.logical_not(gt_single))
                    acc = (TP + TN) / (N_p + N_n)

                    # 计算IoU
                    intersection = TP
                    union = np.sum(pred_binary) + np.sum(gt_single) - intersection
                    img_iou = intersection / union if union > 0 else 0.0

                    # 累积指标
                    tp_fp += TP + TN
                    tp += TP
                    pred_true += np.sum(pred_binary)
                    gt_true += N_p
                    iou_list.append(img_iou)
                    acc_list.append(acc)

                    # 添加到总像素计数
                    total_pixels += (N_p + N_n)

                # 记录绝对误差 - 简化处理以提高性能
                # 确保gt_masks是[B, 1, H, W]格式
                if gt_masks.dim() == 3:  # 如果gt_masks是[B, H, W]
                    gt_masks = gt_masks.unsqueeze(1)  # 变成[B, 1, H, W]
                ae = torch.mean(torch.abs(pred_masks - gt_masks), dim=(1, 2, 3)).cpu().numpy()
                mae_list.extend(ae)

                # 每10个epoch记录一次第一批预测结果到TensorBoard
                if batch_idx == 0 and epoch % 10 == 0:
                    # 记录图像、真实值和预测结果
                    for i in range(min(4, inp_imgs.size(0))):
                        writer_vad.add_image(f'Image/{i}', inp_imgs[i], epoch)
                        try:
                            gt_img = gt_masks[i]
                            if gt_img.dim() == 3 and gt_img.size(0) == 1:
                                gt_img = gt_img.squeeze(0)
                            writer_vad.add_image(f'Ground Truth/{i}', gt_img, epoch, dataformats='HW')
                        except Exception as e:
                            print(f"Error adding Ground Truth image to TensorBoard: {e}")

                        # 添加各种预测结果到TensorBoard
                        self._add_prediction_to_tensorboard(h_pred, i, 'h_pred', epoch)
                        self._add_prediction_to_tensorboard(l_pred, i, 'l_pred', epoch)
                        self._add_prediction_to_tensorboard(final_pred, i, 'final_pred', epoch)
                        self._add_prediction_to_tensorboard(refined_pred, i, 'refined_pred', epoch, final_pred)

        # 计算最终指标 - 简化处理以提高性能
        val_loss = running_loss / len(dataloader)

        # 计算指标 - 只计算需要的指标
        accuracy = tp_fp / total_pixels if total_pixels > 0 else 0
        mae = np.mean(mae_list)
        iou = np.mean(iou_list)

        # 打印指标
        print(f'TEST :: ACC: {accuracy:.4f}\tMAE: {mae:.4f}\tIOU: {iou:.4f}\tLOSS: {val_loss:.4f}')

        # 只记录关键指标到TensorBoard
        if epoch > 0:  # 只在非零epoch时记录，避免重复记录
            writer_vad.add_scalar('Loss', val_loss, epoch)
            writer_vad.add_scalar('Accuracy', accuracy, epoch)
            writer_vad.add_scalar('IoU', iou, epoch)

        # 返回多个指标，便于保存模型时使用
        return val_loss, mae, iou

    def _add_prediction_to_tensorboard(self, pred, index, name, epoch, fallback=None):
        """Helper method to add prediction images to TensorBoard - 简化版本"""
        try:
            # 简化处理逻辑
            if pred.dim() == 4:
                # 如果是2通道的CRF输出，取第二个通道（前景）
                if pred.size(1) == 2 and name == 'refined_pred':
                    pred_img = pred[index, 1]
                # 如果是单通道，直接取
                elif pred.size(1) == 1:
                    pred_img = pred[index, 0]
                # 其他情况，选择适当的通道
                else:
                    pred_img = pred[index, 0]
            else:
                # 如果形状不符合预期，使用fallback
                if fallback is not None:
                    pred_img = fallback[index, 0] if fallback.dim() == 4 else fallback[index]
                else:
                    return

            # 确保张量是2D的
            if pred_img.dim() > 2:
                pred_img = pred_img.squeeze()

            # 标准化到[0, 1]范围
            pred_img = torch.clamp(pred_img, 0, 1)

            # 添加到TensorBoard
            writer_vad.add_image(f'{name}/{index}', pred_img, epoch, dataformats='HW')
        except Exception as e:
            # 简化错误处理
            print(f"Error adding {name} image to TensorBoard: {e}")

    def _save_model(self, epoch, is_best=False, is_early_stop=False, metrics=None):
        """Save model checkpoint with detailed information"""
        # 如果没有提供指标，则计算当前的指标
        if metrics is None:
            val_loss, mae, iou = self.test(self.test_dataloader, epoch=0)  # 使用epoch=0避免额外的TensorBoard记录
        else:
            val_loss, mae, iou = metrics

        # 准备模型状态
        state = {
            'model': self.model.state_dict(),
            'epoch': epoch,
            'loss': val_loss,
            'mae': mae,
            'iou': iou
        }

        #save model
        if is_early_stop:
                # 早停模型使用特殊命名
            torch.save(state, os.path.join(self.model_path, 'weights', f'early_stop_epoch-{epoch:03d}_mae-{mae:.4f}_loss-{val_loss:.4f}.pth'))
            print(f"Early stopping model saved at epoch {epoch}")
        elif is_best:
            # 最佳模型使用特殊命名
            torch.save(state, os.path.join(self.model_path, 'weights', f'best_model_epoch-{epoch:03d}_mae-{mae:.4f}_loss-{val_loss:.4f}.pth'))
        else:
            # 常规保存使用详细命名
            torch.save(state, os.path.join(self.model_path, 'weights', f'model_epoch-{epoch:03d}_mae-{mae:.4f}_loss-{val_loss:.4f}.pth'))
        # 保存优化器状态（如果需要）
        if self.save_opt:
            opt_state = self.optimizer.state_dict()
            if is_best:
                torch.save(opt_state, os.path.join(self.model_path, 'optimizers', f'best_opt_epoch-{epoch:03d}.pth'))
            else:
                torch.save(opt_state, os.path.join(self.model_path, 'optimizers', f'opt_epoch-{epoch:03d}.pth'))

        print(f"Saved model checkpoint at epoch {epoch} with MAE: {mae:.4f}, Loss: {val_loss:.4f}")


if __name__ == '__main__':
    args = parse_arguments()
    engine = Engine(args)
    engine.train()