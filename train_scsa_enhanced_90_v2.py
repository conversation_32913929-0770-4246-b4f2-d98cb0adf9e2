"""
 @Time    : 2024
 <AUTHOR> <PERSON>ine
 @Project : IG_SLAM
 @File    : train_scsa_enhanced_90_v2.py
 @Function: 基于87.95% SCSA成功经验的增强训练，目标90% IoU

基于原始train_scsa.py的简洁格式，保留成功配置，添加90% IoU优化
"""
import os
import sys
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
from pathlib import Path
import logging
from tqdm import tqdm
import json
from collections import defaultdict
import math

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from ig_glass.gdnet_scsa_enhanced_90 import create_scsa_enhanced_90_model
from ig_glass.loss_scsa_enhanced_90 import create_scsa_enhanced_90_loss
from ig_glass.glass_mask import GlassDataset


def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'scsa90_{time.strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


class EarlyStopping:
    """早停机制 - 基于原始SCSA成功经验"""
    def __init__(self, patience=20, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = None
        
    def __call__(self, val_loss):
        if self.best_loss is None:
            self.best_loss = val_loss
        elif val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
        else:
            self.counter += 1
            
        return self.counter >= self.patience


def train_model(args):
    """主训练函数 - 基于原始SCSA格式"""
    
    # 设备和日志
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger = setup_logging(args.log_dir)
    logger.info("开始SCSA Enhanced 90% IoU训练")
    
    # 创建模型
    model = create_scsa_enhanced_90_model(
        backbone_path=args.backbone_path,
        crf_iter=args.crf_iter,
        trainable_crf=args.trainable_crf
    ).to(device)
    
    # 创建损失函数
    criterion = create_scsa_enhanced_90_loss(adaptive_weights=args.adaptive_loss).to(device)
    
    # 优化器 - 基于SCSA成功经验，但使用分层学习率
    def create_optimizer(model, lr):
        """创建优化器 - SCSA验证的最优配置"""
        # 分离参数组
        backbone_params = []
        scsa_params = []
        other_params = []
        
        for name, param in model.named_parameters():
            if any(layer in name for layer in ['layer0', 'layer1', 'layer2', 'layer3', 'layer4']):
                backbone_params.append(param)
            elif 'scsa' in name.lower() or 'attention' in name.lower():
                scsa_params.append(param)
            else:
                other_params.append(param)
        
        # SCSA验证的分层权重衰减策略
        optimizer = optim.SGD([
            {'params': backbone_params, 'lr': lr * 0.1, 'weight_decay': 1e-4},
            {'params': scsa_params, 'lr': lr * 1.2, 'weight_decay': 5e-5},  # SCSA稍高学习率
            {'params': other_params, 'lr': lr, 'weight_decay': 1e-4}
        ], momentum=0.9, nesterov=True)
        
        return optimizer
    
    optimizer = create_optimizer(model, args.lr)
    
    # 学习率调度器 - 基于SCSA成功经验
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=10, 
        min_lr=1e-7, verbose=True
    )
    
    # 数据加载器
    def create_dataloaders(train_path, val_path, batch_size, num_workers):
        """创建数据加载器 - SCSA验证的数据增强"""
        train_transforms = [
            ('resize', (416, 416)),
            ('horizontal_flip', 0.5),
            ('vertical_flip', 0.3),
            ('rotation', 15),
            ('color_jitter', {'brightness': 0.2, 'contrast': 0.2}),
            ('normalize', {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]})
        ]
        
        val_transforms = [
            ('resize', (416, 416)),
            ('normalize', {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]})
        ]
        
        train_dataset = GlassDataset(train_path, transforms=train_transforms, mode='train')
        val_dataset = GlassDataset(val_path, transforms=val_transforms, mode='val')
        
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True,
            num_workers=num_workers, pin_memory=True, drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=True
        )
        
        return train_loader, val_loader
    
    train_loader, val_loader = create_dataloaders(
        args.train_data_path, args.val_data_path, args.batch_size, args.num_workers
    )
    
    # 训练状态
    best_iou = 0.0
    best_epoch = 0
    train_history = defaultdict(list)
    early_stopping = EarlyStopping(patience=args.patience)
    
    # 训练阶段配置 - 90% IoU优化
    training_stages = [
        {'epoch_start': 0, 'epoch_end': 60, 'edge_weight': 3.0, 'focus': 'foundation'},
        {'epoch_start': 60, 'epoch_end': 120, 'edge_weight': 5.0, 'focus': 'edge_refinement'}, 
        {'epoch_start': 120, 'epoch_end': 200, 'edge_weight': 7.0, 'focus': 'precision_tuning'}
    ]
    
    def get_current_stage(epoch):
        """获取当前训练阶段"""
        for stage in training_stages:
            if stage['epoch_start'] <= epoch < stage['epoch_end']:
                return stage
        return training_stages[-1]  # 最后阶段
    
    def calculate_iou(pred, target, threshold=0.5):
        """计算IoU - 基于SCSA成功经验"""
        pred_binary = (pred > threshold).float()
        target_binary = target.float()
        
        intersection = (pred_binary * target_binary).sum()
        union = pred_binary.sum() + target_binary.sum() - intersection
        
        iou = intersection / (union + 1e-8)
        return iou.item()
    
    def train_epoch(epoch):
        """训练一个epoch"""
        model.train()
        total_loss = 0
        total_iou = 0
        num_batches = 0
        
        # 获取当前阶段并调整损失权重
        current_stage = get_current_stage(epoch)
        if hasattr(criterion, 'edge_weight') and criterion.adaptive_weights:
            criterion.edge_weight.data = torch.tensor(current_stage['edge_weight'])
        
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1} ({current_stage["focus"]})')
        
        for images, targets in progress_bar:
            images, targets = images.to(device), targets.to(device)
            
            # 前向传播
            outputs = model(images)
            loss, loss_dict = criterion(outputs, targets)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪 - SCSA验证的稳定训练
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # 计算IoU
            if 'refined_pred' in outputs:
                pred = outputs['refined_pred']
                if pred.size(1) == 2:
                    pred = pred[:, 1:2]
            else:
                pred = outputs.get('ensemble_pred', outputs.get('final_pred', outputs['pred_h']))
            
            batch_iou = calculate_iou(pred, targets)
            
            # 记录
            total_loss += loss.item()
            total_iou += batch_iou
            num_batches += 1
            
            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'IoU': f'{batch_iou:.4f}',
                'Stage': current_stage['focus']
            })
        
        avg_loss = total_loss / num_batches
        avg_iou = total_iou / num_batches
        
        logger.info(f"训练 Epoch {epoch+1}: Loss={avg_loss:.4f}, IoU={avg_iou:.4f}")
        
        return avg_loss, avg_iou
    
    def validate_epoch(epoch):
        """验证一个epoch"""
        model.eval()
        total_loss = 0
        total_iou = 0
        num_batches = 0
        
        with torch.no_grad():
            for images, targets in tqdm(val_loader, desc='Validation'):
                images, targets = images.to(device), targets.to(device)
                
                # 前向传播
                outputs = model(images)
                loss, _ = criterion(outputs, targets)
                
                # 计算IoU
                if 'refined_pred' in outputs:
                    pred = outputs['refined_pred']
                    if pred.size(1) == 2:
                        pred = pred[:, 1:2]
                else:
                    pred = outputs.get('ensemble_pred', outputs.get('final_pred', outputs['pred_h']))
                
                batch_iou = calculate_iou(pred, targets)
                
                total_loss += loss.item()
                total_iou += batch_iou
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        avg_iou = total_iou / num_batches
        
        logger.info(f"验证 Epoch {epoch+1}: Loss={avg_loss:.4f}, IoU={avg_iou:.4f}")
        
        return avg_loss, avg_iou
    
    def save_checkpoint(epoch, is_best=False, is_90iou=False):
        """保存检查点"""
        os.makedirs(args.save_dir, exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'best_iou': best_iou,
            'train_history': dict(train_history),
            'args': vars(args)
        }
        
        # 保存常规检查点
        if epoch % args.save_interval == 0:
            torch.save(checkpoint, os.path.join(args.save_dir, f'checkpoint_epoch_{epoch+1}.pth'))
        
        # 保存最佳模型
        if is_best:
            torch.save(checkpoint, os.path.join(args.save_dir, 'best_model.pth'))
            logger.info(f"🎉 新的最佳IoU: {best_iou:.4f}")
        
        # 保存90% IoU里程碑
        if is_90iou:
            torch.save(checkpoint, os.path.join(args.save_dir, '90iou_achieved.pth'))
            logger.info(f"🎯 达到90% IoU目标！IoU: {best_iou:.4f}")
    
    # 主训练循环
    logger.info(f"开始训练 - 总共{args.epochs}个epoch")
    logger.info(f"训练数据: {len(train_loader.dataset)} 样本")
    logger.info(f"验证数据: {len(val_loader.dataset)} 样本")
    
    for epoch in range(args.epochs):
        # 训练
        train_loss, train_iou = train_epoch(epoch)
        
        # 验证
        val_loss, val_iou = validate_epoch(epoch)
        
        # 更新学习率
        scheduler.step(val_loss)
        
        # 记录历史
        train_history['train_loss'].append(train_loss)
        train_history['train_iou'].append(train_iou)
        train_history['val_loss'].append(val_loss)
        train_history['val_iou'].append(val_iou)
        
        # 检查最佳模型
        is_best = val_iou > best_iou
        is_90iou = val_iou >= 0.90 and best_iou < 0.90  # 首次达到90%
        
        if is_best:
            best_iou = val_iou
            best_epoch = epoch + 1
        
        # 保存检查点
        save_checkpoint(epoch, is_best, is_90iou)
        
        # 早停检查
        if early_stopping(val_loss):
            logger.info(f"早停触发，最佳IoU: {best_iou:.4f} (Epoch {best_epoch})")
            break
        
        # 定期打印损失权重
        if epoch % 20 == 0 and hasattr(criterion, 'get_loss_weights'):
            weights = criterion.get_loss_weights()
            logger.info(f"损失权重: {weights}")
    
    # 保存训练历史
    history_path = os.path.join(args.save_dir, 'training_history.json')
    with open(history_path, 'w') as f:
        json.dump(dict(train_history), f, indent=2)
    
    logger.info("训练完成！")
    logger.info(f"最佳IoU: {best_iou:.4f} (Epoch {best_epoch})")
    
    return best_iou


def main():
    parser = argparse.ArgumentParser(description='SCSA Enhanced 90% IoU训练')
    
    # 数据参数
    parser.add_argument('--train_data_path', type=str, required=True, help='训练数据路径')
    parser.add_argument('--val_data_path', type=str, required=True, help='验证数据路径')
    parser.add_argument('--batch_size', type=int, default=8, help='批次大小')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载器线程数')
    
    # 模型参数
    parser.add_argument('--backbone_path', type=str, default=None, help='预训练骨干网络路径')
    parser.add_argument('--crf_iter', type=int, default=7, help='CRF迭代次数')
    parser.add_argument('--trainable_crf', type=bool, default=True, help='CRF是否可训练')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=200, help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-3, help='学习率')  # SCSA验证的最优值
    parser.add_argument('--patience', type=int, default=25, help='早停耐心值')
    
    # 损失函数参数
    parser.add_argument('--adaptive_loss', type=bool, default=True, help='自适应损失权重')
    
    # 保存参数
    parser.add_argument('--save_dir', type=str, default='./checkpoints_scsa90', help='模型保存目录')
    parser.add_argument('--log_dir', type=str, default='./logs_scsa90', help='日志保存目录')
    parser.add_argument('--save_interval', type=int, default=20, help='模型保存间隔')
    
    args = parser.parse_args()
    
    # 开始训练
    best_iou = train_model(args)
    
    print(f"\n🎯 训练完成！最佳IoU: {best_iou:.4f}")
    if best_iou >= 0.90:
        print("🎉 成功达到90% IoU目标！")
    else:
        print(f"🔥 距离90% IoU还需: {0.90 - best_iou:.3f}")


if __name__ == '__main__':
    main()