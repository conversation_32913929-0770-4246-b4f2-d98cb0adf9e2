#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化Proteus ViT-B训练脚本
解决ViT效果不佳的问题，目标90%+ IoU

主要改进：
1. 正确使用Proteus ViT-B预训练权重
2. 优化的多尺度特征提取
3. 改进的训练策略
4. 与SCSA基准对比
"""

import os
import sys
import time
import argparse
import numpy as np
from datetime import datetime

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")

# 添加项目路径
sys.path.append('ig_glass')

from gdnet_optimized_vit import create_optimized_vit_model
from gdnet_scsa import GDNetSCSA
from loss_enhanced_scsa import create_enhanced_scsa_loss
from glass_dataloader import GlassDataLoader


class OptimizedProteusViTBTrainer:
    """优化Proteus ViT-B训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化模型
        self._init_models()
        self._init_optimizers()
        self._init_dataloaders()
        self._init_logging()
        
        # 性能跟踪
        self.best_iou = 0.0
        self.training_history = []
        
        print(f"🚀 优化Proteus ViT-B训练器初始化完成")
        print(f"   设备: {self.device}")
        print(f"   目标: 解决ViT效果不佳问题，达到90%+ IoU")
    
    def _init_models(self):
        """初始化模型"""
        print("📦 创建优化Proteus ViT-B模型...")
        self.model = create_optimized_vit_model(
            backbone_path=self.args.backbone_path,
            crf_iter=self.args.crf_iter,
            trainable_crf=True
        ).to(self.device)
        
        # 损失函数 - 针对ViT优化
        self.criterion = create_enhanced_scsa_loss(
            focal_weight=0.2,       # 适当增加focal权重
            iou_weight=0.6,         # 保持IoU权重
            edge_weight=0.15,       # 边缘权重
            consistency_weight=0.05, # 一致性权重
            adaptive=True           # 启用自适应权重
        )
        
        # 打印模型信息
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"   总参数: {total_params:,} ({total_params/1e6:.1f}M)")
        print(f"   可训练参数: {trainable_params:,} ({trainable_params/1e6:.1f}M)")
        
        # 如果有基准模型，也加载用于对比
        if self.args.compare_with_scsa and os.path.exists(self.args.scsa_model_path):
            print("📦 加载SCSA基准模型用于对比...")
            self.scsa_model = GDNetSCSA(backbone_path=self.args.backbone_path).to(self.device)
            scsa_checkpoint = torch.load(self.args.scsa_model_path, map_location=self.device)
            self.scsa_model.load_state_dict(scsa_checkpoint)
            self.scsa_model.eval()
    
    def _init_optimizers(self):
        """初始化优化器"""
        # 分层学习率 - ViT backbone使用较小学习率
        vit_params = []
        other_params = []
        
        for name, param in self.model.named_parameters():
            if 'vit_backbone' in name:
                vit_params.append(param)
            else:
                other_params.append(param)
        
        self.optimizer = optim.AdamW([
            {'params': vit_params, 'lr': self.args.lr * 0.1, 'weight_decay': 1e-4},  # ViT用小学习率
            {'params': other_params, 'lr': self.args.lr, 'weight_decay': 1e-5}
        ], lr=self.args.lr, betas=(0.9, 0.999), eps=1e-8)
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=15, T_mult=2, eta_min=1e-6
        )
        
        # 性能监控调度器
        self.plateau_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=8, verbose=True
        )
    
    def _init_dataloaders(self):
        """初始化数据加载器"""
        # 训练数据 - 使用适中的数据增强
        self.train_loader = DataLoader(
            GlassDataLoader(
                mode='train',
                augment_data=True,
                target_size=self.args.input_size,
                glass_augmentation='moderate'  # 适中增强，避免过度增强影响ViT
            ),
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        # 验证数据
        self.val_loader = DataLoader(
            GlassDataLoader(
                mode='test',
                augment_data=False,
                target_size=self.args.input_size
            ),
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            pin_memory=True
        )
    
    def _init_logging(self):
        """初始化日志"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.log_dir = f"runs/optimized_proteus_vitb_{timestamp}"
        self.writer = SummaryWriter(self.log_dir)
        
        # 创建检查点目录
        self.checkpoint_dir = f"checkpoints/optimized_proteus_vitb_{timestamp}"
        os.makedirs(self.checkpoint_dir, exist_ok=True)
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_iou = 0.0
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.args.epochs}')
        
        for batch_idx, (images, targets) in enumerate(pbar):
            images = images.to(self.device)
            targets = targets.to(self.device)
            
            # 前向传播
            predictions = self.model(images)
            
            # 处理预测结果
            pred = predictions['pred']
            
            # 计算性能指标
            with torch.no_grad():
                pred_binary = (pred > 0.5).float()
                intersection = torch.sum(pred_binary * targets, dim=(2, 3))
                union = torch.sum(pred_binary, dim=(2, 3)) + torch.sum(targets, dim=(2, 3)) - intersection
                iou = torch.mean(intersection / (union + 1e-7))
                
                performance_metrics = {
                    'iou': iou.item(),
                    'epoch': epoch,
                    'batch': batch_idx
                }
            
            # 构建损失函数需要的预测字典
            pred_dict = {
                'pred_h': pred,
                'pred_l': pred,
                'ensemble_pred': pred,
                'refined_pred': pred
            }
            
            # 计算损失
            loss_dict = self.criterion(pred_dict, targets, performance_metrics)
            loss = loss_dict['total_loss']
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 更新统计
            total_loss += loss.item()
            total_iou += iou.item()
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'IoU': f'{iou.item():.4f}',
                'LR': f'{self.optimizer.param_groups[0]["lr"]:.2e}'
            })
            
            # 记录到tensorboard
            if batch_idx % 50 == 0:
                step = epoch * len(self.train_loader) + batch_idx
                self.writer.add_scalar('Train/Loss', loss.item(), step)
                self.writer.add_scalar('Train/IoU', iou.item(), step)
                self.writer.add_scalar('Train/LR', self.optimizer.param_groups[0]['lr'], step)
                
                # 记录各项损失
                for key, value in loss_dict.items():
                    if key != 'total_loss' and torch.is_tensor(value):
                        self.writer.add_scalar(f'Train/{key}', value.item(), step)
        
        # 更新学习率
        self.scheduler.step()
        
        avg_loss = total_loss / len(self.train_loader)
        avg_iou = total_iou / len(self.train_loader)
        
        return avg_loss, avg_iou
    
    def validate(self, epoch):
        """验证"""
        self.model.eval()
        total_loss = 0.0
        total_iou = 0.0
        
        with torch.no_grad():
            for images, targets in tqdm(self.val_loader, desc='Validating'):
                images = images.to(self.device)
                targets = targets.to(self.device)
                
                predictions = self.model(images)
                pred = predictions['pred']
                
                # 构建损失函数需要的预测字典
                pred_dict = {
                    'pred_h': pred,
                    'pred_l': pred,
                    'ensemble_pred': pred,
                    'refined_pred': pred
                }
                
                # 计算损失
                loss_dict = self.criterion(pred_dict, targets)
                loss = loss_dict['total_loss']
                
                # 计算IoU
                pred_binary = (pred > 0.5).float()
                intersection = torch.sum(pred_binary * targets, dim=(2, 3))
                union = torch.sum(pred_binary, dim=(2, 3)) + torch.sum(targets, dim=(2, 3)) - intersection
                iou = torch.mean(intersection / (union + 1e-7))
                
                total_loss += loss.item()
                total_iou += iou.item()
        
        avg_loss = total_loss / len(self.val_loader)
        avg_iou = total_iou / len(self.val_loader)
        
        # 记录到tensorboard
        self.writer.add_scalar('Val/Loss', avg_loss, epoch)
        self.writer.add_scalar('Val/IoU', avg_iou, epoch)
        
        # 更新plateau scheduler
        self.plateau_scheduler.step(avg_iou)
        
        return avg_loss, avg_iou
    
    def save_checkpoint(self, epoch, iou, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_iou': self.best_iou,
            'iou': iou
        }
        
        # 保存最新检查点
        torch.save(checkpoint, os.path.join(self.checkpoint_dir, 'latest.pth'))
        
        # 保存最佳检查点
        if is_best:
            torch.save(checkpoint, os.path.join(self.checkpoint_dir, 'best.pth'))
            print(f"💾 保存最佳模型，IoU: {iou:.4f}")
    
    def train(self):
        """主训练循环"""
        print("🚀 开始优化Proteus ViT-B训练")
        print("   目标: 解决ViT效果不佳问题，达到90%+ IoU")
        
        for epoch in range(self.args.epochs):
            # 训练
            train_loss, train_iou = self.train_epoch(epoch)
            
            # 验证
            val_loss, val_iou = self.validate(epoch)
            
            # 打印结果
            print(f"Epoch {epoch+1}/{self.args.epochs}")
            print(f"  Train - Loss: {train_loss:.4f}, IoU: {train_iou:.4f}")
            print(f"  Val   - Loss: {val_loss:.4f}, IoU: {val_iou:.4f}")
            
            # 检查是否是最佳模型
            is_best = val_iou > self.best_iou
            if is_best:
                self.best_iou = val_iou
                improvement = val_iou - (self.training_history[-1] if self.training_history else 0.78)
                print(f"📈 IoU提升: {improvement:+.4f}")
                
                # 检查是否解决了ViT效果不佳的问题
                if val_iou >= 0.85:
                    print(f"🎉 ViT性能显著改善！当前IoU: {val_iou:.4f} (原始ViT: ~78%)")
                if val_iou >= 0.90:
                    print(f"🏆 成功突破90% IoU！ViT优化成功！")
            
            # 记录历史
            self.training_history.append(val_iou)
            
            # 保存检查点
            self.save_checkpoint(epoch, val_iou, is_best)
            
            # 早停检查
            if len(self.training_history) >= self.args.patience:
                recent_best = max(self.training_history[-self.args.patience:])
                if recent_best <= self.best_iou:
                    print(f"早停触发，最佳IoU: {self.best_iou:.4f}")
                    break
        
        print(f"✅ 训练完成！")
        print(f"   最佳IoU: {self.best_iou:.4f}")
        if self.best_iou >= 0.85:
            print(f"   🎉 成功解决ViT效果不佳问题！")
        else:
            print(f"   📈 仍需进一步优化，距离85%还差: {0.85 - self.best_iou:.4f}")
        
        self.writer.close()


def main():
    parser = argparse.ArgumentParser(description='Optimized Proteus ViT-B Training')
    parser.add_argument('--backbone_path', type=str,
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/proteus_vitb_backbone.pth',
                       help='Proteus ViT-B backbone weights path')
    parser.add_argument('--epochs', type=int, default=80, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=6, help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--input_size', type=int, default=416, help='Input image size')
    parser.add_argument('--num_workers', type=int, default=4, help='Number of workers')
    parser.add_argument('--crf_iter', type=int, default=5, help='CRF iterations')
    parser.add_argument('--patience', type=int, default=15, help='Early stopping patience')
    parser.add_argument('--compare_with_scsa', action='store_true',
                       help='Compare with SCSA baseline')
    parser.add_argument('--scsa_model_path', type=str,
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/scsa-30-150.pth',
                       help='SCSA model path for comparison')
    
    args = parser.parse_args()
    
    # 创建训练器并开始训练
    trainer = OptimizedProteusViTBTrainer(args)
    trainer.train()


if __name__ == '__main__':
    main()
