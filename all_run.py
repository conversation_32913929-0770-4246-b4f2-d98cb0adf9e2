# use light.py to calculate brightness and feature points
# use blank.py to calculate edge points
# use blank_line.py to calculate line points
# use main.py to calculate all points 
import os
import ig_light.Light as light
import ig_glass.blank as blank
#import ig_glass.blank_line as blank_line
import ig_glass.allpoints as allpoints
import ig_glass.blank_line_test as blank_line_test
import ig_glass.glass_mask as glass_mask

import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import check_mkdir, crf_refine
from ig_glass.gdnet import GDNet
# set the path of all floders
light_path = './light'
blank_path = './blank'
blank_line_path = './blank_line'
allpoints_path = './allpoints'
blank_line_test_path = './blank_line_test'
glass_mask_path = './glass_mask'
backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext_101_32x4d.pth'

# 动态亮度调整目标范围
BRIGHTNESS_TARGET_MIN = 100
BRIGHTNESS_TARGET_MAX = 110

def path_set(tempdata_path,new_path):
    gdd_results_root = os.path.join(tempdata_path,new_path)
    return gdd_results_root
    

# set the path of all files
def main():
    alldata_path = "/home/<USER>/Documents/dataset/IR"
    #bag = ["P010"]#,"P006","P002","P010"]
    #bag = ["TS_stairs_up"] #"TS_entrance_cafe",
    bag = ["IR1227"] #"IR1227,IR1202",
    #bag = ["outfull0425"]
    # device set
    device_ids = [0]
    torch.cuda.set_device(device_ids[0])
    for i in bag:
        tempdata_path = os.path.join(alldata_path,i)
        image_folder = os.path.join(tempdata_path,"orin")

        #run all the 4 py to get results
        # #light 
        # current = "light"
        # output_folder = path_set(tempdata_path,current)
        # brightness_list, feature_points_list = light.process_images(image_folder, output_folder)
        
        # #allpoints
        # #Step 1: Load GDNet model
        # #parameter set
        # ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        # exp_name = 'IG-SLAM'
        # args = {
        #     'snapshot': '200',
        #     'scale': 416,
        #     'crf': True,  
        #     'glass_threshold': 0.98,  # 玻璃区域识别阈值
        # }
        # # scale 416
        # net = GDNet(backbone_path=backbone_path).cuda(device_ids[0])
        # if len(args['snapshot']) > 0:
        #     print(f'Load snapshot {args["snapshot"]} for testing')
        #     net.load_state_dict(torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')))
        #     print(f'Load {os.path.join(ckpt_path, exp_name, args["snapshot"] + ".pth")} succeed!')

        # net.eval()
        # current = "points"
        # output_folder = path_set(tempdata_path,current)
        # brightness_list, feature_points_list = allpoints.detect_glass_and_adjust_brightness(image_folder, output_folder, net, args['glass_threshold'])

        # # blank
        # # parameter set
        # ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        # exp_name = 'IG-SLAM'
        # args = {
        #     'snapshot': '200',
        #     'scale': 416,
        #     'crf': True,
        #     'glass_threshold': 0.9,  # 玻璃区域识别阈值
        # }

        # net = GDNet(backbone_path=backbone_path).cuda(device_ids[0])
        # if len(args['snapshot']) > 0:
        #     print(f'Load snapshot {args["snapshot"]} for testing')
        #     net.load_state_dict(torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')))
        #     print(f'Load {os.path.join(ckpt_path, exp_name, args["snapshot"] + ".pth")} succeed!')

        # net.eval()
        # current = "blank"
        # output_folder = path_set(tempdata_path,current)
        # brightness_list, feature_points_list = blank.detect_glass_and_adjust_brightness(image_folder, output_folder, net, args['glass_threshold'])

        # # ig blank_line
        # # parameter set
        # ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        # exp_name = 'IG-SLAM'
        # args = {
        #     'snapshot': '200',
        #     'scale': 416,
        #     'crf': True,
        #     'glass_threshold': 0.9,  # 玻璃区域识别阈值
        # }

        # net = GDNet(backbone_path=backbone_path).cuda(device_ids[0])
        # if len(args['snapshot']) > 0:
        #     print(f'Load snapshot {args["snapshot"]} for testing')
        #     net.load_state_dict(torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')))
        #     print(f'Load {os.path.join(ckpt_path, exp_name, args["snapshot"] + ".pth")} succeed!')

        # net.eval()
        # current = "blank_line"
        # output_folder = path_set(tempdata_path,current)
        # brightness_list, feature_points_list = blank_line.detect_glass_and_adjust_brightness(image_folder, output_folder, net, args['glass_threshold'])
        
        # # ig blank_line_test
        # parameter set
        ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        exp_name = 'IG-SLAM'
        args = {
            'snapshot': 'fofin',
            'scale': 416,
            'crf': True,
            'glass_threshold': 0.9,  # 玻璃区域识别阈值
        }

        net = GDNet(backbone_path=backbone_path).cuda(device_ids[0])
        if len(args['snapshot']) > 0:
            print(f'Load snapshot {args["snapshot"]} for testing')
            load_path = torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth'))
            net.load_state_dict(load_path['model'])
            #net.load_state_dict(torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')))
            print(f'Load {os.path.join(ckpt_path, exp_name, args["snapshot"] + ".pth")} succeed!')

        net.eval()
        current = "blank_line_test"
        output_folder = path_set(tempdata_path,current)
        brightness_list, feature_points_list = blank_line_test.detect_glass_and_adjust_brightness(image_folder, output_folder, net, args['glass_threshold'])
        
        # ig glass_mask
        # parameter set
        ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        exp_name = 'IG-SLAM'
        args = {
            'snapshot': 'fofin',
            'scale': 416,
            'crf': True,
            'glass_threshold': 0.5,  # 玻璃区域识别阈值
        }

        net = GDNet(backbone_path=backbone_path).cuda(device_ids[0])
        if len(args['snapshot']) > 0:
            print(f'Load snapshot {args["snapshot"]} for testing')
            load_path = torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth'))
            if args["snapshot"]=="200":
                net.load_state_dict(load_path)
            else:
                net.load_state_dict(load_path['model'])
            print(f'Load {os.path.join(ckpt_path, exp_name, args["snapshot"] + ".pth")} succeed!')

        net.eval()
        current = "glass_mask_net"
        output_folder = path_set(tempdata_path,current)
        brightness_list, feature_points_list = glass_mask.detect_glass_and_adjust_brightness(image_folder, output_folder, net, args['glass_threshold'])
        
if __name__ == '__main__':
    main()