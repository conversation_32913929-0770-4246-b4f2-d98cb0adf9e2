#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化Proteus ViT-B测试脚本
验证ViT优化效果，对比原始ViT和SCSA性能

主要功能：
1. 测试优化后的Proteus ViT-B性能
2. 与原始ViT和SCSA基准对比
3. 详细的性能分析和可视化
"""

import os
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from tqdm import tqdm
import argparse
import time

# 添加项目路径
import sys
sys.path.append('ig_glass')

from gdnet_optimized_vit import create_optimized_vit_model
from gdnet_proteus_vits import create_proteus_vits_model
from gdnet_scsa import GDNetSCSA
from misc import (compute_iou, compute_fmeasure, compute_ber, compute_acc, 
                 compute_mae, compute_precision_recall, compute_aber)


class OptimizedProteusViTBTester:
    """优化Proteus ViT-B测试器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化模型
        self._init_models()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((args.input_size, args.input_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        print(f"🚀 优化Proteus ViT-B测试器初始化完成")
        print(f"   设备: {self.device}")
        print(f"   模型数量: {len(self.models)}")
    
    def _init_models(self):
        """初始化对比模型"""
        self.models = {}
        
        # 1. 优化Proteus ViT-B模型
        if os.path.exists(self.args.optimized_model_path):
            print("📦 加载优化Proteus ViT-B模型...")
            model = create_optimized_vit_model(
                backbone_path=self.args.backbone_path,
                crf_iter=self.args.crf_iter
            )
            checkpoint = torch.load(self.args.optimized_model_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device).eval()
            self.models['optimized_vitb'] = model
            print(f"   ✅ 优化ViT-B加载成功")
        
        # 2. 原始Proteus ViT-B模型
        if self.args.include_original_vitb and os.path.exists(self.args.original_vitb_path):
            print("📦 加载原始Proteus ViT-B模型...")
            model = create_proteus_vits_model(
                backbone_path=self.args.backbone_path,
                crf_iter=self.args.crf_iter,
                vit_type='vitb'
            )
            checkpoint = torch.load(self.args.original_vitb_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'] if 'model_state_dict' in checkpoint else checkpoint)
            model.to(self.device).eval()
            self.models['original_vitb'] = model
            print(f"   ✅ 原始ViT-B加载成功")
        
        # 3. SCSA基准模型
        if self.args.include_scsa and os.path.exists(self.args.scsa_model_path):
            print("📦 加载SCSA基准模型...")
            model = GDNetSCSA(backbone_path=self.args.backbone_path)
            checkpoint = torch.load(self.args.scsa_model_path, map_location=self.device)
            model.load_state_dict(checkpoint)
            model.to(self.device).eval()
            self.models['scsa_baseline'] = model
            print(f"   ✅ SCSA基准加载成功")
        
        if not self.models:
            raise ValueError("没有找到有效的模型文件！")
        
        # 打印模型参数量
        for name, model in self.models.items():
            param_count = sum(p.numel() for p in model.parameters()) / 1e6
            print(f"   {name}: {param_count:.1f}M 参数")
    
    def test_single_image(self, image_path, gt_path=None):
        """测试单张图像"""
        # 加载图像
        image = Image.open(image_path).convert('RGB')
        original_size = image.size
        
        # 预处理
        input_tensor = self.transform(image).unsqueeze(0).to(self.device)
        
        # 多模型预测
        predictions = {}
        inference_times = {}
        
        with torch.no_grad():
            for model_name, model in self.models.items():
                try:
                    start_time = time.time()
                    
                    # 前向传播
                    output = model(input_tensor)
                    
                    inference_time = time.time() - start_time
                    inference_times[model_name] = inference_time
                    
                    # 处理不同模型的输出格式
                    if model_name == 'scsa_baseline':
                        if isinstance(output, dict):
                            pred = output.get('refined_pred', output.get('pred', None))
                        else:
                            pred = output
                        
                        if pred is not None and pred.dim() == 4 and pred.size(1) == 2:
                            pred = pred[:, 1:2]  # 取前景通道
                    else:  # ViT模型
                        if isinstance(output, dict):
                            pred = output.get('refined_pred', output.get('pred'))
                            if pred.dim() == 4 and pred.size(1) == 2:
                                pred = pred[:, 1:2]  # 取前景通道
                        else:
                            pred = output
                    
                    # 转换为numpy并调整尺寸
                    pred_np = pred.squeeze().cpu().numpy()
                    pred_resized = cv2.resize(pred_np, original_size, interpolation=cv2.INTER_LINEAR)
                    
                    predictions[model_name] = pred_resized
                    
                except Exception as e:
                    print(f"⚠️ 模型 {model_name} 预测失败: {e}")
                    continue
        
        # 计算指标（如果有真值）
        metrics = {}
        if gt_path and os.path.exists(gt_path):
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
            if gt_mask.shape != predictions[list(predictions.keys())[0]].shape:
                gt_mask = cv2.resize(gt_mask, original_size)
            
            gt_normalized = gt_mask.astype(np.float32) / 255.0
            
            for model_name, pred in predictions.items():
                # 二值化预测
                pred_binary = (pred > 0.5).astype(np.float32)
                
                # 计算指标
                model_metrics = {
                    'iou': compute_iou(pred_binary, gt_normalized),
                    'accuracy': compute_acc(pred_binary, gt_normalized),
                    'mae': compute_mae(pred_binary, gt_normalized),
                    'ber': compute_ber(pred_binary, gt_normalized),
                    'aber': compute_aber(pred_binary, gt_normalized),
                    'inference_time': inference_times.get(model_name, 0)
                }
                
                # 计算F-measure
                precision, recall = compute_precision_recall(pred_binary, gt_normalized)
                model_metrics['f_measure'] = compute_fmeasure(precision, recall)
                
                metrics[model_name] = model_metrics
        
        return predictions, metrics
    
    def test_dataset(self, image_folder, gt_folder, output_folder):
        """测试整个数据集"""
        os.makedirs(output_folder, exist_ok=True)
        
        image_files = [f for f in os.listdir(image_folder) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        all_metrics = {name: [] for name in self.models.keys()}
        
        print(f"🔍 开始测试 {len(image_files)} 张图像...")
        
        for image_file in tqdm(image_files, desc="测试进度"):
            try:
                image_path = os.path.join(image_folder, image_file)
                gt_path = os.path.join(gt_folder, image_file.replace('.jpg', '.png'))
                
                # 测试图像
                predictions, metrics = self.test_single_image(image_path, gt_path)
                
                # 保存结果
                for model_name, pred in predictions.items():
                    model_output_folder = os.path.join(output_folder, model_name)
                    os.makedirs(model_output_folder, exist_ok=True)
                    result_path = os.path.join(model_output_folder, image_file.replace('.jpg', '.png'))
                    cv2.imwrite(result_path, (pred * 255).astype(np.uint8))
                
                # 收集指标
                for model_name, model_metrics in metrics.items():
                    all_metrics[model_name].append(model_metrics)
                
            except Exception as e:
                print(f"❌ 处理图像 {image_file} 失败: {e}")
                continue
        
        # 计算平均指标并打印对比结果
        print("\n" + "="*80)
        print("🎯 ViT优化效果对比分析")
        print("="*80)
        
        avg_metrics = {}
        for model_name, metrics_list in all_metrics.items():
            if metrics_list:
                avg_metrics[model_name] = {}
                for key in metrics_list[0].keys():
                    avg_metrics[model_name][key] = np.mean([m[key] for m in metrics_list])
        
        # 打印详细对比
        for model_name, metrics in avg_metrics.items():
            print(f"\n📊 {model_name.upper()} 性能:")
            print(f"   IoU: {metrics['iou']:.4f}")
            print(f"   Accuracy: {metrics['accuracy']:.4f}")
            print(f"   F-measure: {metrics['f_measure']:.4f}")
            print(f"   MAE: {metrics['mae']:.4f}")
            print(f"   推理时间: {metrics['inference_time']*1000:.2f}ms")
        
        # 性能改进分析
        if 'optimized_vitb' in avg_metrics:
            opt_iou = avg_metrics['optimized_vitb']['iou']
            print(f"\n🎉 优化ViT-B性能: IoU = {opt_iou:.4f}")
            
            if 'original_vitb' in avg_metrics:
                orig_iou = avg_metrics['original_vitb']['iou']
                improvement = opt_iou - orig_iou
                print(f"📈 相比原始ViT-B改进: {improvement:+.4f} ({improvement/orig_iou*100:+.1f}%)")
            
            if 'scsa_baseline' in avg_metrics:
                scsa_iou = avg_metrics['scsa_baseline']['iou']
                vs_scsa = opt_iou - scsa_iou
                print(f"🆚 相比SCSA基准: {vs_scsa:+.4f} ({vs_scsa/scsa_iou*100:+.1f}%)")
            
            # 检查是否解决了ViT效果不佳的问题
            if opt_iou >= 0.85:
                print(f"✅ 成功解决ViT效果不佳问题！(目标85%+)")
            if opt_iou >= 0.90:
                print(f"🏆 突破90% IoU目标！ViT优化大成功！")
            else:
                print(f"📈 距离90%目标还差: {0.90 - opt_iou:.4f}")
        
        print("="*80)
        
        return avg_metrics


def main():
    parser = argparse.ArgumentParser(description='Optimized Proteus ViT-B Testing')
    parser.add_argument('--backbone_path', type=str,
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/proteus_vitb_backbone.pth',
                       help='Proteus ViT-B backbone weights path')
    parser.add_argument('--optimized_model_path', type=str,
                       default='checkpoints/optimized_proteus_vitb/best.pth',
                       help='Optimized ViT-B model path')
    parser.add_argument('--original_vitb_path', type=str,
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/vitb-msd-100.pth',
                       help='Original ViT-B model path')
    parser.add_argument('--scsa_model_path', type=str,
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/scsa-30-150.pth',
                       help='SCSA baseline model path')
    parser.add_argument('--input_size', type=int, default=416, help='Input image size')
    parser.add_argument('--crf_iter', type=int, default=5, help='CRF iterations')
    parser.add_argument('--image_folder', type=str,
                       default='/home/<USER>/Documents/ig_slam_maskdata/test_GDD/image',
                       help='Test images folder')
    parser.add_argument('--gt_folder', type=str,
                       default='/home/<USER>/Documents/ig_slam_maskdata/test_GDD/mask',
                       help='Ground truth masks folder')
    parser.add_argument('--output_folder', type=str,
                       default='results/optimized_vitb_comparison',
                       help='Output results folder')
    parser.add_argument('--include_original_vitb', action='store_true',
                       help='Include original ViT-B for comparison')
    parser.add_argument('--include_scsa', action='store_true',
                       help='Include SCSA baseline for comparison')
    
    args = parser.parse_args()
    
    # 创建测试器并运行测试
    tester = OptimizedProteusViTBTester(args)
    metrics = tester.test_dataset(args.image_folder, args.gt_folder, args.output_folder)
    
    if metrics:
        print(f"\n✅ 测试完成！")
        if 'optimized_vitb' in metrics:
            opt_iou = metrics['optimized_vitb']['iou']
            print(f"🎯 优化ViT-B最终IoU: {opt_iou:.4f}")
            if opt_iou >= 0.85:
                print(f"🎉 成功解决ViT效果不佳问题！")
    else:
        print("❌ 测试失败，没有获得有效指标")


if __name__ == '__main__':
    main()
