#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Proteus ViT-S Engine风格训练脚本
基于SCSA的Engine架构，集成原版Proteus ViT-S
"""

import os
import sys
import time
import random
import argparse
import numpy as np
from datetime import datetime

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")

# 添加项目路径
sys.path.append('ig_glass')

from gdnet_proteus_vits import create_proteus_vits_model
from loss_proteus_vits import create_proteus_vits_loss
from glass_dataloader import GlassDataLoader


def set_random_seed(seed=42):
    """设置随机种子以确保结果可复现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)
    print(f"🎲 随机种子已设置为: {seed}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Proteus ViT-S Engine训练')
    
    # 基本训练参数
    parser.add_argument('--epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=8, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--trainsize', type=int, default=416, help='训练图像尺寸')
    
    # 数据路径
    parser.add_argument('--data_mode', type=str, default='train', 
                       choices=['train', 'train_gdd', 'mix_train'],
                       help='数据集模式')
    parser.add_argument('--val_mode', type=str, default='test',
                       choices=['test', 'test_gdd', 'mix_test'],
                       help='验证集模式')
    
    # 模型参数
    parser.add_argument('--backbone_path', type=str, 
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/proteus_vitb_backbone.pth',
                       help='Proteus预训练权重路径')
    parser.add_argument('--crf_iter', type=int, default=3, help='CRF迭代次数')
    parser.add_argument('--trainable_crf', action='store_true', help='是否训练CRF参数')
    
    # 优化器参数
    parser.add_argument('--optimizer', type=str, default='AdamW', 
                       choices=['Adam', 'AdamW', 'SGD'], help='优化器类型')
    parser.add_argument('--scheduler', type=str, default='CosineAnnealingLR',
                       choices=['CosineAnnealingLR', 'StepLR', 'MultiStepLR'],
                       help='学习率调度器')
    parser.add_argument('--warmup_epochs', type=int, default=5, help='预热轮数')
    
    # 损失函数权重
    parser.add_argument('--bce_weight', type=float, default=0.1, help='BCE损失权重')
    parser.add_argument('--iou_weight', type=float, default=0.7, help='IoU损失权重')
    parser.add_argument('--edge_weight', type=float, default=0.2, help='边缘损失权重')
    parser.add_argument('--physics_weight', type=float, default=0.1, help='物理损失权重')
    parser.add_argument('--consistency_weight', type=float, default=0.05, help='一致性损失权重')
    
    # 训练控制
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载线程数')
    parser.add_argument('--save_freq', type=int, default=5, help='模型保存频率')
    parser.add_argument('--val_freq', type=int, default=1, help='验证频率')
    parser.add_argument('--print_freq', type=int, default=10, help='打印频率')
    
    # 早停和保存
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    parser.add_argument('--min_delta', type=float, default=0.001, help='早停最小改进')
    parser.add_argument('--save_dir', type=str, 
                       default='ig_glass/models_proteus_vits',
                       help='模型保存目录')
    
    # 随机种子和设备
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--gpu', type=str, default='0', help='GPU设备')
    
    # Glass数据增强
    parser.add_argument('--glass_augmentation', type=str, default='strong',
                       choices=['none', 'light', 'moderate', 'strong'],
                       help='玻璃感知数据增强强度')
    
    return parser.parse_args()


class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=10, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        
    def step(self, val_score):
        if self.best_score is None:
            self.best_score = val_score
        elif val_score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = val_score
            self.counter = 0
        
        return self.early_stop


class Engine:
    """Proteus ViT-S训练引擎"""
    
    def __init__(self, args):
        self.args = args
        
        # 设置随机种子
        set_random_seed(args.seed)
        
        # 设置设备
        os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  使用设备: {self.device}")
        
        # 创建保存目录
        self.save_dir = os.path.join(args.save_dir, f'ProteusViTS-Engine-{datetime.now().strftime("%Y%m%d-%H%M%S")}')
        os.makedirs(self.save_dir, exist_ok=True)
        os.makedirs(os.path.join(self.save_dir, 'weights'), exist_ok=True)
        os.makedirs(os.path.join(self.save_dir, 'logs'), exist_ok=True)
        
        # TensorBoard
        self.writer = SummaryWriter(os.path.join(self.save_dir, 'logs'))
        
        # 初始化模型、损失函数、优化器、数据加载器
        self._init_model()
        self._init_criterion()
        self._init_dataloaders()
        self._init_optimizer()
        
        # 早停机制
        self.early_stopping = EarlyStopping(patience=args.patience, min_delta=args.min_delta)
        
        # 最佳指标记录
        self.best_iou = 0.0
        self.best_mae = float('inf')
        self.start_epoch = 0
        
        print(f"🚀 Proteus ViT-S Engine初始化完成")
        print(f"   模型参数: {sum(p.numel() for p in self.model.parameters())/1e6:.1f}M")
        print(f"   保存目录: {self.save_dir}")
        
    def _init_model(self):
        """初始化模型"""
        print("📦 初始化Proteus ViT-S模型...")
        self.model = create_proteus_vits_model(
            backbone_path=self.args.backbone_path,
            crf_iter=self.args.crf_iter,
            trainable_crf=self.args.trainable_crf
        ).to(self.device)
        
    def _init_criterion(self):
        """初始化损失函数"""
        print("🎯 初始化损失函数...")
        self.criterion = create_proteus_vits_loss(
            bce_weight=self.args.bce_weight,
            iou_weight=self.args.iou_weight,
            edge_weight=self.args.edge_weight
        ).to(self.device)
        
    def _init_dataloaders(self):
        """初始化数据加载器"""
        print("📊 初始化数据加载器...")
        
        # 训练数据集
        train_dataset = GlassDataLoader(
            mode=self.args.data_mode,
            augment_data=True,
            target_size=self.args.trainsize,
            glass_augmentation=self.args.glass_augmentation
        )
        
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        # 验证数据集
        val_dataset = GlassDataLoader(
            mode=self.args.val_mode,
            augment_data=False,
            target_size=self.args.trainsize,
            glass_augmentation='none'
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            pin_memory=True
        )
        
        print(f"   训练样本: {len(train_dataset)}")
        print(f"   验证样本: {len(val_dataset)}")
        print(f"   批次大小: {self.args.batch_size}")
        print(f"   图像尺寸: {self.args.trainsize}x{self.args.trainsize}")
        
    def _init_optimizer(self):
        """初始化优化器和调度器"""
        print("⚙️  初始化优化器...")
        
        # 分层学习率 - ViT backbone使用较小学习率
        vit_params = []
        other_params = []
        
        for name, param in self.model.named_parameters():
            if 'vit_backbone' in name:
                vit_params.append(param)
            else:
                other_params.append(param)
        
        # 创建优化器
        if self.args.optimizer == 'AdamW':
            self.optimizer = optim.AdamW([
                {'params': vit_params, 'lr': self.args.lr * 0.1, 'weight_decay': self.args.weight_decay},
                {'params': other_params, 'lr': self.args.lr, 'weight_decay': self.args.weight_decay}
            ])
        elif self.args.optimizer == 'Adam':
            self.optimizer = optim.Adam([
                {'params': vit_params, 'lr': self.args.lr * 0.1, 'weight_decay': self.args.weight_decay},
                {'params': other_params, 'lr': self.args.lr, 'weight_decay': self.args.weight_decay}
            ])
        elif self.args.optimizer == 'SGD':
            self.optimizer = optim.SGD([
                {'params': vit_params, 'lr': self.args.lr * 0.1, 'weight_decay': self.args.weight_decay, 'momentum': 0.9},
                {'params': other_params, 'lr': self.args.lr, 'weight_decay': self.args.weight_decay, 'momentum': 0.9}
            ])
        
        # 创建学习率调度器
        if self.args.scheduler == 'CosineAnnealingLR':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, 
                T_max=self.args.epochs,
                eta_min=self.args.lr * 0.01
            )
        elif self.args.scheduler == 'StepLR':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, 
                step_size=20, 
                gamma=0.1
            )
        elif self.args.scheduler == 'MultiStepLR':
            self.scheduler = optim.lr_scheduler.MultiStepLR(
                self.optimizer, 
                milestones=[20, 35], 
                gamma=0.1
            )
        
        # 学习率预热
        if self.args.warmup_epochs > 0:
            self.warmup_scheduler = optim.lr_scheduler.LinearLR(
                self.optimizer,
                start_factor=0.1,
                total_iters=self.args.warmup_epochs
            )
        else:
            self.warmup_scheduler = None
            
        print(f"   优化器: {self.args.optimizer}")
        print(f"   调度器: {self.args.scheduler}")
        print(f"   预热轮数: {self.args.warmup_epochs}")
        
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        loss_components = {
            'bce': 0.0, 'iou': 0.0, 'edge': 0.0
        }
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.args.epochs}')
        
        for i, (images, masks) in enumerate(pbar):
            images = images.to(self.device)
            masks = masks.to(self.device)
            
            # 前向传播
            outputs = self.model(images)
            
            # 计算损失
            loss, loss_dict = self.criterion(outputs, masks, images)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 统计损失
            total_loss += loss.item()
            for key in loss_components:
                if f'{key}_loss' in loss_dict:
                    loss_components[key] += loss_dict[f'{key}_loss'].item()
            
            # 更新进度条 - 每10个批次更新一次显示
            if i % 10 == 0 or i == len(self.train_loader) - 1:
                pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'IoU': f'{loss_dict.get("iou_loss", 0):.4f}',
                    'BCE': f'{loss_dict.get("bce_loss", 0):.4f}',
                    'Edge': f'{loss_dict.get("edge_loss", 0):.4f}'
                })
            
            # 记录到TensorBoard
            step = epoch * len(self.train_loader) + i
            if i % self.args.print_freq == 0:
                self.writer.add_scalar('Train/Loss', loss.item(), step)
                for key, value in loss_dict.items():
                    if key != 'dynamic_weights' and isinstance(value, torch.Tensor):
                        self.writer.add_scalar(f'Train/{key}', value.item(), step)
        
        # 学习率调度
        if epoch < self.args.warmup_epochs and self.warmup_scheduler:
            self.warmup_scheduler.step()
        else:
            self.scheduler.step()
        
        # 平均损失
        avg_loss = total_loss / len(self.train_loader)
        for key in loss_components:
            loss_components[key] /= len(self.train_loader)
        
        return avg_loss, loss_components
    
    def validate(self, epoch):
        """验证"""
        self.model.eval()
        total_loss = 0.0
        total_iou = 0.0
        total_mae = 0.0
        
        with torch.no_grad():
            for images, masks in tqdm(self.val_loader, desc='Validating'):
                images = images.to(self.device)
                masks = masks.to(self.device)
                
                # 前向传播
                outputs = self.model(images)
                
                # 计算损失
                loss, loss_dict = self.criterion(outputs, masks, images)
                total_loss += loss.item()
                
                # 计算IoU和MAE
                pred = torch.sigmoid(outputs['ensemble_pred'])
                iou = self._calculate_iou(pred, masks)
                mae = self._calculate_mae(pred, masks)
                
                total_iou += iou
                total_mae += mae
        
        avg_loss = total_loss / len(self.val_loader)
        avg_iou = total_iou / len(self.val_loader)
        avg_mae = total_mae / len(self.val_loader)
        
        # 记录到TensorBoard
        self.writer.add_scalar('Val/Loss', avg_loss, epoch)
        self.writer.add_scalar('Val/IoU', avg_iou, epoch)
        self.writer.add_scalar('Val/MAE', avg_mae, epoch)
        
        return avg_iou, avg_mae
    
    def _calculate_iou(self, pred, target):
        """计算IoU"""
        pred_binary = (pred > 0.5).float()
        target_binary = (target > 0.5).float()
        
        intersection = (pred_binary * target_binary).sum()
        union = pred_binary.sum() + target_binary.sum() - intersection
        
        if union == 0:
            return 1.0
        return (intersection / union).item()
    
    def _calculate_mae(self, pred, target):
        """计算MAE"""
        return torch.abs(pred - target).mean().item()
    
    def save_checkpoint(self, epoch, iou, mae, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_iou': self.best_iou,
            'best_mae': self.best_mae,
            'args': self.args
        }
        
        # 保存最新检查点
        torch.save(checkpoint, os.path.join(self.save_dir, 'weights', 'latest.pth'))
        
        # 保存最佳模型
        if is_best:
            torch.save(checkpoint, os.path.join(self.save_dir, 'weights', 'best.pth'))
            print(f"✅ 保存最佳模型: IoU={iou:.4f}, MAE={mae:.4f}")
        
        # 定期保存
        if epoch % self.args.save_freq == 0:
            torch.save(checkpoint, os.path.join(self.save_dir, 'weights', f'epoch_{epoch}.pth'))
    
    def load_checkpoint(self, checkpoint_path):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.best_iou = checkpoint['best_iou']
        self.best_mae = checkpoint['best_mae']
        self.start_epoch = checkpoint['epoch'] + 1
        print(f"✅ 加载检查点: epoch={checkpoint['epoch']}, IoU={self.best_iou:.4f}")
    
    def train(self):
        """主训练循环"""
        print(f"\n🚀 开始训练 Proteus ViT-S Engine")
        print(f"   目标: 超越SCSA的87.9% IoU")
        print(f"   策略: 原版Proteus + Glass Physics + SCSA优势")
        print("=" * 60)
        
        start_time = time.time()
        
        for epoch in range(self.start_epoch, self.args.epochs):
            epoch_start_time = time.time()
            
            # 训练
            train_loss, loss_components = self.train_epoch(epoch)
            
            # 验证
            if epoch % self.args.val_freq == 0:
                val_iou, val_mae = self.validate(epoch)
            else:
                val_iou, val_mae = 0.0, 0.0
            
            # 更新最佳指标
            is_best = val_iou > self.best_iou
            if is_best:
                self.best_iou = val_iou
                self.best_mae = val_mae
            
            # 保存模型
            if epoch % self.args.val_freq == 0:
                self.save_checkpoint(epoch, val_iou, val_mae, is_best)
            
            # 早停检查
            if epoch % self.args.val_freq == 0:
                if self.early_stopping.step(val_iou):
                    print(f"🛑 早停触发，停止训练")
                    break
            
            # 打印epoch结果
            epoch_time = time.time() - epoch_start_time
            current_lr = self.optimizer.param_groups[0]['lr']
            
            print(f"\nEpoch {epoch+1}/{self.args.epochs} | Time: {epoch_time:.1f}s")
            print(f"Train Loss: {train_loss:.4f} | LR: {current_lr:.6f}")
            print(f"Loss Components - IoU: {loss_components['iou']:.4f}, "
                  f"BCE: {loss_components['bce']:.4f}, "
                  f"Edge: {loss_components['edge']:.4f}")
            if epoch % self.args.val_freq == 0:
                print(f"Val IoU: {val_iou:.4f} | Val MAE: {val_mae:.4f}")
            print("-" * 60)
        
        total_time = time.time() - start_time
        print(f"\n🎉 训练完成!")
        print(f"   最佳IoU: {self.best_iou:.4f}")
        print(f"   最佳MAE: {self.best_mae:.4f}")
        print(f"   总时间: {total_time/3600:.2f}小时")
        print(f"   模型保存在: {self.save_dir}")
        
        self.writer.close()


def main():
    """主函数"""
    args = parse_arguments()
    
    print("🌟 Proteus ViT-S Engine训练系统")
    print("=" * 60)
    print(f"配置参数:")
    for key, value in vars(args).items():
        print(f"  {key}: {value}")
    print("=" * 60)
    
    # 创建训练引擎
    engine = Engine(args)
    
    # 开始训练
    engine.train()


if __name__ == '__main__':
    main() 