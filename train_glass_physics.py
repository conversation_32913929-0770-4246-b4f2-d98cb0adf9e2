"""
基于玻璃物理特性的训练脚本
测试新的物理特性检测方法
"""
from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

import os
import time
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from torchvision import transforms
import numpy as np
from tqdm import tqdm

from ig_glass.gdnet_glass_physics_progressive import create_progressive_glass_physics_model
from ig_glass.loss_glass_physics import GlassPhysicsLoss
from ig_glass.loss_glass_physics_simple import SimpleGlassPhysicsLoss
from ig_glass.loss_glass_physics_progressive import ProgressiveGlassPhysicsLoss
from ig_glass.dataloader_fixed import SODLoader
from ig_glass.dataloader import FODLoader
from ig_glass.gdnet_glass_physics_progressive import create_progressive_glass_physics_model
from ig_glass.loss_glass_physics_progressive import ProgressiveGlassPhysicsLoss


def parse_arguments():
    parser = argparse.ArgumentParser(description='玻璃物理特性模型训练参数')
    parser.add_argument('--epochs', default=302, help='训练轮数', type=int)
    parser.add_argument('--bs', default=6, help='批次大小', type=int)
    parser.add_argument('--lr', default=0.0005, help='学习率', type=float)
    parser.add_argument('--wd', default=0.0005, help='L2权重衰减参数', type=float)
    parser.add_argument('--img_size', default=416, help='训练图像大小', type=int)
    parser.add_argument('--aug', default=True, help='是否使用数据增强', type=bool)
    parser.add_argument('--n_worker', default=4, help='数据加载线程数', type=int)
    parser.add_argument('--test_interval', default=10, help='测试间隔（多少个epoch）', type=int)
    parser.add_argument('--save_interval', default=20, help='保存间隔（多少个epoch）', type=int)
    parser.add_argument('--save_opt', default=False, help='是否同时保存优化器', type=bool)
    parser.add_argument('--log_interval', default=250, help='日志记录间隔（批次数）', type=int)
    parser.add_argument('--res_mod', default = './ckpt', help='恢复模型的路径', type=str)
    parser.add_argument('--res_opt', default=None, help='恢复优化器的路径', type=str)
    parser.add_argument('--use_gpu', default=True, help='是否使用GPU', type=bool)
    parser.add_argument('--base_save_path', default='/home/<USER>/ws/IG_SLAM/ig_glass/models_glass_physics', help='模型保存基础路径', type=str)
    parser.add_argument('--crf_iter', default=3, help='CRF迭代次数', type=int)
    parser.add_argument('--trainable_crf', default=True, help='CRF参数是否可训练', type=bool)
    
    # 物理特性损失权重 - 100次后训练修改参数配置
    parser.add_argument('--main_weight', default=0.4, help='主预测损失权重', type=float)
    parser.add_argument('--physics_weight', default=0.2, help='物理预测损失权重', type=float)
    parser.add_argument('--final_weight', default=0.3, help='最终预测损失权重', type=float)
    parser.add_argument('--consistency_weight', default=0.1, help='物理一致性损失权重', type=float)
    parser.add_argument('--edge_weight', default=0.05, help='边缘损失权重', type=float)
    parser.add_argument('--iou_weight', default=0.4, help='IoU损失权重', type=float)
    
    # CRF和其他参数
    parser.add_argument('--bilateral_weight', default=8.0, help='双边滤波权重', type=float)
    parser.add_argument('--gaussian_weight', default=4.0, help='高斯滤波权重', type=float)
    parser.add_argument('--bilateral_spatial_sigma', default=35.0, help='双边滤波的空间sigma', type=float)
    parser.add_argument('--bilateral_color_sigma', default=2.5, help='双边滤波的颜色sigma', type=float)
    parser.add_argument('--gaussian_sigma', default=1.2, help='高斯滤波的sigma', type=float)
    
    parser.add_argument('--use_kfold', default=False, help='是否使用k折交叉验证', type=bool)
    parser.add_argument('--use_simple_loss', default=False, help='是否使用简化损失函数进行稳定性测试', type=bool)

    return parser.parse_args()


class EarlyStopping:
    def __init__(self, patience=7, min_delta=0.001):
        """
        patience: 无改进后终止训练的轮数
        min_delta: 被视为改进的最小变化量
        """
        self.patience = patience
        self.min_delta = min_delta
        self.best_loss = float('inf')
        self.counter = 0

    def step(self, val_loss):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0  # 重置计数器
        else:
            self.counter += 1  # 无改进，增加计数

        return self.counter >= self.patience  # 如果超过耐心值，返回True


class Engine:
    def __init__(self, args):
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.wd = args.wd
        self.img_size = args.img_size
        self.aug = args.aug
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.save_opt = args.save_opt
        self.log_interval = args.log_interval
        self.res_mod_path = args.res_mod
        self.res_opt_path = args.res_opt
        self.use_gpu = args.use_gpu
        self.crf_iter = args.crf_iter
        self.trainable_crf = args.trainable_crf
        self.use_kfold = args.use_kfold
        self.use_simple_loss = args.use_simple_loss
        
        # 损失权重
        self.main_weight = args.main_weight
        self.physics_weight = args.physics_weight
        self.final_weight = args.final_weight
        self.consistency_weight = args.consistency_weight
        self.edge_weight = args.edge_weight
        self.iou_weight = args.iou_weight

        # CRF参数
        self.bilateral_weight = args.bilateral_weight
        self.gaussian_weight = args.gaussian_weight
        self.bilateral_spatial_sigma = args.bilateral_spatial_sigma
        self.bilateral_color_sigma = args.bilateral_color_sigma
        self.gaussian_sigma = args.gaussian_sigma

        self.model_path = args.base_save_path + '/IG-SLAM-GlassPhysics'
        print('模型将保存在: {}\n'.format(self.model_path))
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))

        if torch.cuda.is_available():
            self.device = torch.device(device='cuda')
        else:
            self.device = torch.device(device='cpu')

        # 初始化渐进式玻璃物理特性模型
        self.model = create_progressive_glass_physics_model(
            backbone_path='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth',
            crf_iter=self.crf_iter,
            trainable_crf=self.trainable_crf
        )

        # 更新CRF参数
        if hasattr(self.model, 'crf'):
            self.model.crf.bilateral_weight = nn.Parameter(torch.tensor(self.bilateral_weight)) if self.trainable_crf else self.bilateral_weight
            self.model.crf.gaussian_weight = nn.Parameter(torch.tensor(self.gaussian_weight)) if self.trainable_crf else self.gaussian_weight
            self.model.crf.bilateral_spatial_sigma = nn.Parameter(torch.tensor(self.bilateral_spatial_sigma)) if self.trainable_crf else self.bilateral_spatial_sigma
            self.model.crf.bilateral_color_sigma = nn.Parameter(torch.tensor(self.bilateral_color_sigma)) if self.trainable_crf else self.bilateral_color_sigma
            self.model.crf.gaussian_sigma = nn.Parameter(torch.tensor(self.gaussian_sigma)) if self.trainable_crf else self.gaussian_sigma

        self.model.to(self.device)

        # 初始化损失函数 - 使用渐进式版本
        if self.use_simple_loss:
            print("使用简化损失函数进行稳定性测试")
            self.criterion = SimpleGlassPhysicsLoss(
                device=self.device,
                focal_weight=0.6,
                iou_weight=0.4
            )
        else:
            print("使用渐进式物理特性损失函数")
            self.criterion = ProgressiveGlassPhysicsLoss(
                device=self.device,
                focal_weight=0.2, #0.15,
                iou_weight=0.5, #0.45,
                edge_weight=0.3, #0.4,
                multiscale_edge_weight=0.1, #0.2,
                consistency_weight=0.1,
                crf_weight=0.05
            )

        # 初始化优化器，对不同参数应用不同的权重衰减
        params = self._split_params_for_weight_decay(self.model)
        self.optimizer = optim.SGD(params, lr=self.lr, momentum=0.9)

        # 如果是恢复训练，加载模型和优化器
        ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        exp_name = 'IG-SLAM'
        args = {
            'snapshot': 'physics-100-60',
            'scale': 416,
            'crf': False
        }

        # 如果是恢复训练，加载模型和优化器
        if self.res_mod_path is not None and os.path.exists(ckpt_path):
            chkpt = os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')
            print(f"从以下路径加载检查点: {chkpt}")
            try:
                checkpoint = torch.load(chkpt)
                if 'model_state_dict' in checkpoint:
                    self.model.load_state_dict(checkpoint['model_state_dict'], strict=False)
                else:
                    self.model.load_state_dict(checkpoint, strict=False)
                print("成功加载模型权重")
            except Exception as e:
                print(f"加载模型失败: {e}")

        # 设置早停机制
        self.early_stopping = EarlyStopping(patience=7)
        
        # 初始化TensorBoard writers
        log_dir_train = os.path.join(self.model_path, 'logs', 'train')
        log_dir_val = os.path.join(self.model_path, 'logs', 'validation')
        os.makedirs(log_dir_train, exist_ok=True)
        os.makedirs(log_dir_val, exist_ok=True)
        
        self.writer_train = SummaryWriter(log_dir_train)
        self.writer_val = SummaryWriter(log_dir_val)

    def _split_params_for_weight_decay(self, model):
        """
        将参数分为两组:
        - 带权重衰减: 卷积和线性层的权重
        - 不带权重衰减: 所有偏置和批归一化参数
        """
        decay = []
        no_decay = []

        for name, param in model.named_parameters():
            if not param.requires_grad:
                continue
            if 'weight' in name and 'bn' not in name and 'batch_norm' not in name:
                decay.append(param)
            else:
                no_decay.append(param)

        return [
            {'params': decay, 'weight_decay': self.wd},
            {'params': no_decay, 'weight_decay': 0.0}
        ]

    def train(self):
        """训练玻璃物理特性模型"""
        print('开始训练...\n')

        # 设置k-fold范围
        k_range = range(5) if self.use_kfold else range(1)

        for k in k_range:
            if self.use_kfold:
                print(f'K-折交叉验证训练: 第 {k+1} 折')
                self.train_data = FODLoader(mode='train', augment_data=self.aug, target_size=self.img_size, k=k)
                self.test_data = FODLoader(mode='test', augment_data=False, target_size=self.img_size, k=k)
            else:
                self.train_data = SODLoader(mode='train_gdd', augment_data=self.aug, target_size=self.img_size)
                self.test_data = SODLoader(mode='test_gdd', augment_data=False, target_size=self.img_size)

            self.train_dataloader = DataLoader(self.train_data, batch_size=self.bs, shuffle=True, num_workers=self.n_worker)
            self.test_dataloader = DataLoader(self.test_data, batch_size=self.bs, shuffle=False, num_workers=self.n_worker)

            print(f"训练样本: {len(self.train_data)}, 验证样本: {len(self.test_data)}")

            # 设置学习率调度器 - 更保守的设置
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, 'min', patience=5, factor=0.7, verbose=True, min_lr=1e-6)

            best_iou = 0.0  # 改为使用IoU作为最佳模型选择指标
            best_loss = float('inf')

            # 初始化早停机制 - 改为监控IoU
            early_stopping = EarlyStopping(patience=7, min_delta=0.001)

            for epoch in range(1, self.epochs + 1):
                # 训练阶段
                self.model.train()
                running_loss = 0.0
                
                # 用于统计每轮次的损失组件和数据分布
                loss_components = {}
                epoch_positive_ratios = []

                train_tqdm = tqdm(self.train_dataloader, desc=f"轮次 {epoch}/{self.epochs}")
                for batch_idx, (inp_imgs, gt_masks) in enumerate(train_tqdm):
                    inp_imgs = inp_imgs.to(self.device)
                    gt_masks = gt_masks.to(self.device)

                    self.optimizer.zero_grad()

                    # 前向传播
                    outputs = self.model(inp_imgs)

                    # 计算损失
                    loss_dict = self.criterion(outputs, gt_masks)
                    total_loss = loss_dict['total_loss']
                    
                    # 检查损失值的有效性
                    if torch.isnan(total_loss) or torch.isinf(total_loss) or total_loss > 100.0:
                        print(f"警告: 检测到异常损失值: {total_loss.item()}")
                        print("损失详情:", {k: v.item() if hasattr(v, 'item') else v for k, v in loss_dict.items()})
                        # 跳过这个批次
                        self.optimizer.zero_grad()
                        continue

                    # 反向传播和优化
                    total_loss.backward()
                    
                    # 增强梯度裁剪，检查梯度健康状况
                    # 首先检查所有参数的梯度
                    has_nan_grad = False
                    for name, param in self.model.named_parameters():
                        if param.grad is not None:
                            if torch.isnan(param.grad).any() or torch.isinf(param.grad).any():
                                print(f"警告: 参数 {name} 的梯度包含NaN/Inf")
                                param.grad.zero_()  # 清零异常梯度
                                has_nan_grad = True
                    
                    if has_nan_grad:
                        print("检测到异常梯度，已清零，跳过此次优化")
                        continue
                    
                    # 梯度裁剪 - 恢复正常参数
                    total_norm = torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    if torch.isnan(total_norm) or torch.isinf(total_norm) or total_norm > 100.0:
                        print(f"警告: 检测到异常梯度范数: {total_norm}")
                        # 跳过这个批次的优化步骤
                        self.optimizer.zero_grad()
                        continue
                    
                    self.optimizer.step()

                    running_loss += total_loss.item()
                    
                    # 收集损失组件统计
                    for key, value in loss_dict.items():
                        if key not in loss_components:
                            loss_components[key] = 0.0
                        loss_components[key] += value.item() if hasattr(value, 'item') else value
                    
                    # 收集数据分布统计
                    gt_positive = (gt_masks > 0.5).float().sum().item()
                    gt_total = gt_masks.numel()
                    pos_ratio = gt_positive / gt_total if gt_total > 0 else 0
                    epoch_positive_ratios.append(pos_ratio)

                    # 更新进度条
                    focal_val = loss_dict.get('focal_loss', 0)
                    edge_val = loss_dict.get('edge_loss', 0)
                    iou_val = loss_dict.get('iou_loss', 0)
                    consistency_val = loss_dict.get('consistency_loss', 0)
                    multiscale_edge_val = loss_dict.get('multiscale_edge_loss', 0)
                    crf_val = loss_dict.get('crf_loss', 0)
                    
                    train_tqdm.set_postfix(
                        loss=total_loss.item(), 
                        avg_loss=running_loss/(batch_idx+1),
                        focal=focal_val.item() if hasattr(focal_val, 'item') else focal_val,
                        edge=edge_val.item() if hasattr(edge_val, 'item') else edge_val,
                        iou=iou_val.item() if hasattr(iou_val, 'item') else iou_val,
                        consistency=consistency_val.item() if hasattr(consistency_val, 'item') else consistency_val,
                        crf=crf_val.item() if hasattr(crf_val, 'item') else crf_val
                    )

                    # 记录到TensorBoard
                    if batch_idx % self.log_interval == 0:
                        global_step = (epoch - 1) * len(self.train_dataloader) + batch_idx
                        self.writer_train.add_scalar('总损失', total_loss.item(), global_step)
                        
                        # 记录各损失组件
                        for key, value in loss_dict.items():
                            if hasattr(value, 'item'):
                                self.writer_train.add_scalar(f'损失组件/{key}', value.item(), global_step)
                            else:
                                self.writer_train.add_scalar(f'损失组件/{key}', value, global_step)

                        # 记录学习率
                        current_lr = self.optimizer.param_groups[0]['lr']
                        self.writer_train.add_scalar('学习率', current_lr, global_step)
                        
                        # 计算当前批次的玻璃像素比例
                        gt_positive = (gt_masks > 0.5).float().sum().item()
                        gt_total = gt_masks.numel()
                        pos_ratio = gt_positive / gt_total if gt_total > 0 else 0
                        
                        # 记录数据分布统计
                        self.writer_train.add_scalar('数据分布/玻璃像素比例', pos_ratio, global_step)
                        self.writer_train.add_scalar('数据分布/不平衡比例', 1/pos_ratio if pos_ratio > 0 else 0, global_step)

                epoch_loss = running_loss / len(self.train_dataloader)
                
                # 计算本轮次的玻璃像素统计
                avg_positive_ratio = np.mean(epoch_positive_ratios) if epoch_positive_ratios else 0
                min_positive_ratio = np.min(epoch_positive_ratios) if epoch_positive_ratios else 0
                max_positive_ratio = np.max(epoch_positive_ratios) if epoch_positive_ratios else 0
                
                # 打印详细的损失信息和数据分布统计
                print(f"\n========== 轮次 {epoch} 训练总结 ==========")
                print(f"总损失: {epoch_loss:.6f}")
                print(f"玻璃像素占比统计: 平均{avg_positive_ratio:.3f} | 最小{min_positive_ratio:.3f} | 最大{max_positive_ratio:.3f}")
                print(f"数据不平衡程度: {1/avg_positive_ratio:.1f}:1 (背景:玻璃)" if avg_positive_ratio > 0 else "数据不平衡程度: ∞:1 (背景:玻璃)")
                for key, value in loss_components.items():
                    avg_value = value / len(self.train_dataloader)
                    print(f"  {key}: {avg_value:.6f}")
                print("=" * 45)

                # 验证阶段
                if epoch % self.test_interval == 0:
                    metrics = self.test(self.test_dataloader, epoch)
                    val_loss, accuracy, precision, recall, f1_score, mean_iou, mean_mae = metrics

                    # 根据验证损失更新调度器
                    scheduler.step(val_loss)

                    # 检查早停 - 改为监控IoU的负值（IoU越大越好，early stopping监控越小越好）
                    if early_stopping.step(-mean_iou):  # 使用负IoU，这样IoU提升时early stopping会重置
                        print(f"经过 {epoch} 轮次触发早停")
                        self._save_model(epoch, is_early_stop=True, metrics=metrics)
                        break

                    # 如果是最佳模型则保存 - 改为使用IoU
                    if mean_iou > best_iou:
                        best_iou = mean_iou
                        print(f"新的最佳模型，IoU: {best_iou:.4f}, 损失: {val_loss:.6f}, MAE: {mean_mae:.4f}")
                        self._save_model(epoch, is_best=True, metrics=metrics)

                # 定期保存
                if self.save_interval is not None and epoch % self.save_interval == 0:
                    # 如果这个epoch没有运行验证，则运行一次验证并保存指标
                    if epoch % self.test_interval != 0:
                        metrics = self.test(self.test_dataloader, epoch=0)  # 使用epoch=0避免额外的TensorBoard记录
                        self._save_model(epoch, metrics=metrics)
                    else:
                        # 如果这个epoch已经运行了验证，则使用之前的指标
                        self._save_model(epoch, metrics=metrics)

            print(f"第 {k+1} 折训练完成!")

        print("所有训练完成!")
        self.writer_train.close()
        self.writer_val.close()

    def test(self, dataloader=None, epoch=0):
        """在验证数据上测试模型"""
        self.model.eval()

        if dataloader is None:
            # 如果没有提供dataloader，则创建一个用于验证
            validation_dataset = SODLoader(mode='test_gdd', augment_data=False, target_size=self.img_size)
            dataloader = DataLoader(validation_dataset, batch_size=self.bs, shuffle=False,
                                num_workers=self.n_worker)

        running_loss = 0.0
        tp_fp = 0   # TruePositive + TrueNegative，用于准确率
        tp = 0      # TruePositive
        pred_true = 0   # 预测为'1'的数量，用于精确率
        gt_true = 0     # gt掩码中'1'的数量，用于召回率
        mae_list = []   # 保存每张图像的平均绝对误差的列表
        iou_list = []   # 保存每张图像的IoU的列表
        total_pixels = 0  # 所有测试图像的像素总数
        acc_list = []    # 保存每张图像的准确率的列表

        with torch.no_grad():
            test_tqdm = tqdm(dataloader, desc="验证")
            for batch_idx, (inp_imgs, gt_masks) in enumerate(test_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)

                # 前向传播
                outputs = self.model(inp_imgs)

                # 计算损失
                loss_dict = self.criterion(outputs, gt_masks)
                total_loss = loss_dict['total_loss']

                running_loss += total_loss.item()

                # 更新进度条
                test_tqdm.set_postfix(loss=total_loss.item(), avg_loss=running_loss/(batch_idx+1))

                # 使用ensemble_pred进行评估
                if isinstance(outputs, dict) and 'ensemble_pred' in outputs:
                    pred_masks = outputs['ensemble_pred']
                elif isinstance(outputs, dict) and 'final_pred' in outputs:
                    pred_masks = outputs['final_pred']
                else:
                    # 如果是简单的tensor输出
                    pred_masks = outputs

                # 转换为numpy进行评估
                pred_np = pred_masks.detach().cpu().numpy()
                gt_np = gt_masks.detach().cpu().numpy()

                # 计算每个批次中每张图像的指标
                for b in range(pred_np.shape[0]):
                    # 获取单张图像的预测和真实值
                    pred_single = pred_np[b, 0]  # Shape: H x W
                    gt_single = gt_np[b, 0]      # Shape: H x W

                    # 二值化预测 (阈值为0.5)
                    pred_binary = (pred_single > 0.5).astype(np.float32)

                    # 计算TP, TN, FP, FN
                    TP = np.sum(np.logical_and(pred_binary, gt_single))
                    TN = np.sum(np.logical_and(np.logical_not(pred_binary), np.logical_not(gt_single)))

                    # 计算准确率
                    N_p = np.sum(gt_single)
                    N_n = np.sum(np.logical_not(gt_single))
                    acc = (TP + TN) / (N_p + N_n)

                    # 计算IoU
                    intersection = TP
                    union = np.sum(pred_binary) + np.sum(gt_single) - intersection
                    iou = intersection / union if union > 0 else 1.0

                    # 计算MAE
                    mae = np.mean(np.abs(pred_single - gt_single))

                    # 累积指标
                    tp_fp += TP + TN
                    tp += TP
                    pred_true += np.sum(pred_binary)
                    gt_true += N_p
                    iou_list.append(iou)
                    acc_list.append(acc)
                    mae_list.append(mae)

                    # 添加到总像素计数
                    total_pixels += (N_p + N_n)

                # 记录第一个批次的预测到TensorBoard
                if batch_idx == 0 and epoch > 0:
                    self._add_prediction_to_tensorboard(pred_masks, 0, "验证预测", epoch)

        # 计算最终指标
        val_loss = running_loss / len(dataloader)
        accuracy = tp_fp / total_pixels if total_pixels > 0 else 0
        precision = tp / pred_true if pred_true > 0 else 0
        recall = tp / gt_true if gt_true > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        mean_iou = np.mean(iou_list) if iou_list else 0
        mean_mae = np.mean(mae_list) if mae_list else 0
        mean_acc = np.mean(acc_list) if acc_list else 0

        # 记录验证指标到TensorBoard
        if epoch > 0:
            self.writer_val.add_scalar('验证损失', val_loss, epoch)
            self.writer_val.add_scalar('准确率', accuracy, epoch)
            self.writer_val.add_scalar('精确率', precision, epoch)
            self.writer_val.add_scalar('召回率', recall, epoch)
            self.writer_val.add_scalar('F1分数', f1_score, epoch)
            self.writer_val.add_scalar('平均IoU', mean_iou, epoch)
            self.writer_val.add_scalar('平均MAE', mean_mae, epoch)

        print(f"验证结果 - 损失: {val_loss:.6f}, 准确率: {accuracy:.4f}, IoU: {mean_iou:.4f}, MAE: {mean_mae:.4f}")

        return val_loss, accuracy, precision, recall, f1_score, mean_iou, mean_mae

    def _add_prediction_to_tensorboard(self, pred, index, name, epoch, fallback=None):
        """添加预测结果到TensorBoard"""
        try:
            if pred.size(1) == 2:
                # 如果是2通道CRF输出，取前景通道
                pred_img = torch.softmax(pred, dim=1)[:, 1:2]
            else:
                pred_img = pred
            
            # 选择第一张图像
            pred_single = pred_img[index:index+1]
            self.writer_val.add_images(name, pred_single, epoch)
        except Exception as e:
            print(f"添加预测到TensorBoard时出错: {e}")
            if fallback is not None:
                try:
                    self.writer_val.add_images(name + "_fallback", fallback[index:index+1], epoch)
                except:
                    pass

    def _save_model(self, epoch, is_best=False, is_early_stop=False, metrics=None):
        """保存模型"""
        # 创建检查点
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict() if self.save_opt else None,
            'metrics': metrics
        }
        
        # 如果有指标，生成带指标的文件名
        if metrics is not None:
            val_loss, accuracy, precision, recall, f1_score, mean_iou, mean_mae = metrics
            metrics_str = f"loss{val_loss:.4f}_iou{mean_iou:.4f}_mae{mean_mae:.4f}"
        else:
            metrics_str = "no_metrics"
        
        # 保存到指定路径
        if is_best:
            save_path = os.path.join(self.model_path, 'weights', f'best_model_{metrics_str}.pth')
            torch.save(checkpoint, save_path)
            print(f"最佳模型已保存: {save_path}")
        
        if is_early_stop:
            save_path = os.path.join(self.model_path, 'weights', f'early_stop_epoch_{epoch}_{metrics_str}.pth')
            torch.save(checkpoint, save_path)
            print(f"早停模型已保存: {save_path}")
        
        # 定期保存
        if not is_best and not is_early_stop:
            save_path = os.path.join(self.model_path, 'weights', f'checkpoint_epoch_{epoch}_{metrics_str}.pth')
            torch.save(checkpoint, save_path)
            print(f"检查点已保存: {save_path}")


if __name__ == '__main__':
    args = parse_arguments()
    engine = Engine(args)
    engine.train() 