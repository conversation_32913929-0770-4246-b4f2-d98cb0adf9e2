#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Proteus ViT-B训练脚本 - 仿照train_scsa.py格式
升级到ViT-Base获得更强的特征表示能力
"""

from __future__ import print_function
from __future__ import absolute_import  
from __future__ import division

import os
import argparse
import numpy as np
import torch
import torch.optim as optim
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter

# 导入模型和数据
import sys
sys.path.append('ig_glass')
from gdnet_proteus_vits import create_proteus_vits_model
from loss_proteus_vits import create_proteus_vits_loss
from glass_dataloader import GlassDataLoader

# 日志
writer_train = SummaryWriter(log_dir='runs/proteus_vitb_train')
writer_val = SummaryWriter(log_dir='runs/proteus_vitb_valid')

def parse_arguments():
    parser = argparse.ArgumentParser(description='Proteus ViT-B模型训练参数')
    
    # 基本训练参数
    parser.add_argument('--epochs', default=302, help='训练轮数', type=int)
    parser.add_argument('--bs', default=6, help='批次大小', type=int)
    parser.add_argument('--lr', default=0.002, help='学习率', type=float)
    parser.add_argument('--wd', default=0.0001, help='L2权重衰减参数', type=float)
    parser.add_argument('--img_size', default=420, help='训练图像大小(必须能被14整除)', type=int)
    parser.add_argument('--aug', default=True, help='是否使用数据增强', type=bool)
    parser.add_argument('--n_worker', default=4, help='数据加载线程数', type=int)
    
    # 训练控制
    parser.add_argument('--test_interval', default=10, help='测试间隔（多少个epoch）', type=int)
    parser.add_argument('--save_interval', default=20, help='保存间隔（多少个epoch）', type=int)
    parser.add_argument('--save_opt', default=False, help='是否同时保存优化器', type=bool)
    parser.add_argument('--log_interval', default=50, help='日志记录间隔（批次数）', type=int)
    
    # 路径和恢复
    parser.add_argument('--res_mod', default=None, help='恢复模型的路径', type=str)
    parser.add_argument('--res_opt', default=None, help='恢复优化器的路径', type=str)
    parser.add_argument('--use_gpu', default=True, help='是否使用GPU', type=bool)
    parser.add_argument('--base_save_path', default='/home/<USER>/ws/IG_SLAM/ig_glass/models_proteus_vitb', help='模型保存基础路径', type=str)
    
    # 模型参数
    parser.add_argument('--vit_type', default='vitb', help='ViT类型: vits或vitb', type=str)
    parser.add_argument('--backbone_path', default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/proteus_vitb_backbone.pth', help='骨干网络权重路径', type=str)
    parser.add_argument('--crf_iter', default=3, help='CRF迭代次数', type=int)
    parser.add_argument('--trainable_crf', default=False, help='CRF参数是否可训练', type=bool)
    
    # 数据集参数
    parser.add_argument('--data_mode', default='train_gdd', help='训练数据模式', type=str)
    parser.add_argument('--val_mode', default='test_gdd', help='验证数据模式', type=str)
    parser.add_argument('--glass_augmentation', default='strong', help='玻璃感知增强强度', type=str)
    
    # 损失函数权重
    parser.add_argument('--bce_weight', default=0.1, help='BCE损失权重', type=float)
    parser.add_argument('--iou_weight', default=0.5, help='IoU损失权重', type=float)
    parser.add_argument('--edge_weight', default=0.4, help='边缘损失权重', type=float)
    
    # 优化器参数
    parser.add_argument('--optimizer', default='AdamW', help='优化器类型', type=str)
    parser.add_argument('--scheduler', default='CosineAnnealingLR', help='学习率调度器', type=str)
    parser.add_argument('--warmup_epochs', default=5, help='预热轮数', type=int)
    
    # 早停参数
    parser.add_argument('--patience', default=10, help='早停耐心值', type=int)
    parser.add_argument('--min_delta', default=0.001, help='早停最小改进', type=float)
    
    
    # 添加Focal Loss相关参数
    parser.add_argument('--use_focal', default=True, help='是否使用Focal Loss替代BCE Loss', type=bool)
    parser.add_argument('--focal_alpha', default=0.3, help='Focal Loss的alpha参数（玻璃类别权重）', type=float)
    parser.add_argument('--focal_gamma', default=2.5, help='Focal Loss的gamma参数（聚焦程度）', type=float)
    parser.add_argument('--focal_weight', default=0.4, help='Focal Loss的权重', type=float)
    
    return parser.parse_args()


class EarlyStopping:
    def __init__(self, patience=10, min_delta=0.001):
        """早停机制"""
        self.patience = patience
        self.min_delta = min_delta
        self.best_score = None
        self.counter = 0
        self.early_stop = False

    def step(self, val_score):
        if self.best_score is None:
            self.best_score = val_score
        elif val_score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = val_score
            self.counter = 0
        
        return self.early_stop


class Engine:
    def __init__(self, args):
        self.args = args
        
        # 创建保存目录
        self.model_path = os.path.join(args.base_save_path, 'IG-SLAM-ProteusViTB')
        print('模型将保存在: {}\n'.format(self.model_path))
        
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))

        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() and args.use_gpu else 'cpu')
        print(f'使用设备: {self.device}')

        # 初始化模型
        print(f'🚀 创建Proteus ViT-{args.vit_type.upper()}模型...')
        self.model = create_proteus_vits_model(
            backbone_path=args.backbone_path,
            crf_iter=args.crf_iter,
            trainable_crf=args.trainable_crf,
            vit_type=args.vit_type
        ).to(self.device)
        
        # 打印模型信息
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f'模型参数: {total_params:,} ({total_params/1e6:.1f}M)')
        print(f'可训练参数: {trainable_params:,} ({trainable_params/1e6:.1f}M)')

        # 初始化损失函数
        print('🎯 初始化损失函数...')
        if self.args.use_focal:
            print('📈 使用Focal Loss处理类别不平衡问题')
            from ig_glass.loss_proteus_vits import create_proteus_vits_focal_loss
            self.criterion = create_proteus_vits_focal_loss(
                focal_weight=args.focal_weight,
                iou_weight=args.iou_weight,
                edge_weight=args.edge_weight,
                focal_alpha=args.focal_alpha,
                focal_gamma=args.focal_gamma
            ).to(self.device)
        else:
            print('📊 使用标准BCE Loss')
            from ig_glass.loss_proteus_vits import create_proteus_vits_loss
            self.criterion = create_proteus_vits_loss(
                bce_weight=args.bce_weight,
                iou_weight=args.iou_weight,
                edge_weight=args.edge_weight
            ).to(self.device)

        # 初始化数据加载器
        print('📊 初始化数据加载器...')
        self._init_dataloaders()

        # 初始化优化器
        print('⚙️ 初始化优化器...')
        self._init_optimizer()

        # 早停机制
        self.early_stopping = EarlyStopping(patience=args.patience, min_delta=args.min_delta)
        
        # 最佳指标
        self.best_iou = 0.0
        self.best_mae = float('inf')

    def _init_dataloaders(self):
        """初始化数据加载器"""
        # 训练数据集
        self.train_data = GlassDataLoader(
            mode=self.args.data_mode,
            augment_data=self.args.aug,
            target_size=self.args.img_size,
            glass_augmentation=self.args.glass_augmentation
        )
        
        self.train_dataloader = DataLoader(
            self.train_data,
            batch_size=self.args.bs,
            shuffle=True,
            num_workers=self.args.n_worker,
            pin_memory=True,
            drop_last=True
        )

        # 验证数据集
        self.test_data = GlassDataLoader(
            mode=self.args.val_mode,
            augment_data=False,
            target_size=self.args.img_size,
            glass_augmentation='none'
        )
        
        self.test_dataloader = DataLoader(
            self.test_data,
            batch_size=self.args.bs, 
            shuffle=False,
            num_workers=self.args.n_worker,
            pin_memory=True
        )

        print(f'训练样本: {len(self.train_data)}')
        print(f'验证样本: {len(self.test_data)}')

    def _init_optimizer(self):
        """初始化优化器和调度器 - 增强分层学习率策略"""
        # 分层学习率 - 更精细的参数分组
        vit_params = []
        fusion_params = []  # 特征融合模块
        edge_params = []    # 边缘分析模块
        other_params = []   # 其他模块
        
        for name, param in self.model.named_parameters():
            if 'vit_feature_extractor' in name:
                vit_params.append(param)
            elif 'multi_scale_fusion' in name or 'feature_adapter' in name:
                fusion_params.append(param)
            elif 'edge_analyzer' in name or 'physics' in name:
                edge_params.append(param)
            else:
                other_params.append(param)

        # 分层学习率策略配置
        self.freeze_vit_epochs = 10  # 前10轮冻结ViT
        self.gradual_unfreeze_epochs = 10  # 10轮渐进式解冻
        
        print(f"🔧 分层学习率策略:")
        print(f"  📊 ViT参数: {len(vit_params)}个")
        print(f"  🔗 融合参数: {len(fusion_params)}个") 
        print(f"  🎯 边缘参数: {len(edge_params)}个")
        print(f"  ⚙️  其他参数: {len(other_params)}个")
        print(f"  ❄️  前{self.freeze_vit_epochs}轮冻结ViT，专门训练融合模块")
        print(f"  🔥 第{self.freeze_vit_epochs+1}-{self.freeze_vit_epochs+self.gradual_unfreeze_epochs}轮渐进式解冻")

        # 创建优化器 - 精细化学习率
        if self.args.optimizer == 'AdamW':
            self.optimizer = optim.AdamW([
                {'params': vit_params, 'lr': self.args.lr * 0.05, 'weight_decay': self.args.wd, 'name': 'vit'},  # 初始很低
                {'params': fusion_params, 'lr': self.args.lr * 1.2, 'weight_decay': self.args.wd, 'name': 'fusion'},  # 融合模块稍高
                {'params': edge_params, 'lr': self.args.lr * 1.0, 'weight_decay': self.args.wd, 'name': 'edge'},  # 边缘模块标准
                {'params': other_params, 'lr': self.args.lr * 1.0, 'weight_decay': self.args.wd, 'name': 'other'}  # 其他模块标准
            ])
        elif self.args.optimizer == 'Adam':
            self.optimizer = optim.Adam([
                {'params': vit_params, 'lr': self.args.lr * 0.05, 'weight_decay': self.args.wd, 'name': 'vit'},
                {'params': fusion_params, 'lr': self.args.lr * 1.2, 'weight_decay': self.args.wd, 'name': 'fusion'},
                {'params': edge_params, 'lr': self.args.lr * 1.0, 'weight_decay': self.args.wd, 'name': 'edge'},
                {'params': other_params, 'lr': self.args.lr * 1.0, 'weight_decay': self.args.wd, 'name': 'other'}
            ])
        else:
            self.optimizer = optim.SGD([
                {'params': vit_params, 'lr': self.args.lr * 0.05, 'weight_decay': self.args.wd, 'momentum': 0.9, 'name': 'vit'},
                {'params': fusion_params, 'lr': self.args.lr * 1.2, 'weight_decay': self.args.wd, 'momentum': 0.9, 'name': 'fusion'},
                {'params': edge_params, 'lr': self.args.lr * 1.0, 'weight_decay': self.args.wd, 'momentum': 0.9, 'name': 'edge'},
                {'params': other_params, 'lr': self.args.lr * 1.0, 'weight_decay': self.args.wd, 'momentum': 0.9, 'name': 'other'}
            ])

        # 学习率调度器
        if self.args.scheduler == 'CosineAnnealingLR':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=self.args.epochs, eta_min=self.args.lr * 0.01
            )
        elif self.args.scheduler == 'StepLR':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, step_size=20, gamma=0.1
            )
        else:
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, 'min', patience=5, factor=0.5, verbose=True
            )

        # 预热调度器
        if self.args.warmup_epochs > 0:
            self.warmup_scheduler = optim.lr_scheduler.LinearLR(
                self.optimizer, start_factor=0.1, total_iters=self.args.warmup_epochs
            )
        else:
            self.warmup_scheduler = None

        print(f'优化器: {self.args.optimizer}')
        print(f'调度器: {self.args.scheduler}')
        print(f'预热轮数: {self.args.warmup_epochs}')

    def _apply_layered_lr_strategy(self, epoch):
        """应用分层学习率策略"""
        if epoch <= self.freeze_vit_epochs:
            # 阶段1: 冻结ViT，专门训练融合模块
            for param in self.model.vit_feature_extractor.parameters():
                param.requires_grad = False
            
            if epoch == 1:
                print(f"🧊 阶段1 (轮次1-{self.freeze_vit_epochs}): 冻结ViT，专门训练融合模块")
                print(f"   目标: 让融合模块学会如何处理ViT特征")
                
        elif epoch <= self.freeze_vit_epochs + self.gradual_unfreeze_epochs:
            # 阶段2: 渐进式解冻ViT
            if epoch == self.freeze_vit_epochs + 1:
                print(f"🔥 阶段2 (轮次{self.freeze_vit_epochs+1}-{self.freeze_vit_epochs+self.gradual_unfreeze_epochs}): 渐进式解冻ViT")
                print(f"   目标: 逐步释放ViT的表示能力")
            
            # 解冻ViT参数
            for param in self.model.vit_feature_extractor.parameters():
                param.requires_grad = True
            
            # 渐进式提高ViT学习率
            unfreeze_progress = (epoch - self.freeze_vit_epochs) / self.gradual_unfreeze_epochs
            vit_lr_multiplier = 0.05 + (0.1 - 0.05) * unfreeze_progress  # 从0.05逐渐提升到0.1
            
            # 更新ViT参数组的学习率
            for param_group in self.optimizer.param_groups:
                if param_group.get('name') == 'vit':
                    param_group['lr'] = self.args.lr * vit_lr_multiplier
                    
            if epoch % 2 == 1:  # 每2轮打印一次进度
                print(f"   🎯 ViT学习率倍数: {vit_lr_multiplier:.3f}")
                
        else:
            # 阶段3: 完全解冻，正常训练
            if epoch == self.freeze_vit_epochs + self.gradual_unfreeze_epochs + 1:
                print(f"🚀 阶段3 (轮次{self.freeze_vit_epochs+self.gradual_unfreeze_epochs+1}+): 完全解冻，发挥ViT全部潜力")
                print(f"   目标: 充分利用ViT的全局理解能力，冲击90%+ IoU")
            
            # 确保ViT完全解冻
            for param in self.model.vit_feature_extractor.parameters():
                param.requires_grad = True
            
            # 设置最终的ViT学习率
            for param_group in self.optimizer.param_groups:
                if param_group.get('name') == 'vit':
                    param_group['lr'] = self.args.lr * 0.1  # 最终稳定在0.1倍学习率

    def train(self):
        """训练主循环"""
        print('\n🚀 开始训练Proteus ViT-B...')
        print(f'目标: 超越ViT-S基线，冲击90%+ IoU')
        print('=' * 60)

        for epoch in range(1, self.args.epochs + 1):
            # 应用分层学习率策略
            self._apply_layered_lr_strategy(epoch)
            
            # 训练阶段
            self.model.train()
            running_loss = 0.0
            
            train_tqdm = tqdm(self.train_dataloader, desc=f"Epoch {epoch}/{self.args.epochs}")
            for batch_idx, (inp_imgs, gt_masks) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)

                self.optimizer.zero_grad()

                # 前向传播
                outputs = self.model(inp_imgs)

                # 计算损失
                loss, loss_dict = self.criterion(outputs, gt_masks, inp_imgs)

                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                self.optimizer.step()

                running_loss += loss.item()

                # 更新进度条
                if batch_idx % 10 == 0:
                    if self.args.use_focal:
                        train_tqdm.set_postfix({
                            'Loss': f'{loss.item():.4f}',
                            'IoU': f'{loss_dict.get("iou_loss", 0):.4f}',
                            'Focal': f'{loss_dict.get("focal_loss", 0):.4f}',
                            'Edge': f'{loss_dict.get("edge_loss", 0):.4f}'
                        })
                    else:
                        train_tqdm.set_postfix({
                            'Loss': f'{loss.item():.4f}',
                            'IoU': f'{loss_dict.get("iou_loss", 0):.4f}',
                            'BCE': f'{loss_dict.get("bce_loss", 0):.4f}',
                            'Edge': f'{loss_dict.get("edge_loss", 0):.4f}'
                        })

                # 记录到TensorBoard
                if batch_idx % self.args.log_interval == 0:
                    global_step = (epoch - 1) * len(self.train_dataloader) + batch_idx
                    writer_train.add_scalar('Loss/Total', loss.item(), global_step)
                    for key, value in loss_dict.items():
                        if isinstance(value, torch.Tensor):
                            writer_train.add_scalar(f'Loss/{key}', value.item(), global_step)
                    
                    # 记录学习率
                    current_lr = self.optimizer.param_groups[0]['lr']
                    writer_train.add_scalar('Learning_Rate', current_lr, global_step)

            # 学习率调度
            if epoch <= self.args.warmup_epochs and self.warmup_scheduler:
                self.warmup_scheduler.step()
            else:
                if self.args.scheduler != 'ReduceLROnPlateau':
                    self.scheduler.step()

            # 计算平均损失
            epoch_loss = running_loss / len(self.train_dataloader)
            
            # 显示当前学习率信息
            lr_info = []
            for param_group in self.optimizer.param_groups:
                group_name = param_group.get('name', 'unknown')
                lr_value = param_group['lr']
                lr_multiplier = lr_value / self.args.lr
                lr_info.append(f"{group_name}({lr_multiplier:.3f})")
            
            print(f'训练轮次: {epoch} | 损失: {epoch_loss:.6f} | 学习率: {" ".join(lr_info)}')

            # 验证阶段
            if epoch % self.args.test_interval == 0:
                val_loss, val_iou, val_mae = self.test(epoch)
                
                # 如果使用ReduceLROnPlateau，用验证损失更新
                if self.args.scheduler == 'ReduceLROnPlateau':
                    self.scheduler.step(val_loss)

                # 检查是否是最佳模型
                is_best = val_iou > self.best_iou
                if is_best:
                    self.best_iou = val_iou
                    self.best_mae = val_mae
                    print(f'🎉 新的最佳模型! IoU: {val_iou:.4f}, MAE: {val_mae:.4f}')

                # 保存模型
                self._save_model(epoch, val_loss, val_iou, val_mae, is_best)

                # 早停检查
                if self.early_stopping.step(val_iou):
                    print(f'🛑 早停触发，第 {epoch} 轮停止训练')
                    break

            # 定期保存
            if epoch % self.args.save_interval == 0:
                if epoch % self.args.test_interval != 0:
                    # 如果这个epoch没有验证，快速验证一下
                    val_loss, val_iou, val_mae = self.test(epoch=0)
                    self._save_model(epoch, val_loss, val_iou, val_mae, False)

        print(f'\n🎉 训练完成!')
        print(f'最佳IoU: {self.best_iou:.4f}')
        print(f'最佳MAE: {self.best_mae:.4f}')
        
        writer_train.close()
        writer_val.close()

    def test(self, epoch=0):
        """验证模型"""
        self.model.eval()
        running_loss = 0.0
        total_iou = 0.0
        total_mae = 0.0

        with torch.no_grad():
            test_tqdm = tqdm(self.test_dataloader, desc='Validating')
            for batch_idx, (inp_imgs, gt_masks) in enumerate(test_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)

                # 前向传播
                outputs = self.model(inp_imgs)

                # 计算损失
                loss, loss_dict = self.criterion(outputs, gt_masks, inp_imgs)
                running_loss += loss.item()

                # 计算IoU和MAE
                pred = outputs['ensemble_pred']
                iou = self._calculate_iou(pred, gt_masks)
                mae = self._calculate_mae(pred, gt_masks)

                total_iou += iou
                total_mae += mae

                # 更新进度条
                if self.args.use_focal:
                    test_tqdm.set_postfix({
                        'Loss': f'{loss.item():.4f}',
                        'IoU': f'{iou:.4f}',
                        'MAE': f'{mae:.4f}',
                        'Focal': f'{loss_dict.get("focal_loss", 0):.4f}'
                    })
                else:
                    test_tqdm.set_postfix({
                        'Loss': f'{loss.item():.4f}',
                        'IoU': f'{iou:.4f}',
                        'MAE': f'{mae:.4f}',
                        'BCE': f'{loss_dict.get("bce_loss", 0):.4f}'
                    })

        # 计算平均指标
        avg_loss = running_loss / len(self.test_dataloader)
        avg_iou = total_iou / len(self.test_dataloader)
        avg_mae = total_mae / len(self.test_dataloader)

        print(f'验证结果 | 损失: {avg_loss:.4f} | IoU: {avg_iou:.4f} | MAE: {avg_mae:.4f}')

        # 记录到TensorBoard
        if epoch > 0:
            writer_val.add_scalar('Loss', avg_loss, epoch)
            writer_val.add_scalar('IoU', avg_iou, epoch)
            writer_val.add_scalar('MAE', avg_mae, epoch)

        return avg_loss, avg_iou, avg_mae

    def _calculate_iou(self, pred, target):
        """计算IoU"""
        pred_binary = (pred > 0.5).float()
        target_binary = (target > 0.5).float()
        
        intersection = (pred_binary * target_binary).sum()
        union = pred_binary.sum() + target_binary.sum() - intersection
        
        if union == 0:
            return 1.0
        return (intersection / union).item()

    def _calculate_mae(self, pred, target):
        """计算MAE"""
        return torch.abs(pred - target).mean().item()

    def _save_model(self, epoch, val_loss, val_iou, val_mae, is_best=False):
        """保存模型"""
        state = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_iou': self.best_iou,
            'best_mae': self.best_mae,
            'val_loss': val_loss,
            'val_iou': val_iou,
            'val_mae': val_mae,
            'args': self.args
        }

        # 保存最新检查点
        torch.save(state, os.path.join(self.model_path, 'weights', 'latest.pth'))

        if is_best:
            # 保存最佳模型
            torch.save(state, os.path.join(self.model_path, 'weights', 'best.pth'))
            torch.save(state, os.path.join(self.model_path, 'weights', 
                      f'best_iou-{val_iou:.4f}_mae-{val_mae:.4f}_epoch-{epoch}.pth'))

        # 定期保存
        if epoch % self.args.save_interval == 0:
            torch.save(state, os.path.join(self.model_path, 'weights', f'epoch_{epoch}.pth'))

        print(f'模型已保存: epoch={epoch}, IoU={val_iou:.4f}, MAE={val_mae:.4f}')


if __name__ == '__main__':
    args = parse_arguments()
    
    print('🌟 Proteus ViT-B训练系统')
    print('=' * 60)
    print(f'ViT类型: {args.vit_type.upper()}')
    print(f'批次大小: {args.bs}')
    print(f'学习率: {args.lr}')
    print(f'图像尺寸: {args.img_size}')
    print(f'训练轮数: {args.epochs}')
    if args.use_focal:
        print(f'🎯 损失函数: Focal Loss (α={args.focal_alpha}, γ={args.focal_gamma})')
        print(f'   - 权重配置: Focal={args.focal_weight}, IoU={args.iou_weight}, Edge={args.edge_weight}')
        print('   - 专门处理类别不平衡问题 (背景:玻璃=2:1)')
    else:
        print(f'📊 损失函数: BCE Loss')
        print(f'   - 权重配置: BCE={args.bce_weight}, IoU={args.iou_weight}, Edge={args.edge_weight}')
    print('')
    print('🧠 分层学习率策略概览:')
    print('   阶段1 (轮次1-10):   🧊 冻结ViT，专训融合模块')
    print('   阶段2 (轮次11-20):  🔥 渐进解冻ViT (0.05→0.1倍学习率)')
    print('   阶段3 (轮次21+):    🚀 完全解冻，发挥全部潜力')
    print('   �� 预期效果: 加速收敛，避免ViT特征污染融合学习')
    print('=' * 60)
    
    # 开始训练
    engine = Engine(args)
    engine.train() 