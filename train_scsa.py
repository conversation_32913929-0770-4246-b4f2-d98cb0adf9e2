from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

import os
import argparse
import numpy as np
import torch
import torch.optim as optim
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from tqdm import tqdm
from ig_glass.dataloader import SODLoader, FODLoader
from ig_glass.gdnet_scsa import GDNetSCSA
from ig_glass.loss_scsa import SCSAComposite<PERSON>oss
from torch.utils.tensorboard import SummaryWriter

backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth'
# 初始化记录器
writer_train = SummaryWriter(log_dir='runs/scsa_train')
writer_vad = SummaryWriter(log_dir='runs/scsa_valid')

def parse_arguments():
    parser = argparse.ArgumentParser(description='SCSA模型训练参数')
    parser.add_argument('--epochs', default=302, help='训练轮数', type=int)
    parser.add_argument('--bs', default=6, help='批次大小', type=int)
    parser.add_argument('--lr', default=0.0005, help='学习率', type=float)
    parser.add_argument('--wd', default=0.0005, help='L2权重衰减参数', type=float)
    parser.add_argument('--img_size', default=416, help='训练图像大小', type=int)
    parser.add_argument('--aug', default=True, help='是否使用数据增强', type=bool)
    parser.add_argument('--n_worker', default=4, help='数据加载线程数', type=int)
    parser.add_argument('--test_interval', default=10, help='测试间隔（多少个epoch）', type=int)
    parser.add_argument('--save_interval', default=20, help='保存间隔（多少个epoch）', type=int)
    parser.add_argument('--save_opt', default=False, help='是否同时保存优化器', type=bool)
    parser.add_argument('--log_interval', default=250, help='日志记录间隔（批次数）', type=int)
    parser.add_argument('--res_mod', default = './ckpt', help='恢复模型的路径', type=str)
    parser.add_argument('--res_opt', default=None, help='恢复优化器的路径', type=str)
    parser.add_argument('--use_gpu', default=True, help='是否使用GPU', type=bool)
    parser.add_argument('--base_save_path', default='/home/<USER>/ws/IG_SLAM/ig_glass/models_scsa', help='模型保存基础路径', type=str)
    parser.add_argument('--crf_iter', default=3, help='CRF迭代次数', type=int)
    parser.add_argument('--trainable_crf', default=True, help='CRF参数是否可训练', type=bool)
    parser.add_argument('--crf_weight', default=1.5, help='CRF损失权重', type=float)
    # CRF参数（基于网格搜索）
    parser.add_argument('--bilateral_weight', default=10.0, help='双边滤波权重（compat=10）', type=float)
    parser.add_argument('--gaussian_weight', default=5.0, help='高斯滤波权重（compat=5）', type=float)
    parser.add_argument('--bilateral_spatial_sigma', default=40.0, help='双边滤波的空间sigma（sxy=40）', type=float)
    parser.add_argument('--bilateral_color_sigma', default=3.0, help='双边滤波的颜色sigma（srgb=3）', type=float)
    parser.add_argument('--gaussian_sigma', default=1.5, help='高斯滤波的sigma（sxy=1.5）', type=float)
    # 为SCSA优化的损失权重值
    parser.add_argument('--bce_weight', default=0.6, help='BCE/Focal损失组件权重', type=float)
    parser.add_argument('--iou_weight', default=0.4, help='IoU损失组件权重', type=float)
    # Focal Loss参数
    parser.add_argument('--focal_alpha', default=0.25, help='Focal Loss的alpha参数', type=float)
    parser.add_argument('--focal_gamma', default=2.0, help='Focal Loss的gamma参数', type=float)
    parser.add_argument('--use_focal', default=True, help='是否使用Focal Loss替代BCE Loss', type=bool)
    parser.add_argument('--use_kfold', default=False, help='是否使用k折交叉验证', type=bool)

    return parser.parse_args()

class EarlyStopping:
    def __init__(self, patience=7, min_delta=0.001):
        """
        patience: 无改进后终止训练的轮数
        min_delta: 被视为改进的最小变化量
        """
        self.patience = patience
        self.min_delta = min_delta
        self.best_loss = float('inf')
        self.counter = 0

    def step(self, val_loss):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0  # 重置计数器
        else:
            self.counter += 1  # 无改进，增加计数

        return self.counter >= self.patience  # 如果超过耐心值，返回True

class Engine:
    def __init__(self, args):
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.wd = args.wd
        self.img_size = args.img_size
        self.aug = args.aug
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.save_opt = args.save_opt
        self.log_interval = args.log_interval
        self.res_mod_path = args.res_mod
        self.res_opt_path = args.res_opt
        self.use_gpu = args.use_gpu
        self.crf_iter = args.crf_iter
        self.trainable_crf = args.trainable_crf
        self.crf_weight = args.crf_weight
        self.bce_weight = args.bce_weight
        self.iou_weight = args.iou_weight
        self.use_kfold = args.use_kfold
        # Focal Loss参数
        self.focal_alpha = args.focal_alpha
        self.focal_gamma = args.focal_gamma
        self.use_focal = args.use_focal

        # CRF参数
        self.bilateral_weight = args.bilateral_weight
        self.gaussian_weight = args.gaussian_weight
        self.bilateral_spatial_sigma = args.bilateral_spatial_sigma
        self.bilateral_color_sigma = args.bilateral_color_sigma
        self.gaussian_sigma = args.gaussian_sigma

        self.model_path = args.base_save_path + '/IG-SLAM-SCSA'
        print('模型将保存在: {}\n'.format(self.model_path))
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))

        if torch.cuda.is_available():
            self.device = torch.device(device='cuda')
        else:
            self.device = torch.device(device='cpu')

        # 初始化SCSA模型
        self.model = GDNetSCSA(
            backbone_path=backbone_path,
            crf_iter=self.crf_iter,
            trainable_crf=self.trainable_crf
        )

        # 更新CRF参数
        if hasattr(self.model, 'crf') and hasattr(self.model.crf, 'bilateral_weight'):
            self.model.crf.bilateral_weight = nn.Parameter(torch.tensor(self.bilateral_weight)) if self.trainable_crf else self.bilateral_weight
            self.model.crf.gaussian_weight = nn.Parameter(torch.tensor(self.gaussian_weight)) if self.trainable_crf else self.gaussian_weight
            self.model.crf.bilateral_spatial_sigma = nn.Parameter(torch.tensor(self.bilateral_spatial_sigma)) if self.trainable_crf else self.bilateral_spatial_sigma
            self.model.crf.bilateral_color_sigma = nn.Parameter(torch.tensor(self.bilateral_color_sigma)) if self.trainable_crf else self.bilateral_color_sigma
            self.model.crf.gaussian_sigma = nn.Parameter(torch.tensor(self.gaussian_sigma)) if self.trainable_crf else self.gaussian_sigma

        self.model.to(self.device)

        # 初始化损失函数
        self.criterion = SCSACompositeLoss(
            bce_weight=self.bce_weight,
            iou_weight=self.iou_weight,
            edge_weight=0.2  # 添加边界损失权重
        )

        # 初始化优化器，对不同参数应用不同的权重衰减
        params = self._split_params_for_weight_decay(self.model)
        self.optimizer = optim.SGD(params, lr=self.lr, momentum=0.9)

        # 如果是恢复训练，加载模型和优化器
        ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        exp_name = 'IG-SLAM'
        args = {
            'snapshot': 'scsa-30-150',
            'scale': 416,
            'crf': False
        }

        if self.res_mod_path is not None:
            chkpt = os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')
            print(f"从以下路径加载检查点: {chkpt}")

            # 加载基础GDNet权重
            if args['snapshot'] != '200':
                base_weights = torch.load(chkpt)['model']
            else:
                base_weights = torch.load(os.path.join(chkpt))

            # 初始化模型权重
            model_state_dict = self.model.state_dict()

            # 创建一个匹配键的新状态字典
            new_state_dict = {}
            for k, v in base_weights.items():
                if k in model_state_dict:
                    if model_state_dict[k].shape == v.shape:
                        new_state_dict[k] = v
                    else:
                        print(f"由于形状不匹配而跳过参数 {k}: {v.shape} vs {model_state_dict[k].shape}")

            # 更新模型状态字典
            model_state_dict.update(new_state_dict)
            self.model.load_state_dict(model_state_dict, strict=False)
            print(f"成功从检查点加载了 {len(new_state_dict)} 个参数。")

        # 设置早停机制
        self.early_stopping = EarlyStopping(patience=7)

    def _split_params_for_weight_decay(self, model):
        """
        将参数分为两组:
        - 带权重衰减: 卷积和线性层的权重
        - 不带权重衰减: 所有偏置和批归一化参数
        """
        decay = []
        no_decay = []

        for name, param in model.named_parameters():
            if not param.requires_grad:
                continue
            if 'weight' in name and 'bn' not in name and 'batch_norm' not in name:
                decay.append(param)
            else:
                no_decay.append(param)

        return [
            {'params': decay, 'weight_decay': self.wd},
            {'params': no_decay, 'weight_decay': 0.0}
        ]

    def train(self):
        """训练GDNetSCSA模型"""
        print('开始训练...\n')

        # 设置k-fold范围
        k_range = range(5) if self.use_kfold else range(1)

        for k in k_range:
            if self.use_kfold:
                print(f'K-折交叉验证训练: 第 {k+1} 折')
                self.train_data = FODLoader(mode='train', augment_data=self.aug, target_size=self.img_size, k=k)
                self.test_data = FODLoader(mode='test', augment_data=False, target_size=self.img_size, k=k)
            else:
                self.train_data = SODLoader(mode='train_gdd', augment_data=self.aug, target_size=self.img_size)
                self.test_data = SODLoader(mode='test_gdd', augment_data=False, target_size=self.img_size)

            self.train_dataloader = DataLoader(self.train_data, batch_size=self.bs, shuffle=True, num_workers=self.n_worker)
            self.test_dataloader = DataLoader(self.test_data, batch_size=self.bs, shuffle=False, num_workers=self.n_worker)

            print(f"训练样本: {len(self.train_data)}, 验证样本: {len(self.test_data)}")

            # 设置学习率调度器
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, 'min', patience=3, factor=0.5, verbose=True)

            best_loss = float('inf')

            # 初始化早停机制
            early_stopping = EarlyStopping(patience=7, min_delta=0.001)

            for epoch in range(1, self.epochs + 1):
                # 训练阶段
                self.model.train()
                running_loss = 0.0

                train_tqdm = tqdm(self.train_dataloader, desc=f"轮次 {epoch}/{self.epochs}")
                for batch_idx, (inp_imgs, gt_masks) in enumerate(train_tqdm):
                    inp_imgs = inp_imgs.to(self.device)
                    gt_masks = gt_masks.to(self.device)

                    self.optimizer.zero_grad()

                    # 前向传播
                    h_pred, l_pred, final_pred, refined_pred = self.model(inp_imgs)

                    # 计算损失
                    loss = self.criterion((h_pred, l_pred, final_pred, refined_pred), gt_masks)

                    # 反向传播和优化
                    loss.backward()
                    self.optimizer.step()

                    running_loss += loss.item()

                    # 更新进度条
                    train_tqdm.set_postfix(loss=loss.item(), avg_loss=running_loss/(batch_idx+1))

                    # 记录到TensorBoard
                    if batch_idx % self.log_interval == 0:
                        global_step = (epoch - 1) * len(self.train_dataloader) + batch_idx
                        writer_train.add_scalar('损失', loss.item(), global_step)

                        # 记录学习率
                        current_lr = self.optimizer.param_groups[0]['lr']
                        writer_train.add_scalar('学习率', current_lr, global_step)

                epoch_loss = running_loss / len(self.train_dataloader)
                print(f"训练轮次: {epoch} 损失: {epoch_loss:.6f}")

                # 验证阶段
                if epoch % self.test_interval == 0:
                    metrics = self.test(self.test_dataloader, epoch)
                    val_loss = metrics[0]  # 第一个返回值是验证损失

                    # 根据验证损失更新调度器
                    scheduler.step(val_loss)

                    # 检查早停
                    if early_stopping.step(val_loss):
                        print(f"经过 {epoch} 轮次触发早停")
                        self._save_model(epoch, is_early_stop=True, metrics=metrics)
                        break

                    # 如果是最佳模型则保存
                    if val_loss < best_loss:
                        best_loss = val_loss
                        print(f"新的最佳模型，验证损失: {best_loss:.6f}")
                        self._save_model(epoch, is_best=True, metrics=metrics)

                # 定期保存
                if self.save_interval is not None and epoch % self.save_interval == 0:
                    # 如果这个epoch没有运行验证，则运行一次验证并保存指标
                    if epoch % self.test_interval != 0:
                        metrics = self.test(self.test_dataloader, epoch=0)  # 使用epoch=0避免额外的TensorBoard记录
                        self._save_model(epoch, metrics=metrics)
                    else:
                        # 如果这个epoch已经运行了验证，则使用之前的指标
                        self._save_model(epoch, metrics=metrics)

            print(f"第 {k+1} 折训练完成!")

        print("所有训练完成!")
        writer_train.close()
        writer_vad.close()

    def test(self, dataloader=None, epoch=0):
        """在验证数据上测试模型"""
        self.model.eval()

        if dataloader is None:
            # 如果没有提供dataloader，则创建一个用于验证
            validation_dataset = SODLoader(mode='test_gdd', augment_data=False, target_size=self.img_size)
            dataloader = DataLoader(validation_dataset, batch_size=self.bs, shuffle=False,
                                num_workers=self.n_worker)

        running_loss = 0.0
        tp_fp = 0   # TruePositive + TrueNegative，用于准确率
        tp = 0      # TruePositive
        pred_true = 0   # 预测为'1'的数量，用于精确率
        gt_true = 0     # gt掩码中'1'的数量，用于召回率
        mae_list = []   # 保存每张图像的平均绝对误差的列表
        iou_list = []   # 保存每张图像的IoU的列表
        total_pixels = 0  # 所有测试图像的像素总数
        acc_list = []    # 保存每张图像的准确率的列表

        with torch.no_grad():
            test_tqdm = tqdm(dataloader, desc="验证")
            for batch_idx, (inp_imgs, gt_masks) in enumerate(test_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)

                # 前向传播
                h_pred, l_pred, final_pred, refined_pred = self.model(inp_imgs)

                # 计算损失
                loss = self.criterion((h_pred, l_pred, final_pred, refined_pred), gt_masks)

                running_loss += loss.item()

                # 更新进度条
                test_tqdm.set_postfix(loss=loss.item(), avg_loss=running_loss/(batch_idx+1))

                # 使用refined_pred进行评估 - 简化处理以提高性能
                # 如果是2通道，取第二个通道（前景）
                pred_masks = refined_pred[:, 1:2] if refined_pred.size(1) == 2 else refined_pred

                # 转换为numpy进行评估
                pred_np = pred_masks.detach().cpu().numpy()
                gt_np = gt_masks.detach().cpu().numpy()

                # 计算每个批次中每张图像的指标
                for b in range(pred_np.shape[0]):
                    # 获取单张图像的预测和真实值
                    pred_single = pred_np[b, 0]  # 现在我们确保pred_masks是[B, 1, H, W]格式
                    gt_single = gt_np[b, 0]      # Shape: H x W

                    # 二值化预测 (阈值为0.5)
                    pred_binary = (pred_single > 0.5).astype(np.float32)

                    # 计算TP, TN, FP, FN
                    TP = np.sum(np.logical_and(pred_binary, gt_single))
                    TN = np.sum(np.logical_and(np.logical_not(pred_binary), np.logical_not(gt_single)))

                    # 计算准确率
                    N_p = np.sum(gt_single)
                    N_n = np.sum(np.logical_not(gt_single))
                    acc = (TP + TN) / (N_p + N_n)

                    # 计算IoU
                    intersection = TP
                    union = np.sum(pred_binary) + np.sum(gt_single) - intersection
                    iou = (intersection / union).mean()
                    iou_loss = (1.0 - iou) * self.iou_weight

                    # 累积指标
                    tp_fp += TP + TN
                    tp += TP
                    pred_true += np.sum(pred_binary)
                    gt_true += N_p
                    iou_list.append(iou)
                    acc_list.append(acc)

                    # 添加到总像素计数
                    total_pixels += (N_p + N_n)

                # 记录绝对误差 - 简化处理以提高性能
                # 确保gt_masks是[B, 1, H, W]格式
                if gt_masks.dim() == 3:  # 如果gt_masks是[B, H, W]
                    gt_masks = gt_masks.unsqueeze(1)  # 变成[B, 1, H, W]
                ae = torch.mean(torch.abs(pred_masks - gt_masks), dim=(1, 2, 3)).cpu().numpy()
                mae_list.extend(ae)

                # 每10个epoch记录一次第一批预测结果到TensorBoard
                if batch_idx == 0 and epoch % 10 == 0:
                    # 记录图像、真实值和预测结果
                    for i in range(min(4, inp_imgs.size(0))):
                        writer_vad.add_image(f'图像/{i}', inp_imgs[i], epoch)
                        try:
                            gt_img = gt_masks[i]
                            if gt_img.dim() == 3 and gt_img.size(0) == 1:
                                gt_img = gt_img.squeeze(0)
                            writer_vad.add_image(f'真实标签/{i}', gt_img, epoch, dataformats='HW')
                        except Exception as e:
                            print(f"添加真实标签图像到TensorBoard时出错: {e}")

                        # 添加各种预测结果到TensorBoard
                        self._add_prediction_to_tensorboard(h_pred, i, 'h_预测', epoch)
                        self._add_prediction_to_tensorboard(l_pred, i, 'l_预测', epoch)
                        self._add_prediction_to_tensorboard(final_pred, i, '最终预测', epoch)
                        self._add_prediction_to_tensorboard(refined_pred, i, '优化预测', epoch, final_pred)

        # 计算最终指标 - 简化处理以提高性能
        val_loss = running_loss / len(dataloader)

        # 计算指标 - 只计算需要的指标
        accuracy = tp_fp / total_pixels if total_pixels > 0 else 0
        mae = np.mean(mae_list)
        iou = np.mean(iou_list)

        # 打印指标
        print(f'测试 :: 准确率: {accuracy:.4f}\tMAE: {mae:.4f}\tIOU: {iou:.4f}\t损失: {val_loss:.4f}')

        # 只记录关键指标到TensorBoard
        if epoch > 0:  # 只在非零epoch时记录，避免重复记录
            writer_vad.add_scalar('损失', val_loss, epoch)
            writer_vad.add_scalar('准确率', accuracy, epoch)
            writer_vad.add_scalar('IoU', iou, epoch)

        # 返回多个指标，便于保存模型时使用
        return val_loss, mae, iou

    def _add_prediction_to_tensorboard(self, pred, index, name, epoch, fallback=None):
        """帮助方法，将预测图像添加到TensorBoard - 简化版本"""
        try:
            # 简化处理逻辑
            if pred.dim() == 4:
                # 如果是2通道的CRF输出，取第二个通道（前景）
                if pred.size(1) == 2 and name == '优化预测':
                    pred_img = pred[index, 1]
                # 如果是单通道，直接取
                elif pred.size(1) == 1:
                    pred_img = pred[index, 0]
                # 其他情况，选择适当的通道
                else:
                    pred_img = pred[index, 0]
            else:
                # 如果形状不符合预期，使用fallback
                if fallback is not None:
                    pred_img = fallback[index, 0] if fallback.dim() == 4 else fallback[index]
                else:
                    return

            # 确保张量是2D的
            if pred_img.dim() > 2:
                pred_img = pred_img.squeeze()

            # 标准化到[0, 1]范围
            pred_img = torch.clamp(pred_img, 0, 1)

            # 添加到TensorBoard
            writer_vad.add_image(f'{name}/{index}', pred_img, epoch, dataformats='HW')
        except Exception as e:
            # 简化错误处理
            print(f"添加 {name} 图像到TensorBoard时出错: {e}")

    def _save_model(self, epoch, is_best=False, is_early_stop=False, metrics=None):
        """保存模型检查点，包含详细信息"""
        # 如果没有提供指标，则计算当前的指标
        if metrics is None:
            val_loss, mae, iou = self.test(self.test_dataloader, epoch=0)  # 使用epoch=0避免额外的TensorBoard记录
        else:
            val_loss, mae, iou = metrics

        # 准备模型状态
        state = {
            'model': self.model.state_dict(),
            'epoch': epoch,
            'loss': val_loss,
            'mae': mae,
            'iou': iou
        }

        # 保存模型
        if is_early_stop:
            # 早停模型使用特殊命名
            torch.save(state, os.path.join(self.model_path, 'weights', f'early_stop_epoch-{epoch:03d}_mae-{mae:.4f}_loss-{val_loss:.4f}.pth'))
            print(f"第 {epoch} 轮早停模型已保存")
        elif is_best:
            # 最佳模型使用特殊命名
            torch.save(state, os.path.join(self.model_path, 'weights', f'best_model_epoch-{epoch:03d}_mae-{mae:.4f}_loss-{val_loss:.4f}.pth'))
        else:
            # 常规保存使用详细命名
            torch.save(state, os.path.join(self.model_path, 'weights', f'model_epoch-{epoch:03d}_mae-{mae:.4f}_loss-{val_loss:.4f}.pth'))
        # 保存优化器状态（如果需要）
        if self.save_opt:
            opt_state = self.optimizer.state_dict()
            if is_best:
                torch.save(opt_state, os.path.join(self.model_path, 'optimizers', f'best_opt_epoch-{epoch:03d}.pth'))
            else:
                torch.save(opt_state, os.path.join(self.model_path, 'optimizers', f'opt_epoch-{epoch:03d}.pth'))

        print(f"第 {epoch} 轮模型检查点已保存，MAE: {mae:.4f}, 损失: {val_loss:.4f}")


if __name__ == '__main__':
    args = parse_arguments()
    engine = Engine(args)
    engine.train() 