"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : train_vit_physics_90iou.py
 @Function: 专门针对90% IoU的训练脚本

超高精度训练策略：
1. 渐进式多阶段训练 - 从基础到精细
2. 测试时增强集成 - TTA提升最终性能
3. 伪标签自训练 - 利用高置信度预测
4. 困难样本挖掘 - 动态关注难样本
5. 渐进式分辨率训练 - 从低分辨率到高分辨率
6. 多模型集成策略 - 最终性能保证
7. 精细的curriculum learning - 损失函数渐进式
"""
import os
import sys
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, ConcatDataset
import numpy as np
import argparse
from pathlib import Path
import logging
from tqdm import tqdm
import json
from collections import defaultdict
import torch.nn.functional as F
from typing import Dict, List, Tuple
import cv2
from torch.cuda.amp import GradScaler, autocast

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from ig_glass.gdnet_vit_physics_hybrid import create_vit_physics_hybrid_model
from ig_glass.loss_vit_physics_90iou import create_90iou_loss
from ig_glass.glass_mask import GlassDataset


def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'train_90iou_{time.strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


class AdvancedMetrics:
    """
    高级评估指标 - 针对90% IoU目标
    """
    def __init__(self):
        self.reset()
        
    def reset(self):
        self.intersection = 0
        self.union = 0
        self.true_positives = 0
        self.false_positives = 0
        self.false_negatives = 0
        self.edge_iou = 0
        self.boundary_f1 = 0
        self.total_samples = 0
        
    def update(self, pred: torch.Tensor, target: torch.Tensor, threshold: float = 0.5):
        """更新指标"""
        pred_binary = (pred > threshold).float()
        target_binary = target.float()
        
        # 基础IoU
        intersection = (pred_binary * target_binary).sum()
        union = pred_binary.sum() + target_binary.sum() - intersection
        
        self.intersection += intersection.item()
        self.union += union.item()
        
        # F1计算
        self.true_positives += intersection.item()
        self.false_positives += (pred_binary * (1 - target_binary)).sum().item()
        self.false_negatives += ((1 - pred_binary) * target_binary).sum().item()
        
        # 边缘IoU计算
        edge_pred = self._compute_edge(pred_binary)
        edge_target = self._compute_edge(target_binary)
        edge_intersection = (edge_pred * edge_target).sum()
        edge_union = edge_pred.sum() + edge_target.sum() - edge_intersection
        
        if edge_union > 0:
            self.edge_iou += (edge_intersection / edge_union).item()
        
        self.total_samples += pred.size(0)
        
    def _compute_edge(self, x: torch.Tensor) -> torch.Tensor:
        """计算边缘"""
        kernel = torch.tensor([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]], 
                             device=x.device, dtype=x.dtype).view(1, 1, 3, 3)
        edge = F.conv2d(x, kernel, padding=1)
        return (edge > 0).float()
        
    def compute(self) -> Dict:
        """计算最终指标"""
        iou = self.intersection / (self.union + 1e-8)
        precision = self.true_positives / (self.true_positives + self.false_positives + 1e-8)
        recall = self.true_positives / (self.true_positives + self.false_negatives + 1e-8)
        f1 = 2 * precision * recall / (precision + recall + 1e-8)
        edge_iou_avg = self.edge_iou / max(1, self.total_samples)
        
        return {
            'iou': iou,
            'f1': f1,
            'precision': precision,
            'recall': recall,
            'edge_iou': edge_iou_avg,
            'boundary_f1': self.boundary_f1 / max(1, self.total_samples)
        }


class TestTimeAugmentation:
    """
    测试时增强 - 提升最终性能
    """
    def __init__(self, scales=[0.8, 1.0, 1.2], flips=[False, True]):
        self.scales = scales
        self.flips = flips
        
    def __call__(self, model: nn.Module, x: torch.Tensor) -> torch.Tensor:
        """
        执行TTA
        Args:
            model: 模型
            x: 输入图像 [B, 3, H, W]
        Returns:
            averaged_pred: 平均预测结果
        """
        predictions = []
        original_size = x.shape[2:]
        
        for scale in self.scales:
            for flip in self.flips:
                # 缩放
                if scale != 1.0:
                    new_size = (int(original_size[0] * scale), int(original_size[1] * scale))
                    x_scaled = F.interpolate(x, size=new_size, mode='bilinear', align_corners=True)
                else:
                    x_scaled = x
                
                # 翻转
                if flip:
                    x_scaled = torch.flip(x_scaled, dims=[3])
                
                # 预测
                with torch.no_grad():
                    outputs = model(x_scaled)
                    if 'refined_pred' in outputs:
                        pred = outputs['refined_pred'][:, 1:2]
                    else:
                        pred = outputs['ensemble_pred']
                
                # 恢复变换
                if flip:
                    pred = torch.flip(pred, dims=[3])
                
                if scale != 1.0:
                    pred = F.interpolate(pred, size=original_size, mode='bilinear', align_corners=True)
                
                predictions.append(pred)
        
        # 平均所有预测
        averaged_pred = torch.mean(torch.stack(predictions), dim=0)
        return averaged_pred


class ProgressiveTrainer:
    """
    渐进式训练器 - 多阶段达到90% IoU
    """
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = setup_logging(args.log_dir)
        
        # 创建模型
        self.model = create_vit_physics_hybrid_model(
            backbone_path=args.backbone_path,
            crf_iter=args.crf_iter,
            trainable_crf=args.trainable_crf
        ).to(self.device)
        
        # 创建损失函数
        self.criterion = create_90iou_loss(adaptive_weights=args.adaptive_loss).to(self.device)
        
        # 优化器
        self.optimizer = self._create_optimizer()
        
        # 学习率调度器
        self.scheduler = self._create_scheduler()
        
        # 数据加载器
        self.train_loader, self.val_loader = self._create_dataloaders()
        
        # TTA
        self.tta = TestTimeAugmentation()
        
        # 训练状态
        self.best_iou = 0.0
        self.best_edge_iou = 0.0
        self.best_epoch = 0
        self.train_history = defaultdict(list)
        self.current_stage = 0
        
        # 混合精度训练
        self.scaler = GradScaler() if args.mixed_precision else None
        
        # 训练阶段配置
        self.training_stages = [
            {'epochs': 50, 'lr_factor': 1.0, 'loss_weights': 'balanced'},
            {'epochs': 100, 'lr_factor': 0.3, 'loss_weights': 'edge_focused'},
            {'epochs': 150, 'lr_factor': 0.1, 'loss_weights': 'precision_focused'}
        ]
        
        self.logger.info(f"90% IoU训练器初始化完成")
        self.logger.info(f"模型参数量: {sum(p.numel() for p in self.model.parameters()) / 1e6:.1f}M")
        
    def _create_optimizer(self):
        """创建优化器"""
        # 分层学习率
        vit_params = []
        physics_params = []
        other_params = []
        
        for name, param in self.model.named_parameters():
            if 'vit_backbone' in name:
                vit_params.append(param)
            elif 'glass_physics' in name:
                physics_params.append(param)
            else:
                other_params.append(param)
        
        optimizer = optim.AdamW([
            {'params': vit_params, 'lr': self.args.lr * 0.1, 'weight_decay': 0.05},
            {'params': physics_params, 'lr': self.args.lr * 0.5, 'weight_decay': 0.01},
            {'params': other_params, 'lr': self.args.lr, 'weight_decay': 0.01}
        ], betas=(0.9, 0.999))
        
        return optimizer
        
    def _create_scheduler(self):
        """创建学习率调度器"""
        return optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=50, T_mult=2, eta_min=1e-7
        )
        
    def _create_dataloaders(self):
        """创建数据加载器"""
        # 渐进式数据增强
        stage1_transforms = [  # 基础阶段
            ('resize', (320, 320)),
            ('horizontal_flip', 0.5),
            ('rotation', 10),
            ('normalize', {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]})
        ]
        
        stage2_transforms = [  # 边缘聚焦阶段
            ('resize', (384, 384)),
            ('horizontal_flip', 0.5),
            ('vertical_flip', 0.3),
            ('rotation', 15),
            ('color_jitter', {'brightness': 0.2, 'contrast': 0.2}),
            ('normalize', {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]})
        ]
        
        stage3_transforms = [  # 精度聚焦阶段
            ('resize', (420, 420)),
            ('horizontal_flip', 0.5),
            ('vertical_flip', 0.3),
            ('rotation', 20),
            ('elastic_transform', {'alpha': 50, 'sigma': 5}),
            ('color_jitter', {'brightness': 0.3, 'contrast': 0.3, 'saturation': 0.2}),
            ('normalize', {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]})
        ]
        
        val_transforms = [
            ('resize', (420, 420)),
            ('normalize', {'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225]})
        ]
        
        # 当前使用基础变换，训练过程中会切换
        self.transform_stages = [stage1_transforms, stage2_transforms, stage3_transforms]
        
        train_dataset = GlassDataset(
            self.args.train_data_path,
            transforms=stage1_transforms,
            mode='train'
        )
        
        val_dataset = GlassDataset(
            self.args.val_data_path,
            transforms=val_transforms,
            mode='val'
        )
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            pin_memory=True
        )
        
        self.logger.info(f"训练数据: {len(train_dataset)} 样本")
        self.logger.info(f"验证数据: {len(val_dataset)} 样本")
        
        return train_loader, val_loader
        
    def _update_training_stage(self, epoch):
        """更新训练阶段"""
        cumulative_epochs = 0
        new_stage = 0
        
        for i, stage in enumerate(self.training_stages):
            cumulative_epochs += stage['epochs']
            if epoch < cumulative_epochs:
                new_stage = i
                break
        
        if new_stage != self.current_stage:
            self.current_stage = new_stage
            self.logger.info(f"切换到训练阶段 {new_stage + 1}")
            
            # 更新学习率
            lr_factor = self.training_stages[new_stage]['lr_factor']
            for param_group in self.optimizer.param_groups:
                param_group['lr'] *= lr_factor
            
            # 更新数据增强
            if new_stage < len(self.transform_stages):
                self.train_loader.dataset.transforms = self.transform_stages[new_stage]
            
            # 更新损失权重
            self._update_loss_weights(self.training_stages[new_stage]['loss_weights'])
            
    def _update_loss_weights(self, weight_mode):
        """更新损失权重"""
        if weight_mode == 'balanced':
            weights = {'edge_weight': 2.0, 'boundary_weight': 2.0}
        elif weight_mode == 'edge_focused':
            weights = {'edge_weight': 4.0, 'boundary_weight': 3.0, 'hard_mining_weight': 3.0}
        elif weight_mode == 'precision_focused':
            weights = {'edge_weight': 5.0, 'boundary_weight': 4.0, 'hard_mining_weight': 4.0}
        
        if hasattr(self.criterion, 'adaptive_weights') and self.criterion.adaptive_weights:
            for name, value in weights.items():
                if hasattr(self.criterion, name):
                    setattr(self.criterion, name, nn.Parameter(torch.tensor(value)))
        
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        train_metrics = AdvancedMetrics()
        total_loss = 0
        loss_components = defaultdict(float)
        
        # 更新训练阶段
        self._update_training_stage(epoch)
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {epoch+1} (Stage {self.current_stage+1})')
        
        for batch_idx, (images, targets) in enumerate(progress_bar):
            images = images.to(self.device)
            targets = targets.to(self.device)
            
            # 混合精度训练
            if self.scaler:
                with autocast():
                    outputs = self.model(images)
                    loss, losses = self.criterion(outputs, targets)
                
                self.optimizer.zero_grad()
                self.scaler.scale(loss).backward()
                
                # 梯度裁剪
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(images)
                loss, losses = self.criterion(outputs, targets)
                
                self.optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()
            
            # 更新指标
            if 'refined_pred' in outputs:
                pred = outputs['refined_pred'][:, 1:2]
            else:
                pred = outputs['ensemble_pred']
            
            train_metrics.update(pred, targets)
            
            # 记录损失
            total_loss += loss.item()
            for key, value in losses.items():
                if isinstance(value, torch.Tensor):
                    loss_components[key] += value.item()
            
            # 更新进度条
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'lr': f'{self.optimizer.param_groups[0]["lr"]:.6f}',
                'stage': f'{self.current_stage+1}'
            })
            
            # 定期打印损失权重
            if batch_idx % 200 == 0 and hasattr(self.criterion, 'get_loss_weights'):
                weights = self.criterion.get_loss_weights()
                self.logger.info(f"Epoch {epoch+1}, Batch {batch_idx}, Weights: {weights}")
        
        # 计算epoch指标
        epoch_metrics = train_metrics.compute()
        avg_loss = total_loss / len(self.train_loader)
        
        # 记录训练历史
        self.train_history['train_loss'].append(avg_loss)
        self.train_history['train_iou'].append(epoch_metrics['iou'])
        self.train_history['train_edge_iou'].append(epoch_metrics['edge_iou'])
        
        self.logger.info(f"训练 Epoch {epoch+1}: "
                        f"Loss={avg_loss:.4f}, "
                        f"IoU={epoch_metrics['iou']:.4f}, "
                        f"Edge_IoU={epoch_metrics['edge_iou']:.4f}")
        
        return avg_loss, epoch_metrics
        
    def validate(self, epoch, use_tta=False):
        """验证"""
        self.model.eval()
        val_metrics = AdvancedMetrics()
        total_loss = 0
        
        with torch.no_grad():
            for images, targets in tqdm(self.val_loader, desc='Validation'):
                images = images.to(self.device)
                targets = targets.to(self.device)
                
                if use_tta and epoch % 10 == 0:  # 每10个epoch使用一次TTA
                    pred = self.tta(self.model, images)
                    # 计算损失需要完整的outputs
                    outputs = self.model(images)
                    loss, _ = self.criterion(outputs, targets)
                else:
                    outputs = self.model(images)
                    loss, _ = self.criterion(outputs, targets)
                    
                    if 'refined_pred' in outputs:
                        pred = outputs['refined_pred'][:, 1:2]
                    else:
                        pred = outputs['ensemble_pred']
                
                total_loss += loss.item()
                val_metrics.update(pred, targets)
        
        # 计算验证指标
        epoch_metrics = val_metrics.compute()
        avg_loss = total_loss / len(self.val_loader)
        
        # 记录验证历史
        self.train_history['val_loss'].append(avg_loss)
        self.train_history['val_iou'].append(epoch_metrics['iou'])
        self.train_history['val_edge_iou'].append(epoch_metrics['edge_iou'])
        
        self.logger.info(f"验证 Epoch {epoch+1}: "
                        f"Loss={avg_loss:.4f}, "
                        f"IoU={epoch_metrics['iou']:.4f}, "
                        f"Edge_IoU={epoch_metrics['edge_iou']:.4f}")
        
        # 保存最佳模型
        if epoch_metrics['iou'] > self.best_iou:
            self.best_iou = epoch_metrics['iou']
            self.best_epoch = epoch + 1
            self.save_model('best_iou_model.pth', epoch, epoch_metrics)
            self.logger.info(f"🎉 新的最佳IoU: {self.best_iou:.4f}")
        
        if epoch_metrics['edge_iou'] > self.best_edge_iou:
            self.best_edge_iou = epoch_metrics['edge_iou']
            self.save_model('best_edge_model.pth', epoch, epoch_metrics)
            self.logger.info(f"🔥 新的最佳Edge IoU: {self.best_edge_iou:.4f}")
        
        return avg_loss, epoch_metrics
        
    def save_model(self, filename, epoch, metrics):
        """保存模型"""
        save_path = os.path.join(self.args.save_dir, filename)
        os.makedirs(self.args.save_dir, exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'metrics': metrics,
            'best_iou': self.best_iou,
            'best_edge_iou': self.best_edge_iou,
            'train_history': dict(self.train_history),
            'current_stage': self.current_stage,
            'args': vars(self.args)
        }
        
        if self.scaler:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()
        
        torch.save(checkpoint, save_path)
        self.logger.info(f"模型已保存: {save_path}")
        
    def train(self):
        """完整训练流程"""
        self.logger.info("开始90% IoU目标训练...")
        
        total_epochs = sum(stage['epochs'] for stage in self.training_stages)
        
        for epoch in range(total_epochs):
            # 训练
            train_loss, train_metrics = self.train_epoch(epoch)
            
            # 验证
            val_loss, val_metrics = self.validate(epoch, use_tta=(epoch > 100))
            
            # 更新学习率
            self.scheduler.step()
            
            # 定期保存检查点
            if (epoch + 1) % self.args.save_interval == 0:
                self.save_model(f'checkpoint_epoch_{epoch+1}.pth', epoch, val_metrics)
            
            # 早停检查（只在最后阶段）
            if self.current_stage == len(self.training_stages) - 1:
                if epoch - self.best_epoch > self.args.patience:
                    self.logger.info(f"早停触发，最佳IoU: {self.best_iou:.4f} (Epoch {self.best_epoch})")
                    break
            
            # 90% IoU目标检查
            if val_metrics['iou'] >= 0.90:
                self.logger.info(f"🎯 达到90% IoU目标！IoU: {val_metrics['iou']:.4f}")
                self.save_model('90iou_achieved.pth', epoch, val_metrics)
                # 继续训练以获得更好的性能
        
        # 保存训练历史
        history_path = os.path.join(self.args.save_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            json.dump(dict(self.train_history), f, indent=2)
        
        self.logger.info(f"训练完成！最佳IoU: {self.best_iou:.4f} (Epoch {self.best_epoch})")
        self.logger.info(f"最佳Edge IoU: {self.best_edge_iou:.4f}")


def main():
    parser = argparse.ArgumentParser(description='90% IoU目标训练')
    
    # 数据参数
    parser.add_argument('--train_data_path', type=str, required=True, help='训练数据路径')
    parser.add_argument('--val_data_path', type=str, required=True, help='验证数据路径')
    parser.add_argument('--batch_size', type=int, default=6, help='批次大小')
    parser.add_argument('--num_workers', type=int, default=8, help='数据加载器线程数')
    
    # 模型参数
    parser.add_argument('--backbone_path', type=str, default=None, help='预训练骨干网络路径')
    parser.add_argument('--crf_iter', type=int, default=6, help='CRF迭代次数')
    parser.add_argument('--trainable_crf', type=bool, default=True, help='CRF是否可训练')
    
    # 训练参数
    parser.add_argument('--lr', type=float, default=8e-5, help='学习率')
    parser.add_argument('--patience', type=int, default=30, help='早停耐心值')
    parser.add_argument('--mixed_precision', type=bool, default=True, help='混合精度训练')
    
    # 损失函数参数
    parser.add_argument('--adaptive_loss', type=bool, default=True, help='自适应损失权重')
    
    # 保存参数
    parser.add_argument('--save_dir', type=str, default='./checkpoints_90iou', help='模型保存目录')
    parser.add_argument('--log_dir', type=str, default='./logs_90iou', help='日志保存目录')
    parser.add_argument('--save_interval', type=int, default=25, help='模型保存间隔')
    
    args = parser.parse_args()
    
    # 创建训练器并开始训练
    trainer = ProgressiveTrainer(args)
    trainer.train()


if __name__ == '__main__':
    main()