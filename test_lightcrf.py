import os
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import *
from ig_glass.gdnet_lightcrf import GDNetLightCRF

# Device setup
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# Paths
backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext_101_32x4d.pth'

# Parameter set
ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM'
exp_name = 'IG-SLAM-LightCRF'
args = {
    'snapshot': 'light-150-20',  # Use 'best_model' to automatically find the best model
    'scale': 416,
    'crf': False,  # No need for CRF post-processing as it's already integrated
    'glass_threshold': 0.5,  # Threshold for glass region detection
    'crf_iter': 3,  # Number of CRF iterations
    'bilateral_weight': 10.0,  # From compat=10
    'gaussian_weight': 5.0,  # From compat=5
    'bilateral_spatial_sigma': 40.0,  # From sxy=40
    'bilateral_color_sigma': 3.0,  # From srgb=3
    'gaussian_sigma': 1.5,  # From sxy=1.5
}

# 预处理
img_transform = transforms.Compose([
    transforms.Resize((args['scale'], args['scale'])),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

to_pil = transforms.ToPILImage()


def path_set(tempdata_path, new_path):
    results_root = os.path.join(tempdata_path, new_path)
    return results_root


def detect_glass_and_evaluate(image_folder, output_folder, gt_folder, model, glass_threshold):
    """检测玻璃区域并评估结果"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]

    count = 0
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0

    # 用于性能测量
    inference_times = []

    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)

        # 读取图像
        img_pil = Image.open(image_path)
        if img_pil.mode != 'RGB':
            img_pil = img_pil.convert('RGB')
        w, h = img_pil.size

        # 预测
        img_var = Variable(img_transform(img_pil).unsqueeze(0)).cuda(device_ids[0])

        # 测量推理时间
        start_time = torch.cuda.Event(enable_timing=True)
        end_time = torch.cuda.Event(enable_timing=True)

        start_time.record()
        with torch.no_grad():
            _, _, _, refined_pred = model(img_var)  # 只需要CRF精炼后的预测结果
        end_time.record()

        # 等待GPU操作完成
        torch.cuda.synchronize()
        inference_time = start_time.elapsed_time(end_time) / 1000.0  # 转换为秒
        inference_times.append(inference_time)

        # 从CRF输出中提取前景概率（通道1）
        if refined_pred.size(1) == 2:
            crf_predict = refined_pred[:, 1:2]
        else:
            crf_predict = refined_pred

        # 转换预测结果
        crf_predict = crf_predict.data.squeeze(0).cpu()
        crf_predict_np = np.array(transforms.Resize((h, w))(to_pil(crf_predict)))

        # 二值化预测结果
        glass_mask = (crf_predict_np > glass_threshold * 255).astype(np.uint8) * 255

        # 保存结果
        temp_name = image_file.split('.')[0]
        if '.png' in image_file:
            result_name = image_file
        else:
            result_name = temp_name + '.png'
        cv2.imwrite(os.path.join(output_folder, result_name), glass_mask)

        # 评估结果
        if '.png' in image_file:
            gt_name = image_file
        else:
            gt_name = temp_name + '.png'
        gt_path = os.path.join(gt_folder, gt_name)

        if os.path.exists(gt_path):
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)

            # 确保尺寸一致
            if gt_mask.shape != glass_mask.shape:
                gt_mask = cv2.resize(gt_mask, (glass_mask.shape[1], glass_mask.shape[0]))

            # 计算评估指标
            prediction = glass_mask.astype(np.float32) / 255.0
            gt = gt_mask.astype(np.float32) / 255.0

            tiou = compute_iou(prediction, gt)
            tacc = compute_acc(prediction, gt)
            precision, recall = compute_precision_recall(prediction, gt)
            tfm = compute_fmeasure(precision, recall)
            tmae = compute_mae(prediction, gt)
            tber = compute_ber(prediction, gt)
            taber = compute_aber(prediction, gt)

            count += 1
            iou += tiou
            acc += tacc
            fm += tfm
            mae += tmae
            ber += tber
            aber += taber
            
            # if tiou < 0.8:
            #     print(f"Image {image_file}: IoU={tiou:.4f}, Acc={tacc:.4f}, F-measure={tfm:.4f}, MAE={tmae:.4f}")

            #print(f"Image {image_file}: IoU={tiou:.4f}, Acc={tacc:.4f}, F-measure={tfm:.4f}, MAE={tmae:.4f}")

    if count > 0:
        iou = iou / count
        acc = acc / count
        fm = fm / count
        mae = mae / count
        ber = ber / count
        aber = aber / count

    # 计算平均推理时间（跳过前几次作为预热）
    if len(inference_times) > 5:
        avg_inference_time = np.mean(inference_times[5:])
        fps = 1.0 / avg_inference_time
        print(f"\nAverage inference time: {avg_inference_time*1000:.2f} ms")
        print(f"FPS: {fps:.2f}")

    return iou, acc, fm, mae, ber, aber


def main():
    # 加载模型
    model = GDNetLightCRF(backbone_path=backbone_path, crf_iter=args['crf_iter'], trainable_crf=False)

    # 设置CRF参数
    if hasattr(model, 'crf') and hasattr(model.crf, 'bilateral_weight'):
        model.crf.bilateral_weight = torch.nn.Parameter(torch.tensor(args['bilateral_weight']))
        model.crf.gaussian_weight = torch.nn.Parameter(torch.tensor(args['gaussian_weight']))
        model.crf.bilateral_spatial_sigma = torch.nn.Parameter(torch.tensor(args['bilateral_spatial_sigma']))
        model.crf.bilateral_color_sigma = torch.nn.Parameter(torch.tensor(args['bilateral_color_sigma']))
        model.crf.gaussian_sigma = torch.nn.Parameter(torch.tensor(args['gaussian_sigma']))

    # 加载模型权重
    if args['snapshot'].startswith('best_model'):
        # 查找最新的best_model文件
        model_files = [f for f in os.listdir(ckpt_path) if f.startswith('best_model')]
        if model_files:
            model_path = os.path.join(ckpt_path, sorted(model_files)[-1])  # 获取最新的
            print(f"Loading best model from {model_path}")
            checkpoint = torch.load(model_path)
            model.load_state_dict(checkpoint['model'])
        else:
            print("No best_model found. Please specify a model path.")
            return
    else:
        # 加载指定的模型
        print(f'Load snapshot {args["snapshot"]} for testing')
        model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
        checkpoint = torch.load(model_path)
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
        else:
            model.load_state_dict(checkpoint)
        print(f'Load {model_path} succeed!')

    model.cuda(device_ids[0])
    model.eval()

    # 设置路径
    data_path = "/home/<USER>/Documents/ig_slam_maskdata/test_GDD"  # 测试数据路径
    current = "glass_mask_lightcrf"  # 结果文件夹名称

    # 设置输入输出路径
    image_folder = path_set(data_path, "image")  # 输入图像文件夹
    output_folder = path_set(data_path, current)  # 输出结果文件夹
    gt_folder = path_set(data_path, "mask")  # 真值文件夹

    # 检测玻璃区域并评估结果
    iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate(
        image_folder, output_folder, gt_folder, model, args['glass_threshold']
    )

    # 打印评估结果
    print("\nAverage Metrics:")
    print(f"IoU: {iou:.4f}")
    print(f"Accuracy: {acc:.4f}")
    print(f"F-measure: {fm:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"BER: {ber:.4f}")
    print(f"ABER: {aber:.4f}")

    return iou, acc, fm, mae, ber, aber


if __name__ == '__main__':
    main()