#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced SCSA Testing Script for 90%+ IoU Performance
优化的测试脚本，包含改进的后处理和评估流程

主要改进：
1. 多模型集成测试
2. 优化的后处理流程
3. 详细的性能分析
4. 自适应阈值选择
5. 边缘质量评估

目标：验证90%+ IoU性能
"""

import os
import sys
import time
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from tqdm import tqdm
import argparse

# 添加项目路径
sys.path.append('ig_glass')

from gdnet_enhanced_scsa import create_enhanced_scsa_model
from gdnet_hybrid_scsa import create_hybrid_scsa_model
from gdnet_scsa import GDNetSCSA
from misc import (compute_iou, compute_fmeasure, compute_ber, compute_acc, 
                 compute_mae, compute_precision_recall, compute_aber)


class EnhancedSCSATester:
    """增强版SCSA测试器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化模型
        self._init_models()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((args.input_size, args.input_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        print(f"🚀 Enhanced SCSA测试器初始化完成")
        print(f"   设备: {self.device}")
        print(f"   模型数量: {len(self.models)}")
        print(f"   目标: 验证90%+ IoU")
    
    def _init_models(self):
        """初始化多个模型用于集成"""
        self.models = {}
        
        # 1. 增强版SCSA模型
        if os.path.exists(self.args.enhanced_model_path):
            print("📦 加载增强版SCSA模型...")
            model = create_enhanced_scsa_model(
                backbone_path=self.args.backbone_path,
                crf_iter=self.args.crf_iter
            )
            checkpoint = torch.load(self.args.enhanced_model_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device).eval()
            self.models['enhanced'] = model
        
        # 2. 混合SCSA模型
        if os.path.exists(self.args.hybrid_model_path):
            print("📦 加载混合SCSA模型...")
            model = create_hybrid_scsa_model(
                backbone_path=self.args.backbone_path,
                crf_iter=self.args.crf_iter
            )
            checkpoint = torch.load(self.args.hybrid_model_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device).eval()
            self.models['hybrid'] = model
        
        # 3. 原始SCSA模型（作为基准）
        if os.path.exists(self.args.baseline_model_path):
            print("📦 加载基准SCSA模型...")
            model = GDNetSCSA(backbone_path=self.args.backbone_path)
            checkpoint = torch.load(self.args.baseline_model_path, map_location=self.device)
            model.load_state_dict(checkpoint)
            model.to(self.device).eval()
            self.models['baseline'] = model
        
        if not self.models:
            raise ValueError("没有找到有效的模型文件！")
    
    def _adaptive_threshold_selection(self, predictions, targets):
        """自适应阈值选择"""
        best_threshold = 0.5
        best_iou = 0.0
        
        # 测试不同阈值
        thresholds = np.arange(0.3, 0.8, 0.05)
        
        for threshold in thresholds:
            pred_binary = (predictions > threshold).astype(np.float32)
            iou = compute_iou(pred_binary, targets)
            
            if iou > best_iou:
                best_iou = iou
                best_threshold = threshold
        
        return best_threshold, best_iou
    
    def _post_process_prediction(self, prediction, original_size):
        """优化的后处理流程"""
        # 1. 调整尺寸
        prediction = cv2.resize(prediction, original_size, interpolation=cv2.INTER_LINEAR)
        
        # 2. 高斯滤波平滑
        prediction = cv2.GaussianBlur(prediction, (3, 3), 0.5)
        
        # 3. 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        prediction = cv2.morphologyEx(prediction, cv2.MORPH_CLOSE, kernel)
        prediction = cv2.morphologyEx(prediction, cv2.MORPH_OPEN, kernel)
        
        # 4. 边缘增强
        edges = cv2.Canny((prediction * 255).astype(np.uint8), 50, 150)
        edges = edges.astype(np.float32) / 255.0
        prediction = prediction + 0.1 * edges
        prediction = np.clip(prediction, 0, 1)
        
        return prediction
    
    def _ensemble_predictions(self, predictions_dict):
        """集成多个模型的预测结果"""
        if len(predictions_dict) == 1:
            return list(predictions_dict.values())[0]
        
        # 加权平均集成
        weights = {
            'enhanced': 0.4,
            'hybrid': 0.4,
            'baseline': 0.2
        }
        
        ensemble_pred = None
        total_weight = 0
        
        for model_name, prediction in predictions_dict.items():
            weight = weights.get(model_name, 1.0 / len(predictions_dict))
            
            if ensemble_pred is None:
                ensemble_pred = weight * prediction
            else:
                ensemble_pred += weight * prediction
            
            total_weight += weight
        
        return ensemble_pred / total_weight
    
    def test_single_image(self, image_path, gt_path=None):
        """测试单张图像"""
        # 加载图像
        image = Image.open(image_path).convert('RGB')
        original_size = image.size
        
        # 预处理
        input_tensor = self.transform(image).unsqueeze(0).to(self.device)
        
        # 多模型预测
        predictions = {}
        
        with torch.no_grad():
            for model_name, model in self.models.items():
                try:
                    if model_name == 'baseline':
                        # 原始SCSA模型的输出格式
                        output = model(input_tensor)
                        if isinstance(output, dict):
                            pred = output.get('refined_pred', output.get('ensemble_pred', output.get('pred', None)))
                        else:
                            pred = output
                        
                        if pred is not None and pred.dim() == 4 and pred.size(1) == 2:
                            pred = pred[:, 1:2]  # 取前景通道
                    else:
                        # 增强版模型的输出格式
                        output = model(input_tensor)
                        pred = output.get('refined_pred', output.get('ensemble_pred'))
                        
                        if pred.dim() == 4 and pred.size(1) == 2:
                            pred = pred[:, 1:2]  # 取前景通道
                    
                    # 转换为numpy
                    pred_np = pred.squeeze().cpu().numpy()
                    
                    # 后处理
                    pred_processed = self._post_process_prediction(pred_np, original_size)
                    predictions[model_name] = pred_processed
                    
                except Exception as e:
                    print(f"⚠️ 模型 {model_name} 预测失败: {e}")
                    continue
        
        if not predictions:
            raise RuntimeError("所有模型预测都失败了！")
        
        # 集成预测
        ensemble_pred = self._ensemble_predictions(predictions)
        
        # 如果有真值，计算指标
        metrics = {}
        if gt_path and os.path.exists(gt_path):
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
            if gt_mask.shape != ensemble_pred.shape:
                gt_mask = cv2.resize(gt_mask, original_size)
            
            gt_normalized = gt_mask.astype(np.float32) / 255.0
            
            # 自适应阈值选择
            best_threshold, _ = self._adaptive_threshold_selection(ensemble_pred, gt_normalized)
            
            # 二值化预测
            pred_binary = (ensemble_pred > best_threshold).astype(np.float32)
            
            # 计算指标
            metrics = {
                'iou': compute_iou(pred_binary, gt_normalized),
                'accuracy': compute_acc(pred_binary, gt_normalized),
                'mae': compute_mae(pred_binary, gt_normalized),
                'ber': compute_ber(pred_binary, gt_normalized),
                'aber': compute_aber(pred_binary, gt_normalized),
                'threshold': best_threshold
            }
            
            # 计算F-measure
            precision, recall = compute_precision_recall(pred_binary, gt_normalized)
            metrics['f_measure'] = compute_fmeasure(precision, recall)
        
        return ensemble_pred, predictions, metrics
    
    def test_dataset(self, image_folder, gt_folder, output_folder):
        """测试整个数据集"""
        os.makedirs(output_folder, exist_ok=True)
        
        image_files = [f for f in os.listdir(image_folder) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        all_metrics = []
        model_metrics = {name: [] for name in self.models.keys()}
        
        print(f"🔍 开始测试 {len(image_files)} 张图像...")
        
        for image_file in tqdm(image_files, desc="测试进度"):
            try:
                image_path = os.path.join(image_folder, image_file)
                gt_path = os.path.join(gt_folder, image_file.replace('.jpg', '.png'))
                
                # 测试图像
                ensemble_pred, individual_preds, metrics = self.test_single_image(image_path, gt_path)
                
                # 保存结果
                result_path = os.path.join(output_folder, image_file.replace('.jpg', '.png'))
                cv2.imwrite(result_path, (ensemble_pred * 255).astype(np.uint8))
                
                # 收集指标
                if metrics:
                    all_metrics.append(metrics)
                    
                    # 单独评估每个模型
                    if gt_folder and os.path.exists(gt_path):
                        gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE).astype(np.float32) / 255.0
                        
                        for model_name, pred in individual_preds.items():
                            pred_binary = (pred > 0.5).astype(np.float32)
                            model_iou = compute_iou(pred_binary, gt_mask)
                            model_metrics[model_name].append(model_iou)
                
            except Exception as e:
                print(f"❌ 处理图像 {image_file} 失败: {e}")
                continue
        
        # 计算平均指标
        if all_metrics:
            avg_metrics = {}
            for key in all_metrics[0].keys():
                if key != 'threshold':
                    avg_metrics[key] = np.mean([m[key] for m in all_metrics])
            
            print("\n📊 集成模型性能:")
            print(f"   IoU: {avg_metrics['iou']:.4f}")
            print(f"   Accuracy: {avg_metrics['accuracy']:.4f}")
            print(f"   F-measure: {avg_metrics['f_measure']:.4f}")
            print(f"   MAE: {avg_metrics['mae']:.4f}")
            print(f"   BER: {avg_metrics['ber']:.4f}")
            print(f"   ABER: {avg_metrics['aber']:.4f}")
            
            # 检查是否达到90%
            if avg_metrics['iou'] >= 0.90:
                print(f"🎉 恭喜！成功突破90% IoU！当前IoU: {avg_metrics['iou']:.4f}")
            else:
                print(f"📈 当前IoU: {avg_metrics['iou']:.4f}，距离90%还差: {0.90 - avg_metrics['iou']:.4f}")
            
            # 单模型性能对比
            print("\n📊 单模型性能对比:")
            for model_name, ious in model_metrics.items():
                if ious:
                    avg_iou = np.mean(ious)
                    print(f"   {model_name}: {avg_iou:.4f}")
            
            return avg_metrics
        
        return None


def main():
    parser = argparse.ArgumentParser(description='Enhanced SCSA Testing for 90%+ IoU')
    parser.add_argument('--backbone_path', type=str,
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth',
                       help='Backbone weights path')
    parser.add_argument('--enhanced_model_path', type=str,
                       default='checkpoints/enhanced_scsa/best.pth',
                       help='Enhanced SCSA model path')
    parser.add_argument('--hybrid_model_path', type=str,
                       default='checkpoints/hybrid_scsa/best.pth',
                       help='Hybrid SCSA model path')
    parser.add_argument('--baseline_model_path', type=str,
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/scsa-30-150.pth',
                       help='Baseline SCSA model path')
    parser.add_argument('--input_size', type=int, default=416, help='Input image size')
    parser.add_argument('--crf_iter', type=int, default=5, help='CRF iterations')
    parser.add_argument('--image_folder', type=str,
                       default='/home/<USER>/Documents/ig_slam_maskdata/test_GDD/image',
                       help='Test images folder')
    parser.add_argument('--gt_folder', type=str,
                       default='/home/<USER>/Documents/ig_slam_maskdata/test_GDD/mask',
                       help='Ground truth masks folder')
    parser.add_argument('--output_folder', type=str,
                       default='results/enhanced_scsa_test',
                       help='Output results folder')
    
    args = parser.parse_args()
    
    # 创建测试器并运行测试
    tester = EnhancedSCSATester(args)
    metrics = tester.test_dataset(args.image_folder, args.gt_folder, args.output_folder)
    
    if metrics:
        print(f"\n✅ 测试完成！最终IoU: {metrics['iou']:.4f}")
    else:
        print("❌ 测试失败，没有获得有效指标")


if __name__ == '__main__':
    main()
