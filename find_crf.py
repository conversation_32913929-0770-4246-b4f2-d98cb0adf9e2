import optuna
import os
import xlwt
import numpy as np
import pydensecrf.densecrf as dcrf
from skimage import io
import ig_glass.misc as misc

import os
import time
import random
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import check_mkdir, crf_refine,compute_iou, compute_fmeasure,compute_ber,compute_acc,compute_mae,compute_precision_recall,calculate_fpr
from ig_glass.gdnet import GDNet
#from config import gdd_testing_root, gdd_results_root, backbone_path
backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext_101_32x4d.pth'
#gdd_training_root = "/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/re200.pth"
# device set
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# parameter set
ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
exp_name = 'IG-SLAM'
args = {
    'snapshot': 'rere300', #200(orin),rere300(new)
    'scale': 416,
    'crf': True, #True for grid search, False for original results
    'glass_threshold': 0.5,  # 玻璃区域识别阈值
}

# 预处理
img_transform = transforms.Compose([
    transforms.Resize((args['scale'], args['scale'])),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

to_pil = transforms.ToPILImage()

# 动态亮度调整目标范围
BRIGHTNESS_TARGET_MIN = 100
BRIGHTNESS_TARGET_MAX = 120


def calculate_brightness(image_path):
    """计算图像亮度"""
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])
    return brightness


def calculate_feature_points(image_path):
    """计算图像特征点"""
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    orb = cv2.ORB_create(nfeatures=10000)
    keypoints = orb.detect(image, None)
    return keypoints, len(keypoints)


def adjust_image_brightness(image, adjustment):
    """根据调整值调整图像亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    v = np.clip(v.astype(np.int32) + int(adjustment), 0, 255).astype(np.uint8)
    hsv = cv2.merge((h, s, v))
    return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)



def detect_glass_and_adjust_brightness(image_folder, gt_folder,output_folder, net, glass_threshold,crf_params):
    """检测玻璃区域并调整亮度"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    brightness_list = []
    feature_points_list = []
    score = 0
    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)
        temp_name = image_file.split('.')[0]
        gt_name = temp_name+'.png'
        gt_path = os.path.join(gt_folder, gt_name)
        gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
                # Step 2: 读取原始图像
        original_image = cv2.imread(image_path)
        #adjusted_image = adjust_image_brightness_dynamically(original_image, BRIGHTNESS_TARGET_MIN, BRIGHTNESS_TARGET_MAX)
        #temp_path = os.path.join(output_folder, 'temp.png')
        #cv2.imwrite(temp_path, adjusted_image)
        # Step 1: Identify glass region and boundaries
        img_pil = Image.open(image_path)
        if img_pil.mode != 'RGB':
            img_pil = img_pil.convert('RGB')
        w, h = img_pil.size
        img_var = Variable(img_transform(img_pil).unsqueeze(0)).cuda(device_ids[0])
        f1, f2, f3 = net(img_var)
        f3 = f3.data.squeeze(0).cpu()
        f3_resized = np.array(transforms.Resize((h, w))(to_pil(f3)))

        # 使用 glass_threshold 来调整玻璃区域识别的精度
        #glass_mask = (f3_resized > glass_threshold).astype(np.uint8) if f3_resized.max() > glass_threshold else np.zeros((h, w), dtype=np.uint8)

        # Optional: Apply CRF if activated
        if args['crf']:
            #use grid search to find the best parameters
            best_score = -1
            best_params = None
            #original_image = cv2.imread(image_path)
            #adjusted_image  = cv2.imread(temp_path)
            f3_resized = crf_refine(original_image, f3_resized,crf_params)
        
        glass_mask = (f3_resized > glass_threshold).astype(np.uint8) if f3_resized.max() > glass_threshold else np.zeros((h, w), dtype=np.uint8)
        no_glass = np.zeros((h, w), dtype=np.uint8)
            
        #将glass——mask直接图形化
        #image_mask = glass_mask * 255
        t_score = compute_ber(glass_mask, gt_mask)
        score+=t_score
    score = score/len(image_files)
    print(f"current score: {score}")
        # #如果玻璃领域超过一定比例，则该部分没有mask
        # if np.sum(glass_mask) / (h * w) > 0.55:
        #     image_mask = no_glass
        # else:
        #    image_mask = glass_mask * 255


        # # 计算玻璃区域的边缘轮廓
        # contours, _ = cv2.findContours(glass_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # # 绘制玻璃区域边框
        # image_with_contours = adjusted_image.copy()
        # cv2.drawContours(image_with_contours, contours, -1, (0, 0, 255), thickness=3)

        # # Step 3: 计算特征点并清除玻璃区域内的特征点
        # #keypoints, feature_points = calculate_feature_points(temp_path)
        # #keypoints_outside_glass = [kp for kp in keypoints if not glass_mask[int(kp.pt[1]), int(kp.pt[0])]]
        # keypoints_outside_glass = []

        # # 在图像上绘制保留下来的特征点
        # image_with_keypoints = cv2.drawKeypoints(image_with_contours, keypoints_outside_glass, None, color=(0, 255, 0))

        # # 保存结果
        # output_path = os.path.join(output_folder, image_file)
        #glass_mask_path = os.path.join(output_folder, image_file)
        #cv2.imwrite(output_path, image_with_keypoints)
        #cv2.imwrite(glass_mask_path, image_mask)

    return score

def path_set(tempdata_path,new_path):
    gdd_results_root = os.path.join(tempdata_path,new_path)
    return gdd_results_root



def evaluate_results(output_folder,gt_folder):
    # 获取所有图像文件
    image_files = [f for f in os.listdir(output_folder) if f != 'temp.png']
    # 打乱图像文件列表
    random.shuffle(image_files)
    # 计算每组的大致数量
    group_size = len(image_files) // 3
    # 分割成 6 组
    groups = [image_files[i*group_size:(i+1)*group_size] for i in range(3)]
    if len(image_files) % 3 != 0:
        # 处理剩余的文件
        remaining = image_files[3*group_size:]
        for i, file in enumerate(remaining):
            groups[i].append(file)

    all_results = []
    for group in groups:
        count = 0
        iou, acc, fm, mae, ber,fpr = 0, 0, 0, 0,0,0 
        for image_file in group:
            temp_name = image_file.split('.')[0]
            image_path = os.path.join(output_folder, image_file)
            gt_name = temp_name + '.png'
            gt_path = os.path.join(gt_folder, gt_name)
            image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
            prediction = image.astype(np.float32) / 255.0
            gt = gt_mask.astype(np.float32) / 255.0
            tiou = compute_iou(prediction, gt)
            tacc = compute_acc(prediction, gt)
            precision, recall = compute_precision_recall(prediction, gt)
            tfm = compute_fmeasure(precision, recall)
            tmae = compute_mae(prediction, gt)
            tber = compute_ber(prediction, gt)
            tfpr = calculate_fpr(prediction,gt)
            count += 1
            iou += tiou
            acc += tacc
            fm += tfm
            mae += tmae
            ber += tber
            fpr +=tfpr
        if count > 0:
            iou = iou / count
            acc = acc / count
            fm = fm / count
            mae = mae / count
            ber = ber / count
            fpr = fpr/count
        all_results.append((iou, acc, fm, mae, ber,fpr))
    #all results / len(groups) get avg results
    avg_iou = sum([result[0] for result in all_results]) / len(all_results)
    avg_acc = sum([result[1] for result in all_results]) / len(all_results)
    avg_fm = sum([result[2] for result in all_results]) / len(all_results)
    avg_mae = sum([result[3] for result in all_results]) / len(all_results)
    avg_ber = sum([result[4] for result in all_results]) / len(all_results)
    avg_fpr = sum([result[5] for result in all_results]) / len(all_results)

    return avg_iou, avg_acc, avg_fm, avg_mae, avg_ber,avg_fpr

def objective(trial):
    # Step 1: Load GDNet model
    net = GDNet(backbone_path=backbone_path).cuda(device_ids[0])
    if len(args['snapshot']) > 0:
        print(f'Load snapshot {args["snapshot"]} for testing')
        temp_path = torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth'))
        if args['snapshot']=='200':
            net.load_state_dict(temp_path)
        else:
            net.load_state_dict(temp_path['model'])
        print(f'Load {os.path.join(ckpt_path, exp_name, args["snapshot"] + ".pth")} succeed!')

    net.eval()

    current = "glass_mask"
    data_path = "/home/<USER>/Documents/ig_slam_maskdata/new_trainset"
    # Step 5: Input paths (Modify here to change dataset paths)
    image_folder = path_set(data_path,"image")  # Set your dataset input folder here
    output_folder = path_set(data_path,current)
    gt_folder = path_set(data_path,"mask")
    #output_folder = gdd_results_root  # Set your output folder here

        
    #training test
    # 定义超参数搜索空间
    sxy1 = trial.suggest_float("sxy1", 0.3, 1.0, log=True)
    sxy2 = trial.suggest_float("sxy2", 20, 40)
    srgb = trial.suggest_float("srgb", 1.0, 3.0)
    compat1 = trial.suggest_int("compat1", 3, 10)
    compat2 = trial.suggest_int("compat2", 10, 15)
    crf_params = [sxy1, sxy2, srgb, compat1, compat2]
    score = detect_glass_and_adjust_brightness(image_folder, gt_folder,output_folder, net, args['glass_threshold'],crf_params)
            # # Step 6: evaluate the results each image
            # #evaluate_results(output_folder, gt_folder)
            # iou,acc,fm,mae,ber,fpr = evaluate_results(output_folder,gt_folder)
            # print(f"crf_params: {crf_params}")
            # print(f"IOU: {iou}")
            # print(f"Accuracy: {acc}")
            # #print(f"Precision: {pa}")
            # print(f"F-measure: {fm}")
            # print(f"MAE: {mae}")
            # print(f"BER:{ber}")
            # print(f"FPR:{fpr}")
    return score
    
if __name__ == '__main__':

    # 启动优化
    study = optuna.create_study(direction="minimize")
    study.optimize(objective, n_trials=100)
    print("Best params:", study.best_params)
    df = study.trials_dataframe()
    df.to_csv("optuna_trials.csv")  # 保存为CSV

    # 查看DataFrame前5行
    print(df.head())
