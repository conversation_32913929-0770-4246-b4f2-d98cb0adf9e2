#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ViT对比训练脚本
对比原始ViT和优化ViT的性能差异

主要对比：
1. 原始ViT (patch_size=14, 224x224输入)
2. 优化ViT (patch_size=8, 416x416输入)
3. SCSA基准 (87.95% IoU)

目标：验证ViT优化效果，争取达到90%+ IoU
"""

import os
import sys
import time
import argparse
import numpy as np
from datetime import datetime

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")

# 添加项目路径
sys.path.append('ig_glass')

from gdnet_optimized_vit import create_optimized_vit_model
from gdnet_proteus_vits import create_proteus_vits_model
from gdnet_scsa import GDNetSCSA
from loss_enhanced_scsa import create_enhanced_scsa_loss
from glass_dataloader import GlassDataLoader


class ViTComparisonTrainer:
    """ViT对比训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化多个模型进行对比
        self._init_models()
        self._init_optimizers()
        self._init_dataloaders()
        self._init_logging()
        
        # 性能跟踪
        self.best_ious = {name: 0.0 for name in self.models.keys()}
        self.training_history = {name: [] for name in self.models.keys()}
        
        print(f"🚀 ViT对比训练器初始化完成")
        print(f"   设备: {self.device}")
        print(f"   对比模型: {list(self.models.keys())}")
    
    def _init_models(self):
        """初始化对比模型"""
        self.models = {}
        self.criterions = {}
        
        # 1. 优化ViT模型
        print("📦 创建优化ViT模型...")
        self.models['optimized_vit'] = create_optimized_vit_model(
            backbone_path=self.args.backbone_path,
            crf_iter=self.args.crf_iter
        ).to(self.device)
        
        # 2. 原始Proteus ViT模型
        if self.args.include_original_vit:
            print("📦 创建原始Proteus ViT模型...")
            self.models['original_vit'] = create_proteus_vits_model(
                backbone_path=self.args.backbone_path,
                crf_iter=self.args.crf_iter,
                vit_type='vits'
            ).to(self.device)
        
        # 3. SCSA基准模型
        if self.args.include_scsa_baseline:
            print("📦 创建SCSA基准模型...")
            self.models['scsa_baseline'] = GDNetSCSA(
                backbone_path=self.args.backbone_path
            ).to(self.device)
        
        # 为每个模型创建损失函数
        for name in self.models.keys():
            self.criterions[name] = create_enhanced_scsa_loss(
                focal_weight=0.15,
                iou_weight=0.65,
                edge_weight=0.15,
                consistency_weight=0.05,
                adaptive=True
            )
        
        # 打印模型参数量
        for name, model in self.models.items():
            param_count = sum(p.numel() for p in model.parameters()) / 1e6
            print(f"   {name}: {param_count:.1f}M 参数")
    
    def _init_optimizers(self):
        """初始化优化器"""
        self.optimizers = {}
        self.schedulers = {}
        
        for name, model in self.models.items():
            # 为不同模型使用不同的学习率
            if 'vit' in name:
                lr = self.args.lr * 0.5  # ViT使用较小的学习率
            else:
                lr = self.args.lr
            
            self.optimizers[name] = optim.AdamW(
                model.parameters(),
                lr=lr,
                betas=(0.9, 0.999),
                weight_decay=1e-4
            )
            
            self.schedulers[name] = optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizers[name], T_0=10, T_mult=2, eta_min=1e-6
            )
    
    def _init_dataloaders(self):
        """初始化数据加载器"""
        # 训练数据
        self.train_loader = DataLoader(
            GlassDataLoader(
                mode='train',
                augment_data=True,
                target_size=self.args.input_size,
                glass_augmentation='moderate'
            ),
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        # 验证数据
        self.val_loader = DataLoader(
            GlassDataLoader(
                mode='test',
                augment_data=False,
                target_size=self.args.input_size
            ),
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            pin_memory=True
        )
    
    def _init_logging(self):
        """初始化日志"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.log_dir = f"runs/vit_comparison_{timestamp}"
        self.writers = {}
        
        for name in self.models.keys():
            self.writers[name] = SummaryWriter(f"{self.log_dir}/{name}")
        
        # 创建检查点目录
        self.checkpoint_dir = f"checkpoints/vit_comparison_{timestamp}"
        os.makedirs(self.checkpoint_dir, exist_ok=True)
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        epoch_results = {}
        
        for name, model in self.models.items():
            model.train()
            total_loss = 0.0
            total_iou = 0.0
            
            pbar = tqdm(self.train_loader, desc=f'{name} Epoch {epoch+1}')
            
            for batch_idx, (images, targets) in enumerate(pbar):
                images = images.to(self.device)
                targets = targets.to(self.device)
                
                # 前向传播
                predictions = model(images)
                
                # 处理不同模型的输出格式
                if name == 'scsa_baseline':
                    if isinstance(predictions, dict):
                        ensemble_pred = predictions.get('refined_pred', predictions.get('pred', None))
                        if ensemble_pred is not None and ensemble_pred.dim() == 4 and ensemble_pred.size(1) == 2:
                            ensemble_pred = ensemble_pred[:, 1:2]
                    else:
                        ensemble_pred = predictions
                    
                    # 为SCSA创建兼容的预测字典
                    pred_dict = {
                        'pred_h': ensemble_pred,
                        'pred_l': ensemble_pred,
                        'ensemble_pred': ensemble_pred,
                        'refined_pred': ensemble_pred
                    }
                elif name == 'original_vit':
                    pred_dict = predictions
                    ensemble_pred = predictions.get('ensemble_pred', predictions.get('refined_pred'))
                    if ensemble_pred.dim() == 4 and ensemble_pred.size(1) == 2:
                        ensemble_pred = ensemble_pred[:, 1:2]
                else:  # optimized_vit
                    pred_dict = {
                        'pred_h': predictions['pred'],
                        'pred_l': predictions['pred'],
                        'ensemble_pred': predictions['pred'],
                        'refined_pred': predictions['pred']
                    }
                    ensemble_pred = predictions['pred']
                
                # 计算性能指标
                with torch.no_grad():
                    pred_binary = (ensemble_pred > 0.5).float()
                    intersection = torch.sum(pred_binary * targets, dim=(2, 3))
                    union = torch.sum(pred_binary, dim=(2, 3)) + torch.sum(targets, dim=(2, 3)) - intersection
                    iou = torch.mean(intersection / (union + 1e-7))
                
                # 计算损失
                loss_dict = self.criterions[name](pred_dict, targets)
                loss = loss_dict['total_loss']
                
                # 反向传播
                self.optimizers[name].zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                self.optimizers[name].step()
                
                # 更新统计
                total_loss += loss.item()
                total_iou += iou.item()
                
                # 更新进度条
                pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'IoU': f'{iou.item():.4f}'
                })
                
                # 记录到tensorboard
                if batch_idx % 50 == 0:
                    step = epoch * len(self.train_loader) + batch_idx
                    self.writers[name].add_scalar('Train/Loss', loss.item(), step)
                    self.writers[name].add_scalar('Train/IoU', iou.item(), step)
            
            # 更新学习率
            self.schedulers[name].step()
            
            avg_loss = total_loss / len(self.train_loader)
            avg_iou = total_iou / len(self.train_loader)
            
            epoch_results[name] = {'loss': avg_loss, 'iou': avg_iou}
        
        return epoch_results
    
    def validate(self, epoch):
        """验证所有模型"""
        val_results = {}
        
        for name, model in self.models.items():
            model.eval()
            total_loss = 0.0
            total_iou = 0.0
            
            with torch.no_grad():
                for images, targets in tqdm(self.val_loader, desc=f'Val {name}'):
                    images = images.to(self.device)
                    targets = targets.to(self.device)
                    
                    predictions = model(images)
                    
                    # 处理输出格式（同训练时）
                    if name == 'scsa_baseline':
                        if isinstance(predictions, dict):
                            ensemble_pred = predictions.get('refined_pred', predictions.get('pred', None))
                            if ensemble_pred is not None and ensemble_pred.dim() == 4 and ensemble_pred.size(1) == 2:
                                ensemble_pred = ensemble_pred[:, 1:2]
                        else:
                            ensemble_pred = predictions
                        
                        pred_dict = {
                            'pred_h': ensemble_pred,
                            'pred_l': ensemble_pred,
                            'ensemble_pred': ensemble_pred,
                            'refined_pred': ensemble_pred
                        }
                    elif name == 'original_vit':
                        pred_dict = predictions
                        ensemble_pred = predictions.get('ensemble_pred', predictions.get('refined_pred'))
                        if ensemble_pred.dim() == 4 and ensemble_pred.size(1) == 2:
                            ensemble_pred = ensemble_pred[:, 1:2]
                    else:  # optimized_vit
                        pred_dict = {
                            'pred_h': predictions['pred'],
                            'pred_l': predictions['pred'],
                            'ensemble_pred': predictions['pred'],
                            'refined_pred': predictions['pred']
                        }
                        ensemble_pred = predictions['pred']
                    
                    # 计算损失和IoU
                    loss_dict = self.criterions[name](pred_dict, targets)
                    loss = loss_dict['total_loss']
                    
                    pred_binary = (ensemble_pred > 0.5).float()
                    intersection = torch.sum(pred_binary * targets, dim=(2, 3))
                    union = torch.sum(pred_binary, dim=(2, 3)) + torch.sum(targets, dim=(2, 3)) - intersection
                    iou = torch.mean(intersection / (union + 1e-7))
                    
                    total_loss += loss.item()
                    total_iou += iou.item()
            
            avg_loss = total_loss / len(self.val_loader)
            avg_iou = total_iou / len(self.val_loader)
            
            # 记录到tensorboard
            self.writers[name].add_scalar('Val/Loss', avg_loss, epoch)
            self.writers[name].add_scalar('Val/IoU', avg_iou, epoch)
            
            val_results[name] = {'loss': avg_loss, 'iou': avg_iou}
            
            # 更新最佳IoU
            if avg_iou > self.best_ious[name]:
                self.best_ious[name] = avg_iou
                self.save_checkpoint(name, epoch, avg_iou, is_best=True)
        
        return val_results
    
    def save_checkpoint(self, model_name, epoch, iou, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.models[model_name].state_dict(),
            'optimizer_state_dict': self.optimizers[model_name].state_dict(),
            'scheduler_state_dict': self.schedulers[model_name].state_dict(),
            'iou': iou
        }
        
        if is_best:
            torch.save(checkpoint, os.path.join(self.checkpoint_dir, f'{model_name}_best.pth'))
            print(f"💾 保存{model_name}最佳模型，IoU: {iou:.4f}")
    
    def train(self):
        """主训练循环"""
        print("🚀 开始ViT对比训练")
        
        for epoch in range(self.args.epochs):
            print(f"\n=== Epoch {epoch+1}/{self.args.epochs} ===")
            
            # 训练
            train_results = self.train_epoch(epoch)
            
            # 验证
            val_results = self.validate(epoch)
            
            # 打印对比结果
            print("\n📊 训练结果对比:")
            for name in self.models.keys():
                train_iou = train_results[name]['iou']
                val_iou = val_results[name]['iou']
                print(f"  {name:15s}: Train IoU={train_iou:.4f}, Val IoU={val_iou:.4f}")
                
                # 记录历史
                self.training_history[name].append(val_iou)
        
        # 最终结果总结
        print("\n🎯 最终结果总结:")
        print("=" * 60)
        for name, best_iou in self.best_ious.items():
            print(f"{name:15s}: 最佳IoU = {best_iou:.4f}")
            if best_iou >= 0.90:
                print(f"🎉 {name} 成功突破90% IoU!")
        
        # 关闭writers
        for writer in self.writers.values():
            writer.close()


def main():
    parser = argparse.ArgumentParser(description='ViT Comparison Training')
    parser.add_argument('--backbone_path', type=str,
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth',
                       help='Backbone weights path')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=6, help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--input_size', type=int, default=416, help='Input image size')
    parser.add_argument('--num_workers', type=int, default=4, help='Number of workers')
    parser.add_argument('--crf_iter', type=int, default=5, help='CRF iterations')
    parser.add_argument('--include_original_vit', action='store_true', 
                       help='Include original Proteus ViT for comparison')
    parser.add_argument('--include_scsa_baseline', action='store_true',
                       help='Include SCSA baseline for comparison')
    
    args = parser.parse_args()
    
    # 创建训练器并开始训练
    trainer = ViTComparisonTrainer(args)
    trainer.train()


if __name__ == '__main__':
    main()
