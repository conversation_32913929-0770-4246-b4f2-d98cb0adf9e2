from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

import os
import argparse
import numpy as np
import torch
import torch.optim as optim
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from tqdm import tqdm
from ig_glass.dataloader import SOD<PERSON>oader, FODLoader
from ig_glass.gdnet_crf import GDNetCRF, GDNetCRFv2
from ig_glass.loss import EdgeSaliencyLoss, IOU, BCE, FocalLoss
from torch.utils.tensorboard import SummaryWriter

backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext_101_32x4d.pth'
# 初始化Writer
writer_train = SummaryWriter(log_dir='runs/crf_train6')
writer_vad = SummaryWriter(log_dir='runs/crf_valid6')

def parse_arguments():
    parser = argparse.ArgumentParser(description='Parameters to train your model.')
    parser.add_argument('--epochs', default=302, help='Number of epochs to train the model for', type=int)
    parser.add_argument('--bs', default=6, help='Batch size', type=int)
    parser.add_argument('--lr', default=0.001, help='Learning Rate', type=float)
    parser.add_argument('--wd', default=0.0005, help='L2 Weight decay for sgd', type=float)
    parser.add_argument('--img_size', default=416, help='Image size to be used for training', type=int)
    parser.add_argument('--aug', default=True, help='Whether to use Image augmentation', type=bool)
    parser.add_argument('--n_worker', default=1, help='Number of workers to use for loading data', type=int)
    parser.add_argument('--test_interval', default=10, help='Number of epochs after which to test the weights', type=int)
    parser.add_argument('--save_interval', default=20, help='Number of epochs after which to save the weights. If None, does not save', type=int)
    parser.add_argument('--save_opt', default=False, help='Whether to save optimizer along with model weights or not', type=bool)
    parser.add_argument('--log_interval', default=250, help='Logging interval (in #batches)', type=int)
    parser.add_argument('--res_mod', default="./ckpt", help='Path to the model to resume from, default None', type=str) #./ckpt
    parser.add_argument('--res_opt', default=None, help='Path to the optimizer to resume from', type=str)
    parser.add_argument('--use_gpu', default=True, help='Flag to use GPU or not', type=bool)
    parser.add_argument('--base_save_path', default='/home/<USER>/ws/IG_SLAM/ig_glass/models_crf', help='Base path for the models to be saved', type=str)
    parser.add_argument('--crf_iter', default=3, help='Number of CRF iterations', type=int)
    parser.add_argument('--trainable_crf', default=True, help='Whether to make CRF parameters trainable', type=bool)
    parser.add_argument('--model_version', default='v2', help='Model version to use (v1 or v2)', type=str)
    parser.add_argument('--crf_weight', default=1.0, help='Weight for CRF loss', type=float)
    # CRF parameters based on grid search
    parser.add_argument('--bilateral_weight', default=10.0, help='Weight for bilateral filter (from compat=10)', type=float)
    parser.add_argument('--gaussian_weight', default=5.0, help='Weight for gaussian filter (from compat=5)', type=float)
    parser.add_argument('--bilateral_spatial_sigma', default=40.0, help='Spatial sigma for bilateral filter (from sxy=40)', type=float)
    parser.add_argument('--bilateral_color_sigma', default=3.0, help='Color sigma for bilateral filter (from srgb=3)', type=float)
    parser.add_argument('--gaussian_sigma', default=1.5, help='Sigma for gaussian filter (from sxy=1.5)', type=float)
    parser.add_argument('--focal_alpha', default=0.9, help='Alpha parameter for focal loss', type=float)
    parser.add_argument('--focal_gamma', default=3.0, help='Gamma parameter for focal loss', type=float)
    parser.add_argument('--use_kfold', default=False, help='Whether to use k-fold cross validation', type=bool)

    return parser.parse_args()

class EarlyStopping:
    def __init__(self, patience=5, min_delta=0.001):
        """
        patience: 连续多少轮没有提升就停止训练
        min_delta: 最小变化值，小于这个值认为没有提升
        """
        self.patience = patience
        self.min_delta = min_delta
        self.best_loss = float('inf')
        self.counter = 0

    def step(self, val_loss):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0  # 重置计数器
        else:
            self.counter += 1  # 记录连续无提升的次数

        return self.counter >= self.patience  # 超过 patience 轮就返回 True

class Engine:
    def __init__(self, args):
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.wd = args.wd
        self.img_size = args.img_size
        self.aug = args.aug
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.save_opt = args.save_opt
        self.log_interval = args.log_interval
        self.res_mod_path = args.res_mod
        self.res_opt_path = args.res_opt
        self.use_gpu = args.use_gpu
        self.crf_iter = args.crf_iter
        self.trainable_crf = args.trainable_crf
        self.model_version = args.model_version
        self.crf_weight = args.crf_weight
        self.focal_alpha = args.focal_alpha
        self.focal_gamma = args.focal_gamma
        self.use_kfold = args.use_kfold

        # CRF parameters
        self.bilateral_weight = args.bilateral_weight
        self.gaussian_weight = args.gaussian_weight
        self.bilateral_spatial_sigma = args.bilateral_spatial_sigma
        self.bilateral_color_sigma = args.bilateral_color_sigma
        self.gaussian_sigma = args.gaussian_sigma

        self.model_path = args.base_save_path + '/IG-SLAM-CRF'
        print('Models would be saved at : {}\n'.format(self.model_path))
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))

        if torch.cuda.is_available():
            self.device = torch.device(device='cuda')
        else:
            self.device = torch.device(device='cpu')

        # Initialize model based on version
        if self.model_version == 'v1':
            self.model = GDNetCRF(
                backbone_path=backbone_path,
                crf_iter=self.crf_iter,
                trainable_crf=self.trainable_crf
            )
            # Update CRF parameters in the model
            if hasattr(self.model, 'crf') and hasattr(self.model.crf, 'bilateral_weight'):
                self.model.crf.bilateral_weight = nn.Parameter(torch.tensor(self.bilateral_weight)) if self.trainable_crf else self.bilateral_weight
                self.model.crf.gaussian_weight = nn.Parameter(torch.tensor(self.gaussian_weight)) if self.trainable_crf else self.gaussian_weight
                self.model.crf.bilateral_spatial_sigma = nn.Parameter(torch.tensor(self.bilateral_spatial_sigma)) if self.trainable_crf else self.bilateral_spatial_sigma
                self.model.crf.bilateral_color_sigma = nn.Parameter(torch.tensor(self.bilateral_color_sigma)) if self.trainable_crf else self.bilateral_color_sigma
                self.model.crf.gaussian_sigma = nn.Parameter(torch.tensor(self.gaussian_sigma)) if self.trainable_crf else self.gaussian_sigma
        else:  # v2
            self.model = GDNetCRFv2(
                backbone_path=backbone_path,
                crf_iter=self.crf_iter,
                trainable_crf=self.trainable_crf
            )
            # Update CRF parameters in the model
            if hasattr(self.model, 'crf') and hasattr(self.model.crf, 'bilateral_weight'):
                self.model.crf.bilateral_weight = nn.Parameter(torch.tensor(self.bilateral_weight)) if self.trainable_crf else self.bilateral_weight
                self.model.crf.gaussian_weight = nn.Parameter(torch.tensor(self.gaussian_weight)) if self.trainable_crf else self.gaussian_weight
                self.model.crf.bilateral_spatial_sigma = nn.Parameter(torch.tensor(self.bilateral_spatial_sigma)) if self.trainable_crf else self.bilateral_spatial_sigma
                self.model.crf.bilateral_color_sigma = nn.Parameter(torch.tensor(self.bilateral_color_sigma)) if self.trainable_crf else self.bilateral_color_sigma
                self.model.crf.gaussian_sigma = nn.Parameter(torch.tensor(self.gaussian_sigma)) if self.trainable_crf else self.gaussian_sigma

        self.model.to(self.device)

        # Initialize loss functions
        self.edge_loss = EdgeSaliencyLoss(device=self.device)
        self.iou_loss = IOU()
        self.focal_loss = FocalLoss(alpha=self.focal_alpha, gamma=self.focal_gamma)
        self.BCE_loss = BCE()

        # Initialize optimizer
        self.optimizer = optim.SGD(self.model.parameters(), lr=self.lr, momentum=0.9, weight_decay=self.wd)

        # Load model and optimizer if resumed
        ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
        exp_name = 'IG-SLAM'
        args = {
            'snapshot': 'crf-100',
            'scale': 416,
            'crf': False
        }

        if self.res_mod_path is not None:
            chkpt = os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth')
            print(f"Loading checkpoint from: {chkpt}")

            # Load base GDNet weights
            if args['snapshot'] != '200':
                base_weights = torch.load(chkpt)['model']
            else:
                base_weights = torch.load(os.path.join(chkpt))

            # Initialize the base GDNet part of our model
            if self.model_version == 'v1':
                # For v1, we need to load weights into the gdnet submodule
                gdnet_state_dict = self.model.gdnet.state_dict()

                # Filter out keys that don't match
                pretrained_dict = {k: v for k, v in base_weights.items() if k in gdnet_state_dict}
                gdnet_state_dict.update(pretrained_dict)
                self.model.gdnet.load_state_dict(gdnet_state_dict)
            else:  # v2
                # For v2, we need to load weights directly
                model_state_dict = self.model.state_dict()

                # Create a new state dict with matching keys
                new_state_dict = {}
                for k, v in base_weights.items():
                    if k in model_state_dict:
                        new_state_dict[k] = v

                # Update model state dict
                model_state_dict.update(new_state_dict)
                self.model.load_state_dict(model_state_dict, strict=False)

            print("Loaded base GDNet weights successfully")

        if self.res_opt_path is not None:
            chkpt = torch.load(self.res_opt_path, map_location=self.device)
            self.optimizer.load_state_dict(chkpt['optimizer'])
            print("Resuming training with optimizer : {}\n".format(self.res_opt_path))

    def train(self):
        best_test_mae = float('inf')
        early_stopping = EarlyStopping(patience=5, min_delta=0.001)
        running_loss = 0.0

        # Setup data loaders
        if self.use_kfold:
            # K-fold cross validation
            k_range = range(5)
        else:
            # Single fold
            k_range = range(1)

        for k in k_range:
            if self.use_kfold:
                print(f'K-Fold Training: Fold {k+1}')
                self.train_data = FODLoader(mode='train', augment_data=True, target_size=self.img_size, k=k)
                self.test_data = FODLoader(mode='test', augment_data=False, target_size=self.img_size, k=k)
            else:
                self.train_data = SODLoader(mode='train_gdd', augment_data=True, target_size=self.img_size)
                self.test_data = SODLoader(mode='test_gdd', augment_data=False, target_size=self.img_size)

            self.train_dataloader = DataLoader(self.train_data, batch_size=self.bs, shuffle=True, num_workers=self.n_worker)
            self.test_dataloader = DataLoader(self.test_data, batch_size=self.bs, shuffle=False, num_workers=self.n_worker)

            epoch_pbar = tqdm(range(self.epochs), desc="Epochs")
            for epoch in epoch_pbar:
                epoch_loss = 0.0
                self.model.train()

                pbar = tqdm(enumerate(self.train_dataloader),
                           total=len(self.train_dataloader),
                           desc=f"Epoch {epoch+1}/{self.epochs}")
                for batch_idx, (inp_imgs, gt_masks) in pbar:
                    inp_imgs = inp_imgs.to(self.device)
                    gt_masks = gt_masks.to(self.device)

                    self.optimizer.zero_grad()

                    # Forward pass
                    if self.model_version == 'v1':
                        h_predict, l_predict, final_predict, crf_predict = self.model(inp_imgs)
                    else:  # v2
                        h_predict, l_predict, final_predict, crf_predict = self.model(inp_imgs)

                    # Calculate losses
                    # loss_h = self.focal_loss(h_predict, gt_masks) + self.iou_loss(h_predict, gt_masks)
                    # loss_l = self.focal_loss(l_predict, gt_masks) + self.edge_loss(l_predict, gt_masks)
                    # loss_f = self.focal_loss(final_predict, gt_masks) + self.edge_loss(final_predict, gt_masks) + self.iou_loss(final_predict, gt_masks)
                    # loss_crf = self.focal_loss(crf_predict, gt_masks) + self.edge_loss(crf_predict, gt_masks) + self.iou_loss(crf_predict, gt_masks)
                    loss_h = self.focal_loss(h_predict, gt_masks) + self.iou_loss(h_predict, gt_masks)
                    loss_l = self.focal_loss(l_predict, gt_masks) + self.edge_loss(l_predict, gt_masks)
                    loss_f = self.focal_loss(final_predict, gt_masks) + self.edge_loss(final_predict, gt_masks) + self.iou_loss(final_predict, gt_masks)
                    loss_crf = self.focal_loss(crf_predict, gt_masks) + self.edge_loss(crf_predict, gt_masks) + self.iou_loss(crf_predict, gt_masks)

                    # Combine losses
                    loss = loss_h + loss_l + loss_f + self.crf_weight * loss_crf

                    loss_value = loss.item()
                    running_loss += loss_value
                    epoch_loss += loss_value

                    # Update progress bar with loss values
                    pbar.set_postfix({
                        'loss': f'{loss_value:.4f}',
                        'h_loss': f'{loss_h.item():.4f}',
                        'l_loss': f'{loss_l.item():.4f}',
                        'f_loss': f'{loss_f.item():.4f}',
                        'crf_loss': f'{loss_crf.item():.4f}'
                    })

                    # Backward pass
                    loss.backward()
                    self.optimizer.step()

                    if batch_idx % self.log_interval == 0:
                        print('TRAIN :: Epoch : {}\tBatch : {}/{} ({:.2f}%)\t\tTot Loss : {:.4f}'
                              .format(epoch + 1,
                                      batch_idx + 1, len(self.train_dataloader),
                                      (batch_idx + 1) * 100 / len(self.train_dataloader),
                                      running_loss / self.log_interval))

                        writer_train.add_scalar('Training Loss (batch)',
                                               running_loss / self.log_interval,
                                               epoch * len(self.train_dataloader) + batch_idx)
                        running_loss = 0.0

                # Log epoch loss
                epoch_avg_loss = epoch_loss / len(self.train_dataloader)
                writer_train.add_scalar('Loss', epoch_avg_loss, epoch)

                # Update epoch progress bar with average loss
                epoch_pbar.set_postfix({'avg_loss': f'{epoch_avg_loss:.4f}'})

                # Validation
                if epoch % self.test_interval == 0 or epoch % self.save_interval == 0:
                    te_avg_loss, te_acc, te_pre, te_rec, te_mae, te_iou = self.test()

                    mod_chkpt = {
                        'epoch': epoch,
                        'test_mae': float(te_mae),
                        'model': self.model.state_dict(),
                        'test_loss': float(te_avg_loss),
                        'test_acc': float(te_acc),
                        'test_pre': float(te_pre),
                        'test_rec': float(te_rec),
                        'test_iou': float(te_iou)
                    }

                    if early_stopping.step(te_avg_loss):
                        print(f"Early stopping at epoch {epoch}")
                        break  # 触发早停

                    if self.save_opt:
                        opt_chkpt = {
                            'epoch': epoch,
                            'test_mae': float(te_mae),
                            'optimizer': self.optimizer.state_dict(),
                            'test_loss': float(te_avg_loss),
                            'test_acc': float(te_acc),
                            'test_pre': float(te_pre),
                            'test_rec': float(te_rec),
                            'test_iou': float(te_iou)
                        }

                    writer_vad.add_scalar('Loss', te_avg_loss, epoch)
                    writer_vad.add_scalar('Validation mIoU', te_iou, epoch)

                    # Save the best model
                    if te_mae < best_test_mae:
                        best_test_mae = te_mae
                        torch.save(mod_chkpt,
                                  f'{self.model_path}/weights/best-model_epoch-{epoch:03d}_mae-{best_test_mae:.4f}_loss-{te_avg_loss:.4f}.pth')

                        if self.save_opt:
                            torch.save(opt_chkpt,
                                      f'{self.model_path}/optimizers/best-opt_epoch-{epoch:03d}_mae-{best_test_mae:.4f}_loss-{te_avg_loss:.4f}.pth')

                        print('Best Model Saved !!!\n')
                        continue

                    # Save model at regular intervals
                    if self.save_interval is not None and epoch % self.save_interval == 0:
                        torch.save(mod_chkpt,
                                  f'{self.model_path}/weights/model_epoch-{epoch:03d}_mae-{te_mae:.4f}_loss-{te_avg_loss:.4f}.pth')

                        if self.save_opt:
                            torch.save(opt_chkpt,
                                      f'{self.model_path}/optimizers/opt_epoch-{epoch:03d}_mae-{best_test_mae:.4f}_loss-{te_avg_loss:.4f}.pth')

                        print('Model Saved !!!\n')
                        continue

                print('\n')

        # Save model parameter histograms
        for name, param in self.model.named_parameters():
            writer_train.add_histogram(name, param, epoch)

        # Close writers
        writer_train.close()
        writer_vad.close()

    def test(self):
        self.model.eval()

        tot_loss = 0
        tp_fp = 0   # TruePositive + TrueNegative, for accuracy
        tp = 0      # TruePositive
        pred_true = 0   # Number of '1' predictions, for precision
        gt_true = 0     # Number of '1's in gt mask, for recall
        mae_list = []   # List to save mean absolute error of each image
        iou_list = []
        total_pixels = 0  # Total number of pixels across all test images
        acc_list = []    # List to save accuracy of each image

        with torch.no_grad():
            test_pbar = tqdm(enumerate(self.test_dataloader, start=1),
                            total=len(self.test_dataloader),
                            desc="Testing")
            for batch_idx, (inp_imgs, gt_masks) in test_pbar:
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)

                # Accumulate total pixel count
                total_pixels += gt_masks.numel()

                # Forward pass
                if self.model_version == 'v1':
                    h_predict, l_predict, final_predict, crf_predict = self.model(inp_imgs)
                else:  # v2
                    h_predict, l_predict, final_predict, crf_predict = self.model(inp_imgs)

                # Calculate losses
                loss_h = self.focal_loss(h_predict, gt_masks) + self.iou_loss(h_predict, gt_masks)
                loss_l = self.focal_loss(l_predict, gt_masks) + self.edge_loss(l_predict, gt_masks)
                loss_f = self.focal_loss(final_predict, gt_masks) + self.edge_loss(final_predict, gt_masks) + self.iou_loss(final_predict, gt_masks)
                loss_crf = self.focal_loss(crf_predict, gt_masks) + self.edge_loss(crf_predict, gt_masks) + self.iou_loss(crf_predict, gt_masks)

                # Combine losses
                loss = loss_h + loss_l + loss_f + self.crf_weight * loss_crf

                # Use CRF prediction for evaluation
                pred_masks = crf_predict

                loss_value = loss.item()
                tot_loss += loss_value

                # Calculate current metrics
                current_avg_loss = tot_loss / batch_idx
                current_mae = np.mean(mae_list) if mae_list else 0
                current_iou = np.mean(iou_list) if iou_list else 0

                # Update progress bar with loss values and current metrics
                test_pbar.set_postfix({
                    'loss': f'{loss_value:.4f}',
                    'avg_loss': f'{current_avg_loss:.4f}',
                    'mae': f'{current_mae:.4f}',
                    'iou': f'{current_iou:.4f}'
                })

                # Convert to numpy for consistent evaluation
                pred_np = pred_masks.detach().cpu().numpy()
                gt_np = gt_masks.detach().cpu().numpy()

                # Calculate metrics for each image in batch
                for b in range(pred_np.shape[0]):
                    # Get single image prediction and ground truth
                    pred_single = pred_np[b, 0]  # Shape: H x W
                    gt_single = gt_np[b, 0]      # Shape: H x W

                    # Binary prediction (threshold at 0.5)
                    pred_binary = (pred_single > 0.5).astype(np.float32)

                    # Calculate TP, TN, FP, FN
                    TP = np.sum(np.logical_and(pred_binary, gt_single))
                    TN = np.sum(np.logical_and(np.logical_not(pred_binary), np.logical_not(gt_single)))

                    # Calculate accuracy
                    N_p = np.sum(gt_single)
                    N_n = np.sum(np.logical_not(gt_single))
                    acc = (TP + TN) / (N_p + N_n)

                    # Calculate IoU
                    intersection = TP
                    union = np.sum(pred_binary) + np.sum(gt_single) - intersection
                    img_iou = intersection / union if union > 0 else 0.0

                    # Accumulate metrics
                    tp_fp += TP + TN
                    tp += TP
                    pred_true += np.sum(pred_binary)
                    gt_true += N_p
                    iou_list.append(img_iou)
                    acc_list.append(acc)

                    # Add to total pixels count
                    total_pixels += (N_p + N_n)

                # Record the absolute errors
                ae = torch.mean(torch.abs(pred_masks - gt_masks), dim=(1, 2, 3)).cpu().numpy()
                mae_list.extend(ae)

        # Calculate final metrics
        avg_loss = tot_loss / batch_idx

        # Calculate accuracy in two ways for comparison
        pixel_accuracy = tp_fp / total_pixels if total_pixels > 0 else 0
        image_accuracy = np.mean(acc_list) if acc_list else 0

        # Use image-wise accuracy to be consistent with test.py
        accuracy = image_accuracy

        precision = tp / pred_true if pred_true > 0 else 0
        recall = tp / gt_true if gt_true > 0 else 0
        mae = np.mean(mae_list)
        iou = np.mean(iou_list)

        # Print both accuracy metrics for comparison
        print(f'TEST :: Pixel-wise ACC: {pixel_accuracy:.4f}, Image-wise ACC: {image_accuracy:.4f}')

        print('TEST :: MAE : {:.4f}\tACC : {:.4f}\tPRE : {:.4f}\tREC : {:.4f}\tAVG-LOSS : {:.4f}\tMIOU : {:.4f}\n'.format(
            mae, accuracy, precision, recall, avg_loss, iou))

        return avg_loss, accuracy, precision, recall, mae, iou


if __name__ == '__main__':
    rt_args = parse_arguments()

    # Driver class
    trainer = Engine(rt_args)
    trainer.train()
