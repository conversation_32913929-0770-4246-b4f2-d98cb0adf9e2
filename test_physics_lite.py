"""
轻量化Physics模型测试脚本
目标: 验证是否能从85.63%提升到87.5%+
使用glass_dataloader进行专门的玻璃感知数据处理
"""
import os
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import *
from ig_glass.gdnet_physics_lite import create_physics_lite_model
from ig_glass.glass_dataloader import create_glass_dataloaders, GlassInferenceLoader

# Device setup
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# Paths
backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth'

# Parameter set - 使用glass_dataloader的推荐设置
args = {
    'scale': 416,  # 提升到416px，适配ViT-Base和玻璃检测
    'glass_threshold': 0.5,
    'crf_iter': 3,
    'batch_size': 8,
    'num_workers': 4
}

# 使用glass_dataloader的标准化参数
normalize = transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
to_pil = transforms.ToPILImage()


def path_set(tempdata_path, new_path):
    results_root = os.path.join(tempdata_path, new_path)
    return results_root


def test_with_glass_dataloader(model, mode='test_gdd'):
    """使用glass_dataloader进行批量测试"""
    print(f"🔍 使用Glass DataLoader测试模型 ({mode})")
    
    # 创建测试数据加载器
    test_loader = create_glass_dataloaders(
        mode=mode,
        batch_size=args['batch_size'],
        num_workers=args['num_workers'], 
        target_size=args['scale'],
        augment_data=False,  # 测试时不使用数据增强
        glass_augmentation='conservative'  # 保守的增强设置
    )
    
    print(f"📊 测试数据集: {len(test_loader.dataset)}张图像, {len(test_loader)}个批次")
    
    model.eval()
    total_iou, total_acc, total_fm = 0, 0, 0
    total_mae, total_ber, total_aber = 0, 0, 0
    total_samples = 0
    
    # 性能测量
    inference_times = []
    
    with torch.no_grad():
        for batch_idx, (images, masks) in enumerate(test_loader):
            # 移动到GPU
            images = images.cuda(device_ids[0])
            masks = masks.cuda(device_ids[0])
            
            # 测量推理时间
            start_time = torch.cuda.Event(enable_timing=True)
            end_time = torch.cuda.Event(enable_timing=True)
            
            start_time.record()
            outputs = model(images)
            end_time.record()
            
            torch.cuda.synchronize()
            batch_time = start_time.elapsed_time(end_time) / 1000.0
            inference_times.append(batch_time / images.size(0))  # 每张图像的时间
            
            # 获取预测结果
            if isinstance(outputs, dict):
                if 'refined_pred' in outputs and outputs['refined_pred'].size(1) > 1:
                    predictions = outputs['refined_pred'][:, 1:2]  # CRF输出的前景概率
                else:
                    predictions = outputs['ensemble_pred']
            else:
                predictions = outputs
            
            # 批量计算评估指标
            batch_size = images.size(0)
            predictions_np = predictions.cpu().numpy()
            masks_np = masks.cpu().numpy()
            
            for i in range(batch_size):
                pred = predictions_np[i, 0]  # 单通道预测
                gt = masks_np[i, 0]  # 单通道真值
                
                # 计算各项指标
                iou = compute_iou(pred, gt)
                acc = compute_acc(pred, gt)
                precision, recall = compute_precision_recall(pred, gt)
                fm = compute_fmeasure(precision, recall)
                mae = compute_mae(pred, gt)
                ber = compute_ber(pred, gt)
                aber = compute_aber(pred, gt)
                
                total_iou += iou
                total_acc += acc
                total_fm += fm
                total_mae += mae
                total_ber += ber
                total_aber += aber
                total_samples += 1
            
            # 显示进度
            if (batch_idx + 1) % 10 == 0 or (batch_idx + 1) == len(test_loader):
                current_iou = total_iou / total_samples
                print(f"  批次 {batch_idx+1}/{len(test_loader)} - 当前平均IoU: {current_iou:.4f}")
    
    # 计算最终结果
    if total_samples > 0:
        avg_iou = total_iou / total_samples
        avg_acc = total_acc / total_samples
        avg_fm = total_fm / total_samples
        avg_mae = total_mae / total_samples
        avg_ber = total_ber / total_samples
        avg_aber = total_aber / total_samples
        
        # 计算性能指标
        if len(inference_times) > 5:
            avg_inference_time = np.mean(inference_times[5:])  # 跳过前几次预热
            fps = 1.0 / avg_inference_time
        else:
            avg_inference_time = np.mean(inference_times)
            fps = 1.0 / avg_inference_time
        
        # 数值健康检查
        if avg_iou > 1.0:
            print(f"⚠️ 检测到异常IoU值: {avg_iou:.4f}, 可能的原因:")
            print(f"  - 模型未经训练（使用随机权重）")
            print(f"  - 评估函数计算错误")
            print(f"  - 数据预处理问题")
            avg_iou = np.clip(avg_iou, 0.0, 1.0)  # 强制限制在合理范围内
        
        print(f"\n📊 Glass DataLoader测试结果:")
        print(f"  IoU: {avg_iou:.4f} ({avg_iou*100:.2f}%)")
        print(f"  Accuracy: {avg_acc:.4f}")
        print(f"  F-measure: {avg_fm:.4f}")
        print(f"  MAE: {avg_mae:.4f}")
        print(f"  BER: {avg_ber:.4f}")
        print(f"  ABER: {avg_aber:.4f}")
        print(f"  推理时间: {avg_inference_time*1000:.2f} ms/图像")
        print(f"  FPS: {fps:.2f}")
        
        # 模型状态提醒
        print(f"\n⚠️ 重要提醒: 当前测试使用的是未经训练的轻量化模型（随机权重）")
        print(f"🎯 要获得准确的性能评估，需要:")
        print(f"  1️⃣ 使用Glass DataLoader训练轻量化模型")
        print(f"  2️⃣ 加载训练好的检查点进行测试")
        print(f"  3️⃣ 对比训练前后的性能差异")
        
        return {
            'iou': avg_iou,
            'acc': avg_acc,
            'fm': avg_fm,
            'mae': avg_mae,
            'ber': avg_ber,
            'aber': avg_aber,
            'fps': fps
        }
    
    return None


def detect_glass_and_evaluate(image_folder, output_folder, gt_folder, model, glass_threshold):
    """检测玻璃区域并评估结果 - 兼容原有的单图像测试"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # 使用GlassInferenceLoader进行推理
    inference_loader = GlassInferenceLoader(
        img_folder=image_folder,
        target_size=args['scale']
    )
    
    print(f"📸 使用Glass推理加载器处理 {len(inference_loader)} 张图像")
    
    count = 0
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0
    inference_times = []

    for idx in range(len(inference_loader)):
        try:
            # 获取预处理后的数据
            img_resized, img_tensor, img_path = inference_loader[idx]
            
            # 获取文件名
            image_file = os.path.basename(img_path)
            
            # 推理
            img_var = Variable(img_tensor.unsqueeze(0)).cuda(device_ids[0])
            
            # 测量推理时间
            start_time = torch.cuda.Event(enable_timing=True)
            end_time = torch.cuda.Event(enable_timing=True)

            start_time.record()
            with torch.no_grad():
                outputs = model(img_var)
                # 使用CRF精炼后的预测结果
                if isinstance(outputs, dict):
                    if 'refined_pred' in outputs and outputs['refined_pred'].size(1) > 1:
                        refined_pred = outputs['refined_pred']
                        # 从CRF输出中提取前景概率（通道1）
                        if refined_pred.size(1) == 2:
                            crf_predict = refined_pred[:, 1:2]
                        else:
                            crf_predict = refined_pred
                    else:
                        crf_predict = outputs['ensemble_pred']
                else:
                    crf_predict = outputs
            end_time.record()

            # 等待GPU操作完成
            torch.cuda.synchronize()
            inference_time = start_time.elapsed_time(end_time) / 1000.0  # 转换为秒
            inference_times.append(inference_time)

            # 转换预测结果
            crf_predict = crf_predict.data.squeeze(0).cpu()
            
            # 获取原始图像尺寸进行还原
            original_img = Image.open(img_path)
            w, h = original_img.size
            
            crf_predict_np = np.array(transforms.Resize((h, w))(to_pil(crf_predict)))

            # 二值化预测结果
            glass_mask = (crf_predict_np > glass_threshold * 255).astype(np.uint8) * 255

            # 保存结果
            temp_name = image_file.split('.')[0]
            if '.png' in image_file:
                result_name = image_file
            else:
                result_name = temp_name + '.png'
            cv2.imwrite(os.path.join(output_folder, result_name), glass_mask)

            # 评估结果
            if '.png' in image_file:
                gt_name = image_file
            else:
                gt_name = temp_name + '.png'
            gt_path = os.path.join(gt_folder, gt_name)

            if os.path.exists(gt_path):
                gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)

                # 确保尺寸一致
                if gt_mask.shape != glass_mask.shape:
                    gt_mask = cv2.resize(gt_mask, (glass_mask.shape[1], glass_mask.shape[0]))

                # 计算评估指标
                prediction = glass_mask.astype(np.float32) / 255.0
                gt = gt_mask.astype(np.float32) / 255.0

                tiou = compute_iou(prediction, gt)
                tacc = compute_acc(prediction, gt)
                precision, recall = compute_precision_recall(prediction, gt)
                tfm = compute_fmeasure(precision, recall)
                tmae = compute_mae(prediction, gt)
                tber = compute_ber(prediction, gt)
                taber = compute_aber(prediction, gt)

                count += 1
                iou += tiou
                acc += tacc
                fm += tfm
                mae += tmae
                ber += tber
                aber += taber

        except Exception as e:
            print(f"❌ 处理图像 {image_file} 时出错: {e}")
            continue

    if count > 0:
        iou = iou / count
        acc = acc / count
        fm = fm / count
        mae = mae / count
        ber = ber / count
        aber = aber / count

    # 计算平均推理时间（跳过前几次作为预热）
    if len(inference_times) > 5:
        avg_inference_time = np.mean(inference_times[5:])
        fps = 1.0 / avg_inference_time
        print(f"\nAverage inference time: {avg_inference_time*1000:.2f} ms")
        print(f"FPS: {fps:.2f}")

    return iou, acc, fm, mae, ber, aber


def test_lite_model_comparison():
    """测试轻量化模型与原始模型的对比"""
    print("🚀 开始测试轻量化Physics模型...")
    
    # 创建轻量化模型 (目前没有训练权重，先用随机初始化测试架构)
    model = create_physics_lite_model(
        backbone_path=backbone_path,  # 使用预训练backbone
        crf_iter=args['crf_iter'], 
        trainable_crf=False
    )
    
    model.cuda(device_ids[0])
    model.eval()
    
    print("✅ 轻量化Physics模型创建成功")
    print(f"📊 模型参数数量: {sum(p.numel() for p in model.parameters())/1e6:.2f}M")
    
    # 测试glass_dataloader的兼容性
    print("\n🔧 测试Glass DataLoader兼容性...")
    try:
        # 创建测试数据加载器
        test_loader = create_glass_dataloaders(
            mode='test_gdd',
            batch_size=2,  # 小批次测试
            target_size=args['scale'],
            augment_data=False
        )
        
        # 测试一个批次
        for images, masks in test_loader:
            images = images.cuda(device_ids[0])
            with torch.no_grad():
                outputs = model(images)
            
            print(f"✅ Glass DataLoader兼容性测试成功")
            print(f"  输入形状: {images.shape}")
            print(f"  掩码形状: {masks.shape}")
            if isinstance(outputs, dict):
                for key, value in outputs.items():
                    if isinstance(value, torch.Tensor):
                        print(f"  输出 {key}: {value.shape}")
            break
            
    except Exception as e:
        print(f"❌ Glass DataLoader兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    return model


def test_trained_model_with_glass_dataloader():
    """使用已训练的SCSA模型测试Glass DataLoader的效果"""
    print("\n🔍 使用已训练SCSA模型验证Glass DataLoader效果...")
    
    # 检查SCSA模型检查点
    scsa_checkpoint_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/scsa/scsa_best.pth'
    if not os.path.exists(scsa_checkpoint_path):
        print(f"❌ 未找到SCSA模型检查点: {scsa_checkpoint_path}")
        print(f"🔍 搜索可用的SCSA模型...")
        
        # 搜索SCSA模型目录
        scsa_dir = '/home/<USER>/ws/IG_SLAM/ig_glass/models_scsa'
        if os.path.exists(scsa_dir):
            scsa_files = [f for f in os.listdir(scsa_dir) if f.endswith('.pth')]
            if scsa_files:
                scsa_checkpoint_path = os.path.join(scsa_dir, scsa_files[0])
                print(f"✅ 找到SCSA模型: {scsa_checkpoint_path}")
            else:
                print(f"❌ 未找到任何SCSA模型文件")
                return None
        else:
            print(f"❌ SCSA模型目录不存在")
            return None
    
    try:
        # 导入SCSA模型
        from ig_glass.gdnet_scsa import GDNet_SCSA
        
        # 创建SCSA模型
        scsa_model = GDNet_SCSA(
            backbone_path=backbone_path,
            crf_iter=args['crf_iter'],
            trainable_crf=False
        )
        
        # 加载训练权重
        print(f"📥 加载SCSA模型权重: {scsa_checkpoint_path}")
        checkpoint = torch.load(scsa_checkpoint_path, map_location='cpu')
        if 'model_state_dict' in checkpoint:
            scsa_model.load_state_dict(checkpoint['model_state_dict'])
        else:
            scsa_model.load_state_dict(checkpoint)
        
        scsa_model.cuda(device_ids[0])
        scsa_model.eval()
        
        print("✅ SCSA模型加载成功")
        
        # 对比测试：传统DataLoader vs Glass DataLoader
        print("\n📊 开始对比测试...")
        
        # 1. 测试Glass DataLoader (416px)
        print("🔍 测试1: SCSA + Glass DataLoader (416px)")
        glass_results = test_with_glass_dataloader(scsa_model, mode='test_gdd')
        
        # 2. 测试传统的单图像处理方式 (会在后续添加)
        print("\n🔍 测试2: 传统处理方式对比")
        print("  (需要实现传统256px分辨率的批量测试)")
        
        if glass_results:
            print(f"\n📈 Glass DataLoader测试结果:")
            print(f"  IoU: {glass_results['iou']*100:.2f}%")
            print(f"  FPS: {glass_results['fps']:.2f}")
            print(f"  分辨率: 416x416")
            print(f"  增强方式: 玻璃感知增强")
            
            return glass_results
        
    except ImportError as e:
        print(f"❌ 导入SCSA模型失败: {e}")
        print("🔍 请确保gdnet_scsa.py文件存在且可导入")
        return None
    except Exception as e:
        print(f"❌ 加载SCSA模型失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    return None


def main():
    """主函数"""
    print("=" * 80) 
    print("🔍 轻量化Physics模型 + Glass DataLoader 测试")
    print("=" * 80)
    
    # 测试模型架构和数据加载器兼容性
    model = test_lite_model_comparison()
    if model is None:
        print("❌ 模型测试失败，退出")
        return
    
    print("\n📊 Glass DataLoader vs 传统DataLoader 对比:")
    comparisons = [
        "🆚 分辨率: 416px vs 256px (提升63%)",
        "🆚 增强策略: 玻璃感知 vs 通用增强",
        "🆚 物理特性: 透明度+反射+折射 vs 基础几何变换",
        "🆚 光照模拟: 环境反射+不均匀光照 vs 简单亮度调整",
        "🆚 数据处理: 批量优化+推理专用 vs 单图处理"
    ]
    
    for comparison in comparisons:
        print(f"  {comparison}")
    
    print(f"\n💡 Glass DataLoader的关键优势:")
    advantages = [
        "🎯 专门针对玻璃的透明度、反射特性设计增强策略",
        "🔄 保持玻璃区域的物理特征完整性", 
        "⚡ 416px高分辨率，能够捕获更多玻璃边缘细节",
        "🌟 环境反射模拟，增强模型对真实场景的适应性",
        "🚀 批量处理优化，提升训练和测试效率"
    ]
    
    for advantage in advantages:
        print(f"  {advantage}")
    
    # 如果要进行完整测试，取消注释下面的代码
    print(f"\n🎯 下一步测试计划:")
    next_steps = [
        "1️⃣ 使用Glass DataLoader训练轻量化Physics模型",
        "2️⃣ 对比测试 416px vs 256px 分辨率的性能提升", 
        "3️⃣ 验证玻璃感知增强对IoU的改善效果",
        "4️⃣ 目标: 从85.63%提升到87.5%+",
        "5️⃣ 如果成功，继续优化；否则考虑ViT backbone"
    ]
    
    for step in next_steps:
        print(f"  {step}")
    
    # 测试未训练的轻量化模型（用于验证架构）
    print(f"\n🚀 开始Glass DataLoader架构验证测试...")
    results = test_with_glass_dataloader(model, mode='test_gdd')
    if results:
        print(f"\n🎯 架构验证完成! (注意：使用随机权重)")
        
        # 与SCSA模型基准对比
        scsa_iou = 0.8795  # SCSA模型的IoU基准
        physics_iou = 0.8563  # 原Physics模型的IoU基准
        
        print(f"\n📊 性能对比 (仅供参考，模型未训练):")
        print(f"  轻量化Physics + Glass DataLoader: {results['iou']*100:.2f}%")
        print(f"  原Physics模型: {physics_iou*100:.2f}%")
        print(f"  SCSA模型基准: {scsa_iou*100:.2f}%")
    else:
        print("❌ 架构验证失败")
    
    # 使用已训练模型进行真实对比
    print(f"\n" + "="*50)
    print("🎯 真实性能测试 (使用已训练模型)")
    print("="*50)
    
    trained_results = test_trained_model_with_glass_dataloader()
    if trained_results:
        print(f"\n✅ 已训练模型 + Glass DataLoader 测试成功!")
    else:
        print(f"\n⚠️ 已训练模型测试跳过（模型文件不可用）")
    
    print("\n" + "=" * 80)
    print("💡 建议: Glass DataLoader的玻璃感知特性预期能显著提升检测精度")
    print("🎯 关键改进: 更高分辨率 + 物理特性感知 + 环境模拟")
    print("🔥 下一步: 使用Glass DataLoader训练轻量化Physics模型")
    print("=" * 80)


if __name__ == '__main__':
    main() 