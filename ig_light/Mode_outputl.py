import cv2
import os
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline

# Step 1: Calculate image brightness
def calculate_brightness(image_path):
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])  # V channel in HSV represents brightness
    return brightness

# Step 2: Calculate ORB-SLAM3 feature points
def calculate_feature_points(image_path):
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    orb = cv2.ORB_create(nfeatures=20000)  # Increase the maximum number of features to detect
    keypoints = orb.detect(image, None)
    return keypoints, len(keypoints)


# Process images to get brightness and feature points, and save images
def process_images(image_folder, output_folder):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    brightness_list = []
    feature_points_list = []

    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)

        # Calculate brightness
        brightness = calculate_brightness(image_path)
        brightness_list.append((image_file, brightness))

        # Calculate feature points
        keypoints, feature_points = calculate_feature_points(image_path)
        feature_points_list.append((image_file, feature_points))

        # Read image in color to draw on it
        image = cv2.imread(image_path)

        # Draw keypoints on image
        image_with_keypoints = cv2.drawKeypoints(image, keypoints, None, color=(0, 255, 0))

        # Put text for brightness and feature points
        cv2.putText(image_with_keypoints, f'Brightness: {brightness:.2f}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1,
                    (255, 255, 255), 2)
        cv2.putText(image_with_keypoints, f'Feature Points: {feature_points}', (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1,
                    (255, 255, 255), 2)

        # Save the image to the output folder
        output_path = os.path.join(output_folder, image_file)
        cv2.imwrite(output_path, image_with_keypoints)

        # Display the image with brightness and feature points
        # (Optional, can be commented out to avoid blocking execution)
        # cv2.imshow('Image with Brightness and Feature Points', image_with_keypoints)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()

    return brightness_list, feature_points_list


# Paths
image_folder = r"/home/<USER>/ORB_SLAM3-ws/src/ORB_SLAM3/Datasets/Light/mav0/cam0/task"
output_folder = r"/home/<USER>/ORB_SLAM3-ws/src/ORB_SLAM3/Datasets/Light/mav0/cam0/task_brightness"

# Process images
brightness_list, feature_points_list = process_images(image_folder, output_folder)

# Step 2: Combine brightness and feature points data
image_data = {}
for (file, brightness) in brightness_list:
    image_data[file] = {'brightness': brightness}

for (file, feature_points) in feature_points_list:
    if file in image_data:
        image_data[file]['feature_points'] = feature_points

# Prepare data for plotting and modeling
brightness_values = [data['brightness'] for data in image_data.values() if 'feature_points' in data]
feature_points_values = [data['feature_points'] for data in image_data.values() if 'feature_points' in data]

# Step 3: Model the data and plot the curve
linear_model = LinearRegression()
linear_model.fit(np.array(brightness_values).reshape(-1, 1), feature_points_values)

poly_model = make_pipeline(PolynomialFeatures(degree=2), LinearRegression())
poly_model.fit(np.array(brightness_values).reshape(-1, 1), feature_points_values)

brightness_range = np.linspace(min(brightness_values), max(brightness_values), 100).reshape(-1, 1)
linear_predictions = linear_model.predict(brightness_range)
poly_predictions = poly_model.predict(brightness_range)

plt.scatter(brightness_values, feature_points_values, color='blue', label='Actual Data')
plt.plot(brightness_range, linear_predictions, color='red', label='Linear Regression')
plt.plot(brightness_range, poly_predictions, color='green', label='Polynomial Regression (degree 2)')
plt.xlabel('Brightness')
plt.ylabel('Feature Points')
plt.title('Feature Points vs Brightness in Glass Environment')
plt.legend()
plt.show()

# Step 4: Find the optimal brightness
max_feature_points_linear = max(linear_predictions)
optimal_brightness_linear = brightness_range[linear_predictions.argmax()][0]

print(f'Optimal brightness (Linear Regression): {optimal_brightness_linear}')
print(f'Maximum feature points (Linear Regression): {max_feature_points_linear}')

max_feature_points_poly = max(poly_predictions)
optimal_brightness_poly = brightness_range[poly_predictions.argmax()][0]

print(f'Optimal brightness (Polynomial Regression): {optimal_brightness_poly}')
print(f'Maximum feature points (Polynomial Regression): {max_feature_points_poly}')

optimal_brightness_poly, max_feature_points_poly = find_optimal_brightness(poly_model, brightness_range)
print(f'Optimal brightness: {optimal_brightness_poly}')
print(f'Maximum feature points: {max_feature_points_poly}')