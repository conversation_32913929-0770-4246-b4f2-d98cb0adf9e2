import cv2
import os
import numpy as np
# 动态亮度调整目标范围
BRIGHTNESS_TARGET_MIN = 100
BRIGHTNESS_TARGET_MAX = 120

def calculate_brightness(image_path):
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])  # V channel in HSV represents brightness
    return brightness


# calculate feature points
def calculate_feature_points(image_path):
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    orb = cv2.ORB_create(nfeatures=2399)  # Increase the maximum number of features to detect
    keypoints = orb.detect(image, None)
    return keypoints, len(keypoints)

def adjust_image_brightness(image, adjustment):
    """根据调整值调整图像亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    v = np.clip(v.astype(np.int32) + int(adjustment), 0, 255).astype(np.uint8)
    hsv = cv2.merge((h, s, v))
    return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)


def adjust_image_brightness_dynamically(image, target_min, target_max):
    """根据图像亮度的分布动态调整亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    v = hsv[:, :, 2]  # 提取亮度通道

    # 计算亮度的直方图
    hist = cv2.calcHist([v], [0], None, [256], [0, 256])
    cumulative_sum = np.cumsum(hist)
    median_brightness = np.searchsorted(cumulative_sum, cumulative_sum[-1] // 2)

    # 动态目标亮度，基于图像的亮度分布
    current_mean = np.mean(v)
    current_std = np.std(v)

    # 根据当前亮度的均值和标准差，动态调整目标亮度
    if current_mean < target_min:
        adjustment = target_min - median_brightness
    elif current_mean > target_max:
        adjustment = target_max - median_brightness
    else:
        adjustment = (target_max + target_min) / 2 - median_brightness

    # 调整图像亮度
    adjusted_image = adjust_image_brightness(image, adjustment)

    return adjusted_image

# get brightness and feature points, and save images
def process_images(image_folder, output_folder):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    brightness_list = []
    feature_points_list = []

    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)

        # # Calculate brightness
        # brightness = calculate_brightness(image_path)
        # brightness_list.append((image_file, brightness))

        # # Calculate feature points
        # keypoints, feature_points = calculate_feature_points(image_path)
        # feature_points_list.append((image_file, feature_points))

        # Read image in color to draw on it
        image = cv2.imread(image_path)
        adjusted_image = adjust_image_brightness_dynamically(image, BRIGHTNESS_TARGET_MIN, BRIGHTNESS_TARGET_MAX)

        # Draw keypoints on image
        #image_with_keypoints = cv2.drawKeypoints(image, keypoints, None, color=(0, 255, 0))

        # Put text for brightness and feature points
        #cv2.putText(image_with_keypoints, f'Brightness: {brightness:.2f}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1,(255, 255, 255), 2)
        #cv2.putText(image_with_keypoints, f'Feature Points: {feature_points}', (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 1,(255, 255, 255), 2)

        # Save the image to the output folder
        #print({brightness:.2f})
        output_path = os.path.join(output_folder, image_file)
        cv2.imwrite(output_path, adjusted_image)

        # Display the image with brightness and feature points
        # (Optional, can be commented out to avoid blocking execution)
        # cv2.imshow('Image with Brightness and Feature Points', image_with_keypoints)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()

    return brightness_list, feature_points_list


# Paths
#image_folder = r"/home/<USER>/orb_slam3_ws/src/SLAM/Light/Task/room"
#output_folder = r"/home/<USER>/orb_slam3_ws/src/SLAM/Light/Task/room_brightness"

# Process images
#brightness_list, feature_points_list = process_images(image_folder, output_folder)
