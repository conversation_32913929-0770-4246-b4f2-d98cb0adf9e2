import cv2
import os
import numpy as np
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense


def calculate_brightness(image_path):
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])  # V channel in HSV represents brightness
    return brightness


def calculate_feature_points(image_path):
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    orb = cv2.ORB_create(nfeatures=10000)  # Increase the maximum number of features to detect
    keypoints = orb.detect(image, None)
    return keypoints, len(keypoints)


def create_brightness_adjustment_model():
    model = Sequential([
        Dense(64, activation='relu', input_shape=(1,)),
        Dense(32, activation='relu'),
        Dense(1)
    ])
    model.compile(optimizer='adam', loss='mse')
    return model


def train_brightness_adjustment_model(brightness_values, feature_points_values):
    model = create_brightness_adjustment_model()
    X = np.array(brightness_values).reshape(-1, 1)
    y = np.array(feature_points_values).reshape(-1, 1)
    model.fit(X, y, epochs=100, verbose=0)
    return model


def adjust_image_brightness(image, adjustment):
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    v = np.clip(v.astype(np.int32) + int(adjustment), 0, 255).astype(np.uint8)
    hsv = cv2.merge((h, s, v))
    return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)


def process_images(image_folder, output_folder, target_brightness):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    brightness_list = []
    feature_points_list = []

    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)

        # Calculate original brightness
        original_brightness = calculate_brightness(image_path)
        brightness_list.append((image_file, original_brightness))

        # Read image in color
        original_image = cv2.imread(image_path)

        # Adjust brightness to target value
        adjustment = target_brightness - original_brightness
        adjusted_image = adjust_image_brightness(original_image, adjustment)

        # Save adjusted image temporarily to calculate feature points
        temp_path = os.path.join(output_folder, 'temp.jpg')
        cv2.imwrite(temp_path, adjusted_image)

        # Calculate feature points of adjusted image
        keypoints, feature_points = calculate_feature_points(temp_path)
        feature_points_list.append((image_file, feature_points))

        # Draw keypoints on adjusted image
        image_with_keypoints = cv2.drawKeypoints(adjusted_image, keypoints, None, color=(0, 255, 0))

        # Put text for brightness and feature points
        cv2.putText(image_with_keypoints, f'Brightness: {target_brightness:.2f}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(image_with_keypoints, f'Feature Points: {feature_points}', (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        # Save the adjusted image with keypoints to the output folder
        output_path = os.path.join(output_folder, image_file)
        cv2.imwrite(output_path, image_with_keypoints)

    # Clean up temporary file
    if os.path.exists(temp_path):
        os.remove(temp_path)

    return brightness_list, feature_points_list


# Paths
image_folder = r"/home/<USER>/orb_slam3_ws/src/SLAM/hku"
output_folder = r"/home/<USER>/orb_slam3_ws/src/SLAM/output"
target_brightness = 116.13324652777777

# Process images
brightness_list, feature_points_list = process_images(image_folder, output_folder, target_brightness)
