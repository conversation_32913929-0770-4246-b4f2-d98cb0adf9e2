#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Proteus ViT-B测试脚本 - 仿照test_scsa.py格式
测试ViT-Base模型的玻璃检测性能
"""

import os
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import *
from ig_glass.gdnet_proteus_vits import create_proteus_vits_model

# Device setup
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# Paths
backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/proteus_vitb_backbone.pth'

# Parameter set
ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM'
exp_name = 'IG-SLAM-ProteusViTB'
args = {
    'snapshot': 'vitb-msd-100',  # Use 'best' to load best model, or specify epoch like 'best_iou-0.9544_mae-0.0098_epoch-100'
    'scale': 420,  # Must be divisible by 14 for ViT-B patch size
    'crf': False,  # CRF post-processing
    'glass_threshold': 0.5,  # Threshold for glass region detection
    'crf_iter': 3,  # Number of CRF iterations
    'vit_type': 'vitb',  # ViT type: vitb for ViT-Base
    'trainable_crf': False,  # CRF parameters trainable or not
    # CRF parameters
    'bilateral_weight': 10.0,
    'gaussian_weight': 5.0, 
    'bilateral_spatial_sigma': 40.0,
    'bilateral_color_sigma': 3.0,
    'gaussian_sigma': 1.5,
}

# 预处理 - 适配ViT输入要求
img_transform = transforms.Compose([
    transforms.Resize((args['scale'], args['scale'])),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

to_pil = transforms.ToPILImage()


def path_set(tempdata_path, new_path):
    results_root = os.path.join(tempdata_path, new_path)
    return results_root


def detect_glass_and_evaluate(image_folder, output_folder, gt_folder, model, glass_threshold):
    """检测玻璃区域并评估结果"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]

    count = 0
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0

    # 用于性能测量
    inference_times = []

    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)

        # 读取图像
        img_pil = Image.open(image_path)
        if img_pil.mode != 'RGB':
            img_pil = img_pil.convert('RGB')
        w, h = img_pil.size

        # 预测
        img_var = Variable(img_transform(img_pil).unsqueeze(0)).cuda(device_ids[0])

        # 测量推理时间
        start_time = torch.cuda.Event(enable_timing=True)
        end_time = torch.cuda.Event(enable_timing=True)

        start_time.record()
        with torch.no_grad():
            outputs = model(img_var)  # ViT-B模型输出
            # 获取集成预测结果
            if isinstance(outputs, dict):
                if 'ensemble_pred' in outputs:
                    refined_pred = outputs['ensemble_pred']
                elif 'crf_pred' in outputs:
                    refined_pred = outputs['crf_pred']
                else:
                    # 如果没有CRF，使用基础预测
                    refined_pred = outputs.get('pred', list(outputs.values())[0])
            else:
                refined_pred = outputs
        end_time.record()

        # 等待GPU操作完成
        torch.cuda.synchronize()
        inference_time = start_time.elapsed_time(end_time) / 1000.0  # 转换为秒
        inference_times.append(inference_time)

        # 处理预测结果
        if refined_pred.dim() == 4 and refined_pred.size(1) > 1:
            # 如果是多通道输出，取前景通道
            if refined_pred.size(1) == 2:
                predict = refined_pred[:, 1:2]
            else:
                predict = refined_pred[:, 0:1]
        else:
            predict = refined_pred

        # 转换预测结果
        predict = predict.data.squeeze(0).cpu()
        predict_np = np.array(transforms.Resize((h, w))(to_pil(predict)))

        # 二值化预测结果
        glass_mask = (predict_np > glass_threshold * 255).astype(np.uint8) * 255

        # 保存结果
        temp_name = image_file.split('.')[0]
        if '.png' in image_file:
            result_name = image_file
        else:
            result_name = temp_name + '.png'
        cv2.imwrite(os.path.join(output_folder, result_name), glass_mask)

        # 评估结果
        if '.png' in image_file:
            gt_name = image_file
        else:
            gt_name = temp_name + '.png'
        gt_path = os.path.join(gt_folder, gt_name)

        if os.path.exists(gt_path):
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)

            # 确保尺寸一致
            if gt_mask.shape != glass_mask.shape:
                gt_mask = cv2.resize(gt_mask, (glass_mask.shape[1], glass_mask.shape[0]))

            # 计算评估指标
            prediction = glass_mask.astype(np.float32) / 255.0
            gt = gt_mask.astype(np.float32) / 255.0

            tiou = compute_iou(prediction, gt)
            tacc = compute_acc(prediction, gt)
            precision, recall = compute_precision_recall(prediction, gt)
            tfm = compute_fmeasure(precision, recall)
            tmae = compute_mae(prediction, gt)
            tber = compute_ber(prediction, gt)
            taber = compute_aber(prediction, gt)

            count += 1
            iou += tiou
            acc += tacc
            fm += tfm
            mae += tmae
            ber += tber
            aber += taber

    if count > 0:
        iou = iou / count
        acc = acc / count
        fm = fm / count
        mae = mae / count
        ber = ber / count
        aber = aber / count

    # 计算平均推理时间（跳过前几次作为预热）
    if len(inference_times) > 5:
        avg_inference_time = np.mean(inference_times[5:])
        fps = 1.0 / avg_inference_time
        print(f"\nAverage inference time: {avg_inference_time*1000:.2f} ms")
        print(f"FPS: {fps:.2f}")

    return iou, acc, fm, mae, ber, aber


def main():
    print('🌟 Proteus ViT-B玻璃检测测试系统')
    print('=' * 60)
    
    # 加载模型
    print(f'🚀 创建Proteus ViT-{args["vit_type"].upper()}模型...')
    model = create_proteus_vits_model(
        backbone_path=backbone_path,
        crf_iter=args['crf_iter'],
        trainable_crf=args['trainable_crf'],
        vit_type=args['vit_type']
    )

    # 设置CRF参数（如果模型有CRF模块）
    if hasattr(model, 'crf') and hasattr(model.crf, 'bilateral_weight'):
        model.crf.bilateral_weight = torch.nn.Parameter(torch.tensor(args['bilateral_weight']))
        model.crf.gaussian_weight = torch.nn.Parameter(torch.tensor(args['gaussian_weight']))
        model.crf.bilateral_spatial_sigma = torch.nn.Parameter(torch.tensor(args['bilateral_spatial_sigma']))
        model.crf.bilateral_color_sigma = torch.nn.Parameter(torch.tensor(args['bilateral_color_sigma']))
        model.crf.gaussian_sigma = torch.nn.Parameter(torch.tensor(args['gaussian_sigma']))

    # 加载模型权重
    if args['snapshot'] == 'best':
        # 查找最新的best模型文件
        model_files = [f for f in os.listdir(ckpt_path) if f.startswith('best') and f.endswith('.pth')]
        if model_files:
            # 优先选择best.pth，否则选择最新的best_iou文件
            if 'best.pth' in model_files:
                model_path = os.path.join(ckpt_path, 'best.pth')
            else:
                model_path = os.path.join(ckpt_path, sorted(model_files)[-1])
            print(f"📦 加载最佳模型: {os.path.basename(model_path)}")
            checkpoint = torch.load(model_path, map_location='cpu')
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ 模型权重加载成功! 训练轮次: {checkpoint.get('epoch', '未知')}")
                print(f"   最佳IoU: {checkpoint.get('best_iou', '未知'):.4f}")
                print(f"   最佳MAE: {checkpoint.get('best_mae', '未知'):.4f}")
            else:
                model.load_state_dict(checkpoint)
        else:
            print("❌ 未找到best模型文件，请检查路径!")
            return
    else:
        # 加载指定的模型
        print(f'📦 加载指定模型: {args["snapshot"]}')
        if not args['snapshot'].endswith('.pth'):
            model_name = args['snapshot'] + '.pth'
        else:
            model_name = args['snapshot']
        
        model_path = os.path.join(ckpt_path, model_name)
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return
            
        checkpoint = torch.load(model_path, map_location='cpu')
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        print(f'✅ 模型 {model_name} 加载成功!')

    model.cuda(device_ids[0])
    model.eval()

    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    print(f'📊 模型参数量: {total_params:,} ({total_params/1e6:.1f}M)')
    print(f'🖼️  输入图像尺寸: {args["scale"]}x{args["scale"]}')
    print(f'🎯 检测阈值: {args["glass_threshold"]}')
    print('=' * 60)

    # 设置路径
    #data_path = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/test_msd"  # 测试数据路径
    data_path = "/home/<USER>/Documents/ig_slam_maskdata/test_GDD"  # 测试数据路径
    current = "glass_mask_proteus_vitb"  # 结果文件夹名称

    # 设置输入输出路径
    image_folder = path_set(data_path, "image")  # 输入图像文件夹
    output_folder = path_set(data_path, current)  # 输出结果文件夹
    gt_folder = path_set(data_path, "mask")  # 真值文件夹

    print(f'📁 图像路径: {image_folder}')
    print(f'📁 输出路径: {output_folder}')
    print(f'📁 真值路径: {gt_folder}')
    print()

    # 检测玻璃区域并评估结果
    print('🔍 开始玻璃检测和评估...')
    iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate(
        image_folder, output_folder, gt_folder, model, args['glass_threshold']
    )

    # 打印评估结果
    print("\n" + "="*60)
    print("🎉 Proteus ViT-B评估结果:")
    print("="*60)
    print(f"📈 IoU (交并比):     {iou:.4f}")
    print(f"🎯 Accuracy (准确率): {acc:.4f}")
    print(f"🔥 F-measure (F值):  {fm:.4f}")
    print(f"📉 MAE (平均绝对误差): {mae:.4f}")
    print(f"⚠️  BER (平衡误差率):  {ber:.4f}")
    print(f"💫 ABER (自适应BER):  {aber:.4f}")
    print("="*60)

    # 性能对比提示
    print("🏆 性能目标:")
    print("   IoU > 0.95   (当前: {:.4f})".format(iou))
    print("   MAE < 0.02   (当前: {:.4f})".format(mae))
    print("   FPS > 50     (实时性要求)")
    
    if iou > 0.95:
        print("🎊 恭喜! IoU超过95%，达到优秀水平!")
    elif iou > 0.90:
        print("👏 很好! IoU超过90%，性能良好!")
    else:
        print("💪 继续优化，向95%+ IoU目标努力!")

    return iou, acc, fm, mae, ber, aber


if __name__ == '__main__':
    main() 