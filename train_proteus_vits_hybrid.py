#!/usr/bin/env python3
"""
 @Time    : 2024
 <AUTHOR> Glennine

 @Project : IG_SLAM
 @File    : train_proteus_vits_hybrid.py
 @Function: 融合SCSA和Glass Physics优势的Proteus ViT-S训练脚本

核心优势：
- Proteus ViT-S: 32.2M参数，强大泛化能力
- SCSA LightLCFI: 87.9% IoU验证的轻量化特征集成
- SCSA注意力: 空间-通道联合注意力机制
- Physics边缘分析: 拉普拉斯+Sobel+HSV引导
- 混合损失策略: BCE(0.1)+IoU(0.7)+Edge(0.2)+Physics(0.1)
- 动态权重调整: 自适应损失平衡
- SCSA CRF参数: 87.9% IoU验证的最优配置
"""

import os
import sys
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm
import argparse

# 添加路径
sys.path.append('ig_glass')
from gdnet_proteus_vits import create_proteus_vits_model
from loss_proteus_vits import create_proteus_vits_loss
from glass_dataloader import GlassDataLoader, GlassInferenceLoader


class ProteusViTSHybridTrainer:
    """融合SCSA和Glass Physics优势的Proteus ViT-S训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建保存目录
        self.model_dir = f'ig_glass/models_proteus_vits_hybrid/{args.model_name}'
        self.weights_dir = os.path.join(self.model_dir, 'weights')
        self.optimizers_dir = os.path.join(self.model_dir, 'optimizers')
        os.makedirs(self.weights_dir, exist_ok=True)
        os.makedirs(self.optimizers_dir, exist_ok=True)
        
        # TensorBoard
        self.writer = SummaryWriter(f'runs/proteus_vits_hybrid_{args.model_name}')
        
        # 初始化模型
        self._init_model()
        
        # 初始化数据加载器
        self._init_dataloaders()
        
        # 初始化优化器和调度器
        self._init_optimizer()
        
        # 训练状态
        self.start_epoch = 0
        self.best_iou = 0.0
        self.best_mae = float('inf')
        
        print(f"🚀 Proteus ViT-S Hybrid训练器初始化完成")
        print(f"   模型: {args.model_name}")
        print(f"   设备: {self.device}")
        print(f"   参数: {sum(p.numel() for p in self.model.parameters())/1e6:.1f}M")
        
    def _init_model(self):
        """初始化模型"""
        self.model = create_proteus_vits_model(
            backbone_path=self.args.backbone_path,
            crf_iter=self.args.crf_iter,
            trainable_crf=True
        ).to(self.device)
        
        # 损失函数 - 使用SCSA和Glass Physics的混合策略
        self.criterion = create_proteus_vits_loss(
            bce_weight=0.1,      # SCSA验证的最优权重
            iou_weight=0.7,      # 87.9% IoU的核心
            edge_weight=0.2,     # 边缘质量
            physics_weight=0.1,  # 物理约束
            consistency_weight=0.05
        )
        
    def _init_dataloaders(self):
        """初始化数据加载器"""
        # Glass感知数据加载器 - 提升分辨率和物理特性
        train_dataset = GlassDataLoader(
            mode='train',
            augment_data=True,
            target_size=self.args.trainsize,
            glass_augmentation='strong'
        )
        
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        # 验证数据加载器
        val_dataset = GlassDataLoader(
            mode='test',
            augment_data=False,
            target_size=self.args.trainsize,
            glass_augmentation='none'
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            pin_memory=True
        )
            
        print(f"📊 数据加载器:")
        print(f"   训练样本: {len(train_dataset)}")
        print(f"   验证样本: {len(val_dataset)}")
        print(f"   批次大小: {self.args.batch_size}")
        print(f"   图像尺寸: {self.args.trainsize}x{self.args.trainsize}")
        
    def _init_optimizer(self):
        """初始化优化器和调度器"""
        # 分层学习率 - ViT backbone使用较小学习率
        vit_params = []
        other_params = []
        
        for name, param in self.model.named_parameters():
            if 'vit_backbone' in name:
                vit_params.append(param)
            else:
                other_params.append(param)
        
        # AdamW优化器 - 适合ViT训练
        self.optimizer = optim.AdamW([
            {'params': vit_params, 'lr': self.args.lr * 0.1, 'weight_decay': self.args.weight_decay},
            {'params': other_params, 'lr': self.args.lr, 'weight_decay': self.args.weight_decay}
        ], lr=self.args.lr, weight_decay=self.args.weight_decay)
        
        # Cosine学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, 
            T_max=self.args.epochs,
            eta_min=self.args.lr * 0.01
        )
        
        # 学习率预热
        self.warmup_epochs = 5
        self.warmup_scheduler = optim.lr_scheduler.LinearLR(
            self.optimizer,
            start_factor=0.1,
            total_iters=self.warmup_epochs
        )
        
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        loss_components = {'bce': 0.0, 'iou': 0.0, 'edge': 0.0, 'physics': 0.0, 'consistency': 0.0}
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.args.epochs}')
        
        for i, (images, masks) in enumerate(pbar):
            images = images.to(self.device)
            masks = masks.to(self.device)
            
            # 前向传播
            outputs = self.model(images)
            
            # 计算损失
            loss, loss_dict = self.criterion(outputs, masks, images)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 统计损失
            total_loss += loss.item()
            for key in loss_components:
                if f'{key}_loss' in loss_dict:
                    loss_components[key] += loss_dict[f'{key}_loss'].item()
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'IoU': f'{loss_dict["iou_loss"].item():.4f}',
                'BCE': f'{loss_dict["bce_loss"].item():.4f}',
                'Edge': f'{loss_dict["edge_loss"].item():.4f}',
                'Physics': f'{loss_dict["physics_loss"].item():.4f}'
            })
            
            # 记录到TensorBoard
            step = epoch * len(self.train_loader) + i
            self.writer.add_scalar('Train/Loss', loss.item(), step)
            for key, value in loss_dict.items():
                if key != 'dynamic_weights':
                    self.writer.add_scalar(f'Train/{key}', value.item(), step)
        
        # 学习率调度
        if epoch < self.warmup_epochs:
            self.warmup_scheduler.step()
        else:
            self.scheduler.step()
        
        # 平均损失
        avg_loss = total_loss / len(self.train_loader)
        for key in loss_components:
            loss_components[key] /= len(self.train_loader)
        
        return avg_loss, loss_components
    
    def validate(self, epoch):
        """验证"""
        if self.val_loader is None:
            return None, None
        
        self.model.eval()
        total_loss = 0.0
        total_iou = 0.0
        total_mae = 0.0
        
        with torch.no_grad():
            for images, masks in tqdm(self.val_loader, desc='Validating'):
                images = images.to(self.device)
                masks = masks.to(self.device)
                
                # 前向传播
                outputs = self.model(images)
                
                # 计算损失
                loss, loss_dict = self.criterion(outputs, masks, images)
                total_loss += loss.item()
                
                # 计算IoU和MAE
                pred = torch.sigmoid(outputs['ensemble_pred'])
                iou = self._calculate_iou(pred, masks)
                mae = self._calculate_mae(pred, masks)
                
                total_iou += iou
                total_mae += mae
        
        avg_loss = total_loss / len(self.val_loader)
        avg_iou = total_iou / len(self.val_loader)
        avg_mae = total_mae / len(self.val_loader)
        
        # 记录到TensorBoard
        self.writer.add_scalar('Val/Loss', avg_loss, epoch)
        self.writer.add_scalar('Val/IoU', avg_iou, epoch)
        self.writer.add_scalar('Val/MAE', avg_mae, epoch)
        
        return avg_iou, avg_mae
    
    def _calculate_iou(self, pred, target):
        """计算IoU"""
        pred_binary = (pred > 0.5).float()
        target_binary = (target > 0.5).float()
        
        intersection = (pred_binary * target_binary).sum()
        union = pred_binary.sum() + target_binary.sum() - intersection
        
        if union == 0:
            return 1.0
        return (intersection / union).item()
    
    def _calculate_mae(self, pred, target):
        """计算MAE"""
        return torch.abs(pred - target).mean().item()
    
    def save_checkpoint(self, epoch, iou, mae, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_iou': self.best_iou,
            'best_mae': self.best_mae,
            'args': self.args
        }
        
        # 保存最新检查点
        torch.save(checkpoint, os.path.join(self.weights_dir, 'latest.pth'))
        
        # 保存最佳模型
        if is_best:
            torch.save(checkpoint, os.path.join(self.weights_dir, f'best_iou-{iou:.4f}_mae-{mae:.4f}_epoch-{epoch}.pth'))
            print(f"✅ 保存最佳模型: IoU={iou:.4f}, MAE={mae:.4f}")
        
        # 定期保存
        if (epoch + 1) % 20 == 0:
            torch.save(checkpoint, os.path.join(self.weights_dir, f'epoch_{epoch+1}.pth'))
    
    def load_checkpoint(self, checkpoint_path):
        """加载检查点"""
        if os.path.exists(checkpoint_path):
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            self.start_epoch = checkpoint['epoch'] + 1
            self.best_iou = checkpoint['best_iou']
            self.best_mae = checkpoint['best_mae']
            print(f"✅ 加载检查点: epoch={self.start_epoch}, best_iou={self.best_iou:.4f}")
            return True
        return False
    
    def train(self):
        """主训练循环"""
        print(f"\n🚀 开始训练 Proteus ViT-S Hybrid")
        print(f"   目标: 超越SCSA的87.9% IoU")
        print(f"   策略: SCSA + Glass Physics 混合优势")
        print("="*60)
        
        # 尝试加载检查点
        latest_checkpoint = os.path.join(self.weights_dir, 'latest.pth')
        self.load_checkpoint(latest_checkpoint)
        
        for epoch in range(self.start_epoch, self.args.epochs):
            start_time = time.time()
            
            # 训练
            train_loss, loss_components = self.train_epoch(epoch)
            
            # 验证
            val_iou, val_mae = self.validate(epoch)
            
            # 记录学习率
            current_lr = self.optimizer.param_groups[0]['lr']
            self.writer.add_scalar('Train/LearningRate', current_lr, epoch)
            
            # 打印统计信息
            epoch_time = time.time() - start_time
            print(f"\nEpoch {epoch+1}/{self.args.epochs} | Time: {epoch_time:.1f}s")
            print(f"Train Loss: {train_loss:.4f} | LR: {current_lr:.6f}")
            print(f"Loss Components - IoU: {loss_components['iou']:.4f}, BCE: {loss_components['bce']:.4f}, Edge: {loss_components['edge']:.4f}")
            
            if val_iou is not None:
                print(f"Val IoU: {val_iou:.4f} | Val MAE: {val_mae:.4f}")
                
                # 检查是否为最佳模型
                is_best = val_iou > self.best_iou
                if is_best:
                    self.best_iou = val_iou
                    self.best_mae = val_mae
                
                # 保存检查点
                self.save_checkpoint(epoch, val_iou, val_mae, is_best)
            else:
                # 没有验证集时，定期保存
                self.save_checkpoint(epoch, 0.0, 0.0, False)
            
            print("-" * 60)
        
        print(f"\n🎉 训练完成!")
        print(f"   最佳IoU: {self.best_iou:.4f}")
        print(f"   最佳MAE: {self.best_mae:.4f}")
        print(f"   模型保存在: {self.weights_dir}")
        
        self.writer.close()


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Proteus ViT-S Hybrid Training')
    
    # 模型参数
    parser.add_argument('--model_name', type=str, default='ProteusViTS-SCSA-Physics',
                       help='模型名称')
    parser.add_argument('--backbone_path', type=str, 
                       default='/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/proteus_vits_backbone.pth',
                       help='Proteus ViT-S预训练权重路径')
    parser.add_argument('--crf_iter', type=int, default=3,
                       help='CRF迭代次数')
    
    # 数据参数
    parser.add_argument('--train_images', type=str, 
                       default='/home/<USER>/Documents/ig_slam_maskdata/train_GDD/image',
                       help='训练图像路径')
    parser.add_argument('--train_masks', type=str,
                       default='/home/<USER>/Documents/ig_slam_maskdata/train_GDD/mask',
                       help='训练掩码路径')
    parser.add_argument('--val_images', type=str,
                       default='/home/<USER>/Documents/ig_slam_maskdata/test_GDD/image',
                       help='验证图像路径')
    parser.add_argument('--val_masks', type=str,
                       default='/home/<USER>/Documents/ig_slam_maskdata/test_GDD/mask',
                       help='验证掩码路径')
    parser.add_argument('--trainsize', type=int, default=416,
                       help='训练图像尺寸')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=150,
                       help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=6,
                       help='批次大小')
    parser.add_argument('--lr', type=float, default=0.0005,
                       help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.0005,
                       help='权重衰减')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='数据加载器工作进程数')
    
    return parser.parse_args()


if __name__ == '__main__':
    args = parse_args()
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 创建训练器
    trainer = ProteusViTSHybridTrainer(args)
    
    # 开始训练
    trainer.train() 